# Project Title
MyTAB - It is an mobile application in both Android and iOS devices. An seperate iOS app for venue has also been maintained by us. This mobile application allow users to place order for food online. User can also choose table service option after placing the order so they might not have to wait. 

## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes. See deployment for notes on how to deploy the project on a live system.

In you workspace first of all clone the project. You can do it in different ways. the best way to do it is to clone it by command in your workspace's terminal.

Command to Clone the Http of the project is below : 
  -- git clone https://gitlab.openxcell.dev/a-team/mytab/api.git

Checkout to a branch you want to work on by :
  -- git checkout <branch_name>
    Check the status of branch if you're successfully switched by:
      -- git branch      

If you're starting with a new feature, maintain it in a existing manner : Like make a new branch namely (Environment Name - feature Name)
   For example if you create a new branch for bugs solve in production environment, make a new branch of name :
        - production-minorBugs

### Prerequisites

Install the node_modules.

```
Hit the below command in Terminal:
-> npm i
or
-> npm install
```

### Installing

-> After getting the project in your local pull a branch that you'll be working on from git.
```
eg : git pull origin master
```

-> Under config directly, you'll find a environment.js file. From there change the database to local database and you're good to run the project.
```
eg :  Change databaseName: process.env.databaseName || 'mytab' to  databaseName: process.env.databaseName || 'mytabLocal'.
```

-> Now run the project by entering the run command for node projects.
```
npm run start
```
The project will be running fine if everything is perfect.

## Environment Variables
Some of the important and most used variables in the environment file are mentioned below.

|   VARIABLE NAME  |                     DEFAULT VALUE                     |                DESCRIPTION               |
|:----------------:|:-----------------------------------------------------:|:---------------------------------------- 
| port             | 8522                                                  | the port on which database  is runnning. |
| databaseName     | 'mytab'                                               | Database used to run the system.         |
| databaseHost     | 'mytab.c47q3b0zfr0p.ap-southeast-2.rds.amazonaws.com' | Database Host.                           |
| databaseUserName | 'root'                                                | Username of the database used.           |
| databasePassword | '*****'                                               | Password of the database used.           |
| databaseDialect  | 'mysql'                                               | The database dialect used.               |

## Changing And Pushing The Code To Git

After doing the changes in a local branch, commit the changes after checking and running it on local to avoid errors on production. After ensuring everything is fine stage the changes. 

You can stage the changes by :
  -- git add <directory_name> (Stage all changes in directory for the next commit.)

After staging all the changes, commit the changes for push. The command to commit the changes: 
  -- git commit -am "commit message" (A power user shortcut command that combines the -a and -m options. This combination immediately creates a commit of all the staged changes and takes an inline commit message.)

After commiting the changes, we are ready to push the changes. 
   -- git push origin master
      (Since we already made sure the local main was up-to-date, this should result in a fast-forward merge, and git push should not complain about any of the non-fast-forward issues discussed above.)

## Built With

* [NodeJS](https://nodejs.org/en/docs/) - The web framework used
* (https://nodejs.org/en/docs/meta/topics/dependencies/) - Dependency Management

## Contributing

Code of Conduct:- 

Our Pledge
In the interest of fostering an open and welcoming environment, we as contributors and maintainers pledge to making participation in our project and our community a harassment-free experience for everyone, regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

Our Standards
Examples of behavior that contributes to creating a positive environment include:

Using welcoming and inclusive language
Being respectful of differing viewpoints and experiences
Gracefully accepting constructive criticism
Focusing on what is best for the community
Showing empathy towards other community members
Examples of unacceptable behavior by participants include:

The use of sexualized language or imagery and unwelcome sexual attention or advances
Trolling, insulting/derogatory comments, and personal or political attacks
Public or private harassment
Publishing others' private information, such as a physical or electronic address, without explicit permission
Other conduct which could reasonably be considered inappropriate in a professional setting
Our Responsibilities
Project maintainers are responsible for clarifying the standards of acceptable behavior and are expected to take appropriate and fair corrective action in response to any instances of unacceptable behavior.

Project maintainers have the right and responsibility to remove, edit, or reject comments, commits, code, wiki edits, issues, and other contributions that are not aligned to this Code of Conduct, or to ban temporarily or permanently any contributor for other behaviors that they deem inappropriate, threatening, offensive, or harmful.

Scope
This Code of Conduct applies both within project spaces and in public spaces when an individual is representing the project or its community. Examples of representing a project or community include using an official project e-mail address, posting via an official social media account, or acting as an appointed representative at an online or offline event. Representation of a project may be further defined and clarified by project maintainers.

Enforcement
Instances of abusive, harassing, or otherwise unacceptable behavior may be reported by contacting the project team. All complaints will be reviewed and investigated and will result in a response that is deemed necessary and appropriate to the circumstances. The project team is obligated to maintain confidentiality with regard to the reporter of an incident. Further details of specific enforcement policies may be posted separately.

Project maintainers who do not follow or enforce the Code of Conduct in good faith may face temporary or permanent repercussions as determined by other members of the project's leadership.

## Versioning

NA 

## Authors

* **zeus-py** - *Initial work* - [MyTAB](https://gitlab.openxcell.dev/a-team/mytab/api)

See also the list of [contributors](https://gitlab.openxcell.dev/a-team/mytab/api/-/graphs/master) who participated in this project.

## License

NA

## Acknowledgments

TEAM WORKING ON THE PROJECT :

Project Manager/ Reporting Manager - Ketan Patel
IOS Developer                      - Umangi Sheth
Android Developer                  - Aniket Chauhan
Backend Developer                  - Viraj Shah
React Developer                    - Nirad Lalani
QA                                 - Janki Joshi