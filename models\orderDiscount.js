var Sequelize = require('sequelize')
const orders = require('./orders')

var orderDiscount = sequelize.define(
  'order_discount',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true,
    },
    orderID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'order',
        key: 'id'
      }
    },
    discountID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'discount',
        key: 'id'
      }
    },
    userID: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    discountCode: {
      type: Sequelize.STRING(255),
      allowNull: false,
    },
    discountType: {
      type: Sequelize.ENUM('percentage', 'fixed'),
      allowNull: false,
    },
    type: {
      type: Sequelize.ENUM('manual', 'automatic'),
      allowNull: false,
    },
    discountValue: {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    },
    discountAmount: {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    },
    createdAt: { type: Sequelize.DATE, allowNull: false },
    updatedAt: { type: Sequelize.DATE },

  },
  {
    freezeTableName: true,
    timestamps: false
  }
)
orderDiscount.belongsTo(orders, { foreignKey: 'orderID' })
orders.hasMany(orderDiscount, { foreignKey: 'orderID' })
module.exports = orderDiscount
