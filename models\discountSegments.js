const Sequelize = require('sequelize');
const bar = require('./bar');
const segment = require('./segment');
const discount = require('./discount');


const discountSegments = sequelize.define(
	'discount_segments',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		segmentID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'segment',
				key: 'id'
			}
		},
		discountID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'discount',
				key: 'id'
			}
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'bar',
				key: 'id'
			}
		}
	},
	{
		timestamps: false,
		freezeTableName: true,
		underscored: false,
	}
)

segment.hasMany(discountSegments, {
	foreignKey: 'segmentID'
});
discountSegments.belongsTo(segment, {
	foreignKey: 'segmentID'
});

discount.hasMany(discountSegments, {
	foreignKey: 'discountID'
});
discountSegments.belongsTo(discount, {
	foreignKey: 'discountID'
});

bar.hasMany(discountSegments, {
	foreignKey: 'barID'
});
discountSegments.belongsTo(bar, {
	foreignKey: 'barID'
});


module.exports = discountSegments