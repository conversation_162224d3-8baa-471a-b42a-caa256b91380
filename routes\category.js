const express = require('express')
const router = express()
const multer = require('multer')

const checkAuth = require('../middleware/barCheckAuth')
const checkUserAuth = require('../middleware/checkAuth')
const category = require('../controller/category')

var upload = multer({})

router.post('/getCategory', upload.array(), checkAuth, category.getCategory)
router.post('/getSubCategory', upload.array(), checkAuth, category.getSubCategory)

/*User API*/
router.post('/getCategoryList', upload.array(), checkUserAuth, category.getCategoryList)

// bar sequence
router.post('/barProductSequence', upload.array(), checkAuth, category.barSubCategorySequence)

router.post('/getSubCategoryList', upload.array(), checkAuth, category.getSubCategoryList)

module.exports = router