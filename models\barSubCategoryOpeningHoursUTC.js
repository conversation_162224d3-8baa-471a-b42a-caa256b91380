const Sequelize = require('sequelize')
const bar = require('./bar')
const barSubCategoryOpeningHours = require('./barSubCategoryOpeningHours')

const barSubCategoryOpeningHoursUTC = sequelize.define(
	'bar_sub_category_opening_hours_utc',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barSubCategoryOpeningHoursID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'bar_sub_category_opening_hours',
				key: 'id'
			}
		},
		subCategoryID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'sub_category',
				key: 'id'
			}
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "bar",
				key: "id"
			}
		},
		weekDay: Sequelize.SMALLINT,
		openingHours: Sequelize.TIME,
		closingHours: Sequelize.TIME,
		isClosed: Sequelize.BOOLEAN,
		createdAt: Sequelize.DATE,
		updatedAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false
	}
)

barSubCategoryOpeningHoursUTC.belongsTo(bar, { foreignKey: 'barID' })
barSubCategoryOpeningHoursUTC.belongsTo(barSubCategoryOpeningHours, {foreignKey: 'barSubCategoryOpeningHoursID', targetKey: 'id'});

module.exports = barSubCategoryOpeningHoursUTC