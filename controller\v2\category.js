var Sequelize = require('sequelize')
const Op = Sequelize.Op
const message = require('../../config/message')
const subCategory = require('../../models/subCategory')
const barCategorySequence = require('../../models/barCategorySequence')
const bar = require('../../models/bar')
const pickupLocation = require('../../models/pickupLocation')
const pickupLocationSubCategory = require('../../models/pickupLocationSubCategory')

exports.getSubCategoryList = async (req, res) => {
  var barID = res.locals.barID;
  const isAllCategories = req.body.isAllCategories == "1";

  try {
    const barData = await bar.findOne({
      attributes: ['id', 'restaurantName', 'posStatus'],
      where: {
        id: barID
      }
    });

    let whereClause = {
      isDeleted: 'No'
    };

    if (
      barData.dataValues.posStatus &&
      barData.dataValues.posStatus === '1'
    ) {
      whereClause.categoryID = '-1';
    } else {
      whereClause[Op.not] = [{ categoryID: '-1' }];
    }

    let attributes = [
      [Sequelize.literal('sub_category.id'), 'subCategoryID'],  
      'name',
      'id',
      'categoryID',
      [Sequelize.col('pickup_location_sub_category->pickup_location.id'), 'pickupLocationId'],
      [Sequelize.col('pickup_location_sub_category->pickup_location.address'), 'pickupLocationAddress'],
    ];

    if (!isAllCategories) {
      attributes = [
        'id',
        'categoryID',
        'name',
        [
          Sequelize.literal(`(
                    SELECT COUNT(product.id) 
                    FROM product 
                    WHERE product.subCategoryID = sub_category.id 
                    AND product.isDeleted = "No" 
                    AND product.barID = ${barID}
                )`),
          'productCount'
        ],
        [Sequelize.col('pickup_location_sub_category->pickup_location.id'), 'pickupLocationId'],
        [Sequelize.col('pickup_location_sub_category->pickup_location.address'), 'pickupLocationAddress'],
      ];

      whereClause[Op.and] = [
        Sequelize.literal(`(
          SELECT COUNT(product.id) 
          FROM product 
          WHERE product.subCategoryID = sub_category.id 
          AND product.isDeleted = "No" 
          AND product.barID = ${barID}
        ) > 0`)
      ];
    }

    let categories = await subCategory.findAll({
      attributes,
      include: [
        {
          model: barCategorySequence,
          as: 'bar_category_sequence',
          required: false,
          attributes: [
            [
              Sequelize.fn(
                'coalesce',
                Sequelize.col('subCategorySequence'),
                1000000000000
              ),
              'subCategorySequence'
            ]
          ],
          where: { barId: barID }
        },
        {
          model: pickupLocationSubCategory,
          attributes:[],
          where: {
            barID
          },
          include: [
            {
              model: pickupLocation,
              attributes: []
            }
          ]
        },
      ],
      order: [
        [Sequelize.literal('`bar_category_sequence.subCategorySequence`')],
        'id'
      ],
      where: whereClause
    });


    res.status(200).send({
      success: 1,
      data: categories,
      message: message.category.listFetched
    })

  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}