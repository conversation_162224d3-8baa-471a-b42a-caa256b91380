var Sequelize = require('sequelize')
var orders = require('./orders')
var product = require('./product')

var order_items = sequelize.define(
  'order_items',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orders",
        key: "id"
      }
    },
    productID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product",
        key: "id"
      }
    },
    price: Sequelize.FLOAT,
    chargeAmount: Sequelize.FLOAT,
    discountedAmount: Sequelize.FLOAT,
    quantity: Sequelize.INTEGER,
    specialRequest: Sequelize.TEXT,
    isCanceled: Sequelize.ENUM('Yes', 'No'),
    refundedQuantity: Sequelize.INTEGER,
    refundAmount: Sequelize.FLOAT,
    newRefundAmount: Sequelize.FLOAT,
    waitTime: Sequelize.TIME,
    orderStatus: Sequelize.ENUM('New', 'Preparing', 'Pickup', 'Pickedup', 'NotPickedup', 'Intoxicated'),
    PreparingStartTime: Sequelize.DATE,
    ReadyTime: Sequelize.DATE,
    PickedupTime: Sequelize.DATE,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

order_items.belongsTo(product, { foreignKey: 'productID' })

orders.hasMany(order_items, { foreignKey: 'orderID' })
order_items.belongsTo(orders, { foreignKey: 'orderID' })

module.exports = order_items