var Sequelize = require('sequelize')

var user_temp_verification = sequelize.define(
  'user_temp_verification',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    email: Sequelize.STRING,
    otp: Sequelize.STRING,
    expiredAt: Sequelize.DATE,
    createdAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = user_temp_verification