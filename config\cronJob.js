const Sequelize = require("sequelize");
const moment = require("moment");
const cron = require("node-cron");
const Op = Sequelize.Op;
const orderItemWaitTimeNotifications = require('../models/orderItemWaitTimeNotifications')
const user = require('../models/user')
const bar = require('../models/bar')
const product = require('../models/product')
const order = require('../models/orders')
const operatingHours = require('../models/operatingHours')
const orderTableNumber = require('../models/orderTableNumber')
const userNotification = require("../models/userNotification");
const orderItems = require('../models/orderItems')
const commonFunction = require("../common/commonFunction");

var env = require('../config/environment');
const barOpeningHoursUTC = require("../models/barOpeningHoursUTC");
var stripe = require('stripe')(env.stripe_secret_key)

cron.schedule("*/5 * * * * *", function () {
  sendOrderPickupStatusNotification();
});

// every one minute
cron.schedule('*/1 * * * *', function () {
	checkRetryPOSOrders();
  changeNotPrintDocketOrderStatus();
});

// every 30 minute
cron.schedule('*/30 * * * *', function () {
  changeCloseVenueOrderStatus();
  changeNotReceivedOrderStatus();
});

cron.schedule('30 21 * * *',function() {
  setNewProductStockValue();
})

async function sendOrderPickupStatusNotification() {
  try {
    let ordersNotificationData = await orderItemWaitTimeNotifications.findAll({
      attributes:[
        'id',
        [Sequelize.literal(`(select pickupCode from orders where id = order_item_wait_time_notifications.orderID) `), 'pickupCode'],
        [Sequelize.literal(`(select orderServiceType from orders where id = order_item_wait_time_notifications.orderID) `), 'orderServiceType'],
        [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = order_item_wait_time_notifications.orderID ORDER BY id DESC LIMIT 1) `), 'tableCode'],
        [Sequelize.literal(`(select barID from orders where id = order_item_wait_time_notifications.orderID) `), 'barID'],
        'userID',
        'orderID',
        'createdAt',
        'barID',
        'pickupLocationID',
        'waitTime',
        [Sequelize.literal(`ADDTIME(createdAt, waitTime)`), 'expectedTime'],
      ],
      where: {
        isDeleted  : 'No',
        // expectedTime: {
        //   [Op.gt] : moment.now(),
        // }
      }
    });

    var orderNotificationGroupBy = []; 

    await ordersNotificationData.map(async (notification) => {
      if(orderNotificationGroupBy.length <= 0){
        orderNotificationGroupBy.push(notification);
      }else{
        let matchCount = 0; 
        orderNotificationGroupBy.map((item, index) => {
          if(item){
            if(notification.dataValues.orderServiceType === 'PICKUP'){
              if((notification.dataValues.orderID === item.orderID &&
                notification.dataValues.userID === item.userID &&
                notification.dataValues.barID === item.barID &&
                notification.dataValues.pickupLocationID === item.pickupLocationID &&
                notification.dataValues.waitTime === item.waitTime))
              {
                matchCount++
              }
            }else{
              if((notification.dataValues.orderID === item.orderID &&
                notification.dataValues.userID === item.userID &&
                notification.dataValues.barID === item.barID &&
                notification.dataValues.waitTime === item.waitTime))
              {
                matchCount++
              }
            }
          }
        })
        if(matchCount === 0){
          orderNotificationGroupBy.push(notification);
        }
      }
    })

    await orderNotificationGroupBy.map(async (notification) => {
      if (notification && notification.dataValues && notification.dataValues.expectedTime <= moment.now()) {
        
        let ordersNotificationExist = await orderItemWaitTimeNotifications.findOne({
          where: {
            id: notification.dataValues.id
          }
        });
        if(ordersNotificationExist.dataValues.id){
          if(notification.dataValues.notification_type == '1'){
            var message = notification && notification.dataValues && notification.dataValues.orderServiceType === 'PICKUP' ? 'You order ' + notification.dataValues.pickupCode + ' is ready to collect.' : 'Your order for table #' + notification.dataValues.tableCode + ' is ready.'
          }else{
            var message = notification && notification.dataValues && notification.dataValues.orderServiceType === 'PICKUP' ? 'Have you collected your order ' + notification.dataValues.pickupCode + '? Tap here to confirm.' : 'Have you received your order for table #' + notification.dataValues.tableCode + '? Tap here to confirm.'
          }
          var notification_type = 'orderReady_waiTime'
          if (message && message != '') {
            await userNotification.create({
              barID: notification.dataValues.barID,
              notification_type: notification_type,
              userID: notification.dataValues.userID,
              dataID: notification.dataValues.orderID,
              message: message,
              createdAt: new Date(),
              updatedAt: new Date()
            })
            await commonFunction.orderStatusNotificationToUser(notification.dataValues.userID, notification.dataValues.orderID, notification_type, message)
          }
          if(notification.dataValues.orderServiceType === 'PICKUP'){
            await orderItemWaitTimeNotifications.findAll({
              where: {
                orderID: notification.dataValues.orderID,
                userID: notification.dataValues.userID,
                barID: notification.dataValues.barID,
                pickupLocationID: notification.dataValues.pickupLocationID,
                waitTime: notification.dataValues.waitTime,
              }
            }).then((items) => {
              items.map(item => { 
                orderItems.update({ orderStatus: 'Pickup', ReadyTime: new Date() },{ where: { id: item.dataValues.orderItemID }})
                item.destroy();
              })
            });
          }else{
            await orderItemWaitTimeNotifications.findAll({
              where: {
                orderID: notification.dataValues.orderID,
                userID: notification.dataValues.userID,
                barID: notification.dataValues.barID,
                waitTime: notification.dataValues.waitTime,
              }
            }).then((items) => {
              items.map(item => { 
                orderItems.update({ orderStatus: 'Pickup', ReadyTime: new Date() },{ where: { id: item.dataValues.orderItemID }})
                item.destroy();
              })
            });
          }

          const TotalOrderItems = await orderItems.count({
            where:{
              orderID: notification.dataValues.orderID,
              isCanceled: 'No',
              isDeleted: 'No',
            },
          });
          const TotalReadyItems = await orderItems.count({
            where:{
              orderID: notification.dataValues.orderID,
              isDeleted: 'No',
              isCanceled: 'No',
              orderStatus: 'Pickup'
            },
          });
          if(TotalOrderItems == TotalReadyItems){
            await order.update({
              orderStatus: 'Pickup',
              ReadyTime: new Date(),
              updatedAt: new Date()
            },
            {
              where: {
                id: notification.dataValues.orderID,
              }
            })
          }
        }
      }
    })
  } catch (error) {
    console.log(error);
    throw error;
  }
}

async function checkRetryPOSOrders() {
	try {
		let barDetails = await bar.findAll({
			where: {
				posStatus: '1'
			}
		});
		barDetails.map(async (bar) => {
			const barOrders = await order.findAll({
				where: {
					posFailedCount: {
						[Op.gte]: '1',
						[Op.lt]: '3'
					},
					posOrderStatus: 'Pending'
				},
				order: [['id', 'DESC']]
			});
			barOrders.map(async (order) => {
        if(order.orderServiceType == 'PICKUP'){
          await commonFunction.createOrderinDoshii(bar.venueId, order.id, 'pickup')
        }else{
          await commonFunction.createOrderinDoshii(bar.venueId, order.id, 'table')
        }
			});
      const barOrdersFailed = await order.findAll({
				where: {
					posFailedCount: '3',
					posOrderStatus: 'Pending'
				},
				order: [['id', 'DESC']]
			});
      barOrdersFailed.map(async (order) => { 
        await order.update(
          {
            posOrderStatus: 'Rejected'
				  },
          {
            where: {
              id: order.id
            }
          }
			  );
      });
		});
	} catch (error) {
		console.log(error);
    const barOrdersFailed = await order.findAll({
      where: {
        posFailedCount: '3',
        posOrderStatus: 'Pending'
      },
      order: [['id', 'DESC']]
    });
    barOrdersFailed.map(async (order) => { 
      await order.update(
        {
          posOrderStatus: 'Rejected'
        },
        {
          where: {
            id: order.id
          }
        }
      );
    });
	}
}

async function changeCloseVenueOrderStatus() {
  try {
    const currentDateTimeUTC = moment().utc();
    const currentDay = currentDateTimeUTC.isoWeekday() - 1;
    const currentTime = currentDateTimeUTC.format("HH:mm:ss");

    const bars = await bar.findAll({
      include: [
        {
          model: barOpeningHoursUTC,
          attributes: [
            "id",
            "weekDay",
            "openingHours",
            "closingHours",
            [Sequelize.literal(`(ADDTIME(closingHours, '02:00:00'))`), "finalClosingHour"],
            "isClosed",
            "barID",
          ],
          required: false,
          where: { isClosed: 0 },
        },
      ],
      order: [[{ model: barOpeningHoursUTC }, "openingHours", "ASC"]],
      where: {
        stripeID: { [Op.ne]: null, [Op.ne]: "" },
        accountVerified: "Approved",
        status: "Active",
        isDeleted: "No",
      },
    });

    if (bars.length === 0) return;

    for (const barData of bars) {
      const barHours = barData.bar_opening_hours_utcs;
      if (!barHours || barHours.length === 0) continue;

      const todaySlots = barHours.filter((hour) => hour.weekDay === currentDay);

      if (todaySlots.length === 0) continue;

      for (let i = 0; i < todaySlots.length; i++) {
        const slot = todaySlots[i];
        
        const closingTime = moment.utc(slot.dataValues.finalClosingHour, "HH:mm:ss");
        
        if (!closingTime.isValid()) {
          continue;
        }

        if (closingTime.isBefore(currentDateTimeUTC)) {
          const pendingOrders = await order.findAll({
            attributes: ["id"],
            where: {
              barID: barData.id,
              orderStatus: { [Op.in]: ["New", "Preparing", "Pickup"] },
            },
          });
    
          if (pendingOrders.length > 0) {
            const orderIdsArr = pendingOrders.map((o) => o.id);
    
            await order.update(
              {
                orderStatus: "Pickedup",
                PickedupTime: new Date(),
                updatedAt: new Date(),
              },
              { where: { id: { [Op.in]: orderIdsArr } } }
            );
    
            await orderItems.update(
              {
                orderStatus: "Pickedup",
                PickedupTime: new Date(),
                updatedAt: new Date(),
              },
              {
                where: {
                  orderID: { [Op.in]: orderIdsArr },
                  orderStatus: { [Op.in]: ["New", "Preparing", "Pickup"] },
                },
              }
            );
    
            await orderItemWaitTimeNotifications.destroy({
              where: { orderID: { [Op.in]: orderIdsArr } },
            });
          }
        }
      }

      const previousDate = moment().utc().subtract(1, "day").format("YYYY-MM-DD");
      const previousOrders = await order.findAll({
        attributes: ["id", "createdAt"],
        where: {
          barID: barData.id,
          orderDate: { [Op.lte]: previousDate },
          orderStatus: { [Op.in]: ["New", "Preparing", "Pickup"] },
        },
      });

      if (previousOrders.length > 0) {
        const previousOrderIds = previousOrders
          .filter((order) => {
            const orderCreatedAtPlusTwoHours = moment.utc(order.createdAt).add(2, "hours");
            return orderCreatedAtPlusTwoHours.isBefore(moment().utc()) && !barHours
              .some((slot) => 
                slot.weekDay === currentDay && 
                orderCreatedAtPlusTwoHours.isBetween(
                  moment.utc(slot.openingHours, "HH:mm:ss"), 
                  moment.utc(slot.closingHours, "HH:mm:ss"), 
                  null, 
                  '[]'
                )
              );
          })
          .map((order) => order.id);

        if (previousOrderIds.length > 0) {
          await order.update(
            {
              orderStatus: "Pickedup",
              PickedupTime: new Date(),
              updatedAt: new Date(),
            },
            { where: { id: { [Op.in]: previousOrderIds } } }
          );

          await orderItems.update(
            {
              orderStatus: "Pickedup",
              PickedupTime: new Date(),
              updatedAt: new Date(),
            },
            {
              where: {
                orderID: { [Op.in]: previousOrderIds },
                orderStatus: { [Op.in]: ["New", "Preparing", "Pickup"] },
              },
            }
          );

          await orderItemWaitTimeNotifications.destroy({
            where: { orderID: { [Op.in]: previousOrderIds } },
          });
        }
      }
    }
  } catch (error) {
    console.error("Error in changeCloseVenueOrderStatus:", error);
  }
}

async function setNewProductStockValue() {
	try {
		const bars = await bar.findAll({
      where: {
        [Op.and]: [
          { stripeID: { [Op.ne]: null } },
          { stripeID: { [Op.ne]: "" } },
          { accountVerified: 'Approved' },
          { status: 'Active' },
          { isDeleted: 'No' },
        ]
      },
		});
		
    if(bars.length > 0) {
      bars.forEach(async element => {
        const products = await product.findAll({
          where: {
            barID: element.id,
            isDailyStockRenewal: 'Yes',
          },
        });
        if(products && products.length > 0){
          products.map(async(productItem) => {
            if(productItem.stock != productItem.dailyStockRenewal){
              console.log('productItem.id : '+productItem.id + ' Updated with stock : '+productItem.dailyStockRenewal);
              await product.update({
                stock: productItem.dailyStockRenewal,
              },
              {
                where: {
                  id: productItem.id
                },
              });
            }
          })
        }
      });
    }
	} catch (error) {
		console.log(error);
	}
}

async function changeNotReceivedOrderStatus() {
	try {
		const bars = await bar.findAll({
      where: {
        [Op.and]: [
          { stripeID: { [Op.ne]: null } },
          { stripeID: { [Op.ne]: "" } },
          { accountVerified: 'Approved' },
          { status: 'Active' },
          { isDeleted: 'No' },
        ]
      },
		});
		
    if(bars.length > 0) {
      bars.forEach(async element => {
        const today = moment(new Date()).format('YYYY-MM-DD')
        const orderIds = await order.findAll({
          attributes:[
            'id',
            'paymentIntentId',
            [Sequelize.literal(`(ADDTIME(createdAt,'00:30:00')) `), 'orderTime'],
          ],
          where: {
            barID: element.id,
            paymentStatus: 'notReceived',
            orderDate: today
          },
        });
        if(orderIds && orderIds.length > 0){
          const orderIdsArr = [];
          let currentTime = moment().format('HH:mm:ss');
          orderIds.map(async(order) => {
            let orderTime = moment(order.dataValues.orderTime).format('HH:mm:ss');
            if(orderTime <= currentTime){
              orderIdsArr.push(order.id)
              if(order.paymentIntentId){
                await stripe.paymentIntents.cancel(order.paymentIntentId,{
                  stripeAccount: element.stripeID,
                });
              }
            }
          })
          if(orderIdsArr.length > 0){
            await order.update({
              paymentStatus: 'failed',
              updatedAt: new Date()
            },
            {
              where: {
                id: {
                  [Op.in] : orderIdsArr
                },
              },
            });
            // Delete wait time notification
            await orderItemWaitTimeNotifications.destroy({
              where: {
                orderID: {
                  [Op.in] : orderIdsArr
                },
              }
            });
          }
        }
      });
    }
	} catch (error) {
		console.log(error);
	}
}

async function changeNotPrintDocketOrderStatus() {
	try {
		const bars = await bar.findAll({
      where: {
        [Op.and]: [
          { stripeID: { [Op.ne]: null } },
          { stripeID: { [Op.ne]: "" } },
          { accountVerified: 'Approved' },
          { status: 'Active' },
          { isDeleted: 'No' },
        ]
      },
		});
		
    if(bars.length > 0) {
      bars.forEach(async element => {
        const today = moment(new Date()).format('YYYY-MM-DD')
        const orderIds = await order.findAll({
          attributes:[
            'id',
            'paymentIntentId',
            [Sequelize.literal(`(ADDTIME(createdAt,'00:01:00')) `), 'orderTime'],
          ],
          where: {
            barID: element.id,
            paymentStatus: 'received',
            isDocketOrder: '1',
            docketPrintingStatus: null,
            orderDate: today
          },
        });
        if(orderIds && orderIds.length > 0){
          const orderIdsArr = [];
          let currentTime = moment().format('HH:mm:ss');
          orderIds.map(async(order) => {
            let orderTime = moment(order.dataValues.orderTime).format('HH:mm:ss');
            if(orderTime <= currentTime){
              await order.update(
                {
                  docketPrintingStatus: 0,
                },
                {
                  where: {
                    id: order.id
                  }
                }
              )
            }
          })
        }
      });
    }
	} catch (error) {
		console.log(error);
	}
}