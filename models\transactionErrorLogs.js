var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')
var orders = require('./orders')

var transactionErrorLogs = sequelize.define(
  'transaction_err_logs',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    userID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "user",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    orderID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orders",
        key: "id"
      }
    },
    amout: Sequelize.FLOAT,
    log: Sequelize.TEXT,
    createdAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

transactionErrorLogs.belongsTo(user, { foreignKey: 'userID' })
transactionErrorLogs.belongsTo(bar, { foreignKey: 'barID' })
transactionErrorLogs.belongsTo(orders, { foreignKey: 'orderID' })

module.exports = transactionErrorLogs