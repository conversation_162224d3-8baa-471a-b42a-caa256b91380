const express = require('express')
const router = express()
const multer = require('multer')

const checkAuth = require('../middleware/barCheckAuth')
const checkUserAuth = require('../middleware/checkAuth')
const product = require('../controller/product')

var upload = multer({})

router.post('/getSingle', upload.array(), checkAuth, product.getSingle)
router.post('/getProductList', upload.array(), checkAuth, product.getProductList)
router.post('/productSequence', upload.array(), checkAuth, product.productSequence) /** 8. Ability to rearrange menu products under subheadings */
router.post('/updateProductImagePath', product.updateProductImagePath)
router.post('/add', checkAuth, product.add)
router.post('/edit', checkAuth, product.edit)
router.post('/updateStatus', upload.array(), checkAuth, product.updateStatus)
router.post('/delete', upload.array(), checkAuth, product.delete)
router.post('/deleteProductVariant', upload.array(), checkAuth, product.deleteProductVariant)
router.post('/deleteProductExtras', upload.array(), checkAuth, product.deleteProductExtras)
router.post('/foodOptionList', upload.none(), checkAuth, product.getProductOptionList)

/*User API*/
router.post('/getOldProducts', upload.array(), checkUserAuth, product.getOldProducts)
router.post('/getItemProfile', upload.array(), checkUserAuth, product.getItemProfile)
router.post('/getProducts', upload.array(), checkUserAuth, product.getProducts)

module.exports = router