const express = require('express')
const router = express()
const env = require('../config/environment')
var Sequelize = require('sequelize')
const Op = Sequelize.Op
const bar = require('../models/bar')
const barOpeningHoursUTC = require('../models/barOpeningHoursUTC')
const userAccessToken = require('../models/userAccessToken')
const barAccessToken = require('../models/barAccessToken')
const user = require('../models/user')
const order = require('../models/orders')
const orderItems = require('../models/orderItems')
const orderItemExtras = require('../models/orderItemExtras')
const orderItemVariants = require('../models/orderItemVariants')
const orderTax = require('../models/orderTax')
const orderRefundTax = require('../models/orderRefundTax')
const coupons = require('../models/coupons')
const orderProductVariantTypes = require('../models/orderProductVariantTypes')
const orderProductVariantSubTypes = require('../models/orderProductVariantSubTypes')
const orderTableNumber = require('../models/orderTableNumber')
const product = require('../models/product')
const productVariantTypes = require('../models/productVariantTypes')
const productVariantSubTypes = require('../models/productVariantSubTypes')
const productVariants = require('../models/productVariants')
const productExtras = require('../models/productExtras')
const pickupLocation = require('../models/pickupLocation')
const subCategory = require('../models/subCategory')
const sub_category_wait_time = require('../models/subCategoryWaitTime')
var jwt = require('jsonwebtoken');
var moment = require('moment');
const fetch = require('node-fetch');

const { GoogleAuth } = require("google-auth-library");
const axios = require("axios");
const bodyParser = require("body-parser");
const path = require("path");
const app = express();
app.use(bodyParser.json());

const PROJECT_ID = env.firebase_project_id;
const FCM_URL = `https://fcm.googleapis.com/v1/projects/${PROJECT_ID}/messages:send`;
const SERVICE_ACCOUNT_PATH = path.join(__dirname, '/../assets/' + env.fcm_file);

async function getAccessToken() {
  const auth = new GoogleAuth({
    keyFilename: SERVICE_ACCOUNT_PATH,
    scopes: ["https://www.googleapis.com/auth/firebase.messaging"],
  });
  const client = await auth.getClient();
  const accessToken = await client.getAccessToken();
  return accessToken.token;
}

module.exports = router

const options = {
  token: {
    key: __dirname + '/../assets/AuthKey_778HQ6BCC2.p8', // optionally: fs.readFileSync('./certs/key.p8')
    keyId: env.push_ios_keyid,
    teamId: env.push_ios_teamid
  },
  production: env.push_production // true for APN production environment, false for APN sandbox environment,
}
var apn = require('apn')

module.exports.orderStatusNotificationToUser = async (userID, orderID, orderStatus, message, notificationTitle = null) => {
  let notificationData = await getUserDetails(userID)
  if (notificationData) {
    let orderData = await getOrderDetails(orderID)
    if (orderData) {
      orderData = orderData.toJSON();
      notificationData = notificationData.toJSON();
      notificationData.message = message
      notificationData.title = notificationTitle ? notificationTitle : 'Order Update'
      notificationData.orderID = orderID
      notificationData.type = orderStatus
      if (orderStatus == 'orderReady_pickupAlert' || orderStatus == 'orderReady' || orderStatus == 'orderReady_waiTime') {
        notificationData.sound = 'myTab_notification_sound.wav'
      } else {
        notificationData.sound = 'default'
      }
      notificationData.orderStatus = orderData.refundStatus == "No" ? orderData.orderStatus : orderData.refundStatus

      let androidUserDeviceTokens = await getAndroidUserDeviceTokens(userID)
      if (androidUserDeviceTokens.length > 0) {
        await sendPushToAllAndroidDevices(
          androidUserDeviceTokens,
          notificationData
        )
      }
      let iosUserDeviceTokens = await getIosUserDeviceTokens(userID)
      if (iosUserDeviceTokens.length > 0) {
        await sendPushToAllFCMIosDevices(
          iosUserDeviceTokens,
          notificationData
        )
      }
    }
  }
}

module.exports.statusNotificationToUser = async (notification_title, message) => {
  let notificationData = { message: message, title: notification_title }

  let allUserDeviceTokens = await getAllUserDeviceTokens()
  if (allUserDeviceTokens.length > 0) {
    await sendPushToAllAndroidDevices(
      allUserDeviceTokens,
      notificationData
    )
    // await sendPushToAllDevices(
    //   allUserDeviceTokens,
    //   notificationData
    // )
  }
}

module.exports.newOrderNotificationToBar = async (userID, barID, orderID, notification_type, message) => {
  let notificationData = await getUserDetails(userID)
  if (notificationData) {
    let orderData = await getOrderDetails(orderID)
    if (orderData) {
      notificationData = notificationData.toJSON();
      notificationData.message = message
      notificationData.title = 'New Order'
      notificationData.orderID = orderID
      notificationData.type = notification_type
      notificationData.contentAvailable = 0

      let iosUserDeviceTokens = await getIosBarDeviceTokens(barID)
      if (iosUserDeviceTokens.length > 0) {
        await sendPushToAllFCMIosDevices(
          iosUserDeviceTokens,
          notificationData,
          env.push_ios_venue
        )
      }
    }
  }
}

module.exports.newOrderNotificationToBarCategoryWise = async (userID, barID, orderID, notification_type, message) => {
  let notificationData = await getUserDetails(userID)
  if (notificationData) {
    let orderData = await getOrderDetailsWithItems(orderID)
    if (orderData) {
      notificationData = notificationData.toJSON();
      notificationData.message = message
      notificationData.title = 'New Order'
      notificationData.orderID = orderID
      notificationData.type = notification_type
      notificationData.contentAvailable = 0

      let subCategoryIDs = []
      orderData.order_items.map((item) => {
        if (subCategoryIDs.indexOf(item.dataValues.subCategoryID) === -1) {
          subCategoryIDs.push(item.dataValues.subCategoryID)
        }
      })
      subCategoryIDs.map(async (subCategoryID) => {
        let iosUserDeviceTokens = await getIosBarDeviceTokensSubCategoryWise(barID, subCategoryID)
        if (iosUserDeviceTokens.length > 0) {
          await sendPushToAllFCMIosDevices(
            iosUserDeviceTokens,
            notificationData
          )
        }
      })
    }
  }
}

module.exports.tableChangeNotificationToBarCategoryWise = async (userID, barID, orderID, notification_type, message) => {
  let notificationData = await getUserDetails(userID)
  if (notificationData) {
    let orderData = await getOrderDetailsWithItems(orderID)
    if (orderData) {
      notificationData = notificationData.toJSON();
      notificationData.message = message
      notificationData.title = 'Table Number Changed'
      notificationData.orderID = orderID
      notificationData.type = notification_type
      notificationData.contentAvailable = 0
      notificationData.sound = "silent"
      orderData.order_items.map(async (item) => {
        let iosUserDeviceTokens = await getIosBarDeviceTokensSubCategoryWise(barID, item.dataValues.subCategoryID)
        if (iosUserDeviceTokens.length > 0) {
          await sendPushToAllFCMIosDevices(
            iosUserDeviceTokens,
            notificationData
          )
        }
      })
    }
  }
}

module.exports.docketPrintNotification = async (userID, barID, orderID, notification_type, msg) => {
  let notificationData = await getUserDetails(userID)
  if (notificationData) {
    let orderData = await getDetailedOrderForDocketPrinting(orderID)
    if (orderData) {
      notificationData = notificationData.toJSON();
      notificationData.message = msg
      notificationData.title = 'Print Docket'
      notificationData.orderID = orderID
      notificationData.type = notification_type
      notificationData.contentAvailable = 1
      notificationData.sound = 'silent'
      if (notificationData.type === 'docketPrint') {
        notificationData.contentAvailable = 0
      }

      let iosUserDeviceTokens = await getIosBarDeviceTokens(barID)
      if (iosUserDeviceTokens.length > 0) {
        await sendPushToAllFCMIosDevices(
          iosUserDeviceTokens,
          notificationData,
          true
        )
      }
    }
  }
}

module.exports.acceptRejectOrderNotificationToBar = async (userID, barID, orderID, notificationPayload) => {
  let notificationData = await getUserDetails(userID)
  if (notificationData) {
    let orderData = await getOrderDetails(orderID)
    if (orderData) {
      notificationData = notificationData.toJSON();
      notificationData.message = notificationPayload.message
      notificationData.title = notificationPayload.title
      notificationData.orderID = orderID
      notificationData.type = notificationPayload.notification_type

      let iosUserDeviceTokens = await getIosBarDeviceTokens(barID)
      if (iosUserDeviceTokens.length > 0) {
        await sendPushToAllFCMIosDevices(
          iosUserDeviceTokens,
          notificationData
        )
      }
    }
  }
}

module.exports.menuUpdateNotificationToBar = async (barID, notificationPayload) => {
  let notificationData = await getUserDetails(userID)
  if (notificationData) {
    notificationData = notificationData.toJSON();
    notificationData.message = notificationPayload.message
    notificationData.title = notificationPayload.title
    notificationData.barID = barID
    notificationData.type = notificationPayload.notification_type

    let iosUserDeviceTokens = await getIosBarDeviceTokens(barID)
    if (iosUserDeviceTokens.length > 0) {
      await sendPushToAllFCMIosDevices(
        iosUserDeviceTokens,
        notificationData
      )
    }
  }
}

async function getIosUserDeviceTokens(userID) {
  try {
    const result = await userAccessToken.findAll({
      attributes: [
        [Sequelize.literal('DISTINCT `deviceToken`'), 'deviceToken'],
        'accessToken',
        'userID',
        'deviceType'
      ],
      include: [
        {
          model: user
        }
      ],
      where: {
        userID: userID,
        deviceType: 'ios',
        deviceToken: {
          [Op.ne]: ''
        }
      },
      group: ['deviceToken']
    })
    return result
  } catch (error) {
    return error
  }
}
async function getAllUserDeviceTokens() {
  try {
    return await userAccessToken.findAll({
      attributes: [
        [Sequelize.literal('DISTINCT `deviceToken`'), 'deviceToken'],
        'accessToken',
        'userID',
        'deviceType'
      ],
      include: [
        {
          model: user
        }
      ],
      where: {
        deviceToken: {
          [Op.ne]: ''
        }
      },
      group: ['deviceToken']
    })
  } catch (error) {
    return error
  }
}

async function getAndroidUserDeviceTokens(userID) {
  try {
    const result = await userAccessToken.findAll({
      attributes: [
        [Sequelize.literal('DISTINCT `deviceToken`'), 'deviceToken'],
        'accessToken',
        'userID',
        'deviceType'
      ],
      include: [
        {
          model: user
        }
      ],
      where: {
        userID: userID,
        deviceType: 'android',
        deviceToken: {
          [Op.ne]: ''
        }
      },
      group: ['deviceToken']
    })
    return result
  } catch (error) {
    return error
  }
}

async function getIosBarDeviceTokens(barID) {
  try {
    const result = await barAccessToken.findAll({
      attributes: [
        [Sequelize.literal('DISTINCT `deviceToken`'), 'deviceToken'],
        'accessToken',
        'barID',
        'deviceType'
      ],
      include: [
        {
          model: bar
        }
      ],
      where: {
        barID: barID,
        deviceType: 'ios',
        deviceToken: {
          [Op.ne]: ''
        }
      },
      group: ['deviceToken']
    })
    return result
  } catch (error) {
    return error
  }
}

async function getIosBarDeviceTokensSubCategoryWise(barID, subCategory) {
  let whereClause = getMultipleFindInSetWhereClause([subCategory], 'subCategoryIDs', true)
  try {
    const result = await barAccessToken.findAll({
      attributes: [
        [Sequelize.literal('DISTINCT `deviceToken`'), 'deviceToken'],
        'accessToken',
        'barID',
        'deviceType'
      ],
      include: [
        {
          model: bar
        }
      ],
      where: {
        barID: barID,
        deviceType: 'ios',
        deviceToken: {
          [Op.ne]: ''
        },
        ...whereClause
      },
      group: ['deviceToken'],
    })
    return result
  } catch (error) {
    return error
  }
}

async function getUserDetails(userID) {
  try {
    const result = await user.findOne({
      attributes: [
        'id',
        'fullName',
        'email',
        'avatar',
        'notification'
      ],
      where: {
        id: userID,
        isDeleted: 'No',
        status: 'Active'
      }
    })
    return result
  } catch (error) {
    return error
  }
}

async function getAllUserDetails() {
  try {
    return await user.findAll({
      attributes: [
        'id',
        'fullName',
        'email',
        'avatar',
        'notification'
      ],
      where: {
        // id: userID,
        isDeleted: 'No',
        status: 'Active'
      }
    })
  } catch (error) {
    return error
  }
}

async function getOrderDetails(orderID) {
  try {
    const result = await order.findOne({
      attributes: [
        'id',
        'orderNo',
        'pickupCode',
        [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
        // [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
        'orderStatus',
        'refundStatus',
      ],
      where: {
        id: orderID
      }
    })
    return result
  } catch (error) {
    return error
  }
}

async function getOrderDetailsWithItems(orderID) {
  try {
    const result = await order.findOne({
      attributes: [
        'id',
        'orderNo',
        'pickupCode',
        [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
        // [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
        'orderStatus',
        'refundStatus',
      ],
      where: {
        id: orderID
      },
      include: [
        {
          attributes: [
            'id',
            'orderID',
            'productID',
            [Sequelize.literal(`(select subCategoryID from product  where product.id = order_items.productID ) `), 'subCategoryID'],
          ],
          model: orderItems,
        }
      ]
    })
    return result
  } catch (error) {
    return error
  }
}

async function getDetailedOrderForDocketPrinting(orderID) {
  try {
    // let tableCategory = [1]
    const orderAttributes = [
      'id',
      'orderNo',
      'subTotal',
      'transactionFee',
      'tax',
      'total',
      'orderDate',
      'orderStatus',
      'promocode_id',
      'promocode_amount',
      'userID',
      'barID',
      'orderServiceType',
      'createdAt'
    ]
    const itemAttributes = [
      'id',
      'orderID',
      'productID',
      'price',
      'quantity',
      'specialRequest',
      'isCanceled',
      'refundAmount',
      'refundedQuantity'
    ]
    const productAttributes = [
      'id',
      'name',
      'categoryID',
      'description',
      'avatar',
      'subCategoryID'
    ]
    const result = await order.findOne({
      where: { id: orderID, isDeleted: 'No' },
      attributes: orderAttributes,
      include: [
        {
          required: true,
          attributes: itemAttributes,
          model: orderItems,
          include: [
            {
              // where: { categoryID: tableCategory },
              attributes: productAttributes,
              model: product,
              include: [
                {
                  attributes: [
                    'id',
                    'description',
                    'address'
                  ],
                  model: pickupLocation,
                }
              ]
            },
            {
              attributes: [
                'id',
                'orderItemID',
                'productExtrasID',
                'price'
              ],
              model: orderItemExtras,
              include: [
                {
                  attributes: [
                    'id',
                    'extraItem'
                  ],
                  model: productExtras,
                }
              ]
            },
            {
              attributes: [
                'id',
                'orderItemID',
                'productVariantsID',
                'price'
              ],
              model: orderItemVariants,
              include: [
                {
                  attributes: [
                    'id',
                    'variantType'
                  ],
                  model: productVariants,
                }
              ]
            },
            {
              attributes: [
                ['id', "orderProductVariantTypeID"],
                'orderItemID',
              ],
              where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
              model: orderProductVariantTypes,
              required: false,
              include: [
                {
                  attributes: [
                    ['id', "productVariantTypeID"],
                    'label',
                  ],
                  model: productVariantTypes,
                  required: true,
                  include: [
                    {
                      attributes: [
                        ['id', "orderProductVariantSubTypeID"],
                        'orderItemID',
                      ],
                      // where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                      model: orderProductVariantSubTypes,
                      include: [
                        {
                          attributes: [
                            ['id', "productVariantSubTypeID"],
                            ['variantType', "extraItem"],
                            'price',
                          ],
                          model: productVariantSubTypes,
                        }
                      ]
                    }
                  ]
                }
              ],
            }
          ]
        },
        {
          required: true,
          model: user,
          attributes: ['id', 'fullName', 'mobile', 'email']
        },
        {
          model: coupons,
          attributes: ['id', 'code', 'name', 'description']
        },
        {
          model: orderTableNumber,
          attributes: ['tableCode']
        }
      ],
      order: [['createdAt', 'ASC']],
      distinct: true,
      duplicating: false
    })
    return result
  } catch (error) {
    return error
  }
}

async function sendPushToAllDevices(registrationIds, notificationData, deviceType = env.push_ios_BundleIDs) {
  new Promise(async (resolve, reject) => {
    try {
      var apnProvider = new apn.Provider(options)
      for (const element of registrationIds) {
        var data = notificationData
        var note = new apn.Notification()
        note.expiry = Math.floor(Date.now() / 1000) + 3600 // Expires 1 hour from now.
        // note.badge = element.user.badge + 1
        note.body = data.message
        if (notificationData.type == 'orderReady_pickupAlert' || notificationData.type == 'orderReady' || notificationData.type == 'orderReady_waiTime') {
          note.sound = 'myTab_notification_sound.wav'
        } else {
          note.sound = 'default'
        }
        note.title = data.type == 'chat' ? data.name : ''
        note.topic = deviceType
        note.mutableContent = true
        note.contentAvailable = 1
        note.aps.data = data
        if (data.type === 'docketPrint') {
          note.body = ''
          note.sound = ''
        }
        let result = await apnProvider.send(note, element.deviceToken)
        if (result.sent.length > 0) {
          try {
            // await user.update(
            //   {
            //     badge: element.user.badge + 1
            //   },
            //   {
            //     where: {
            //       userID: element.user.userID
            //     }
            //   }
            // )
          } catch (e) {
            console.log(e)
          }
        }
      }
      apnProvider.shutdown()
      resolve()
    } catch (error) {
      console.log(error)
    }
  })
}

async function sendSilentPushToAllDevices(registrationIds, notificationData, deviceType = env.push_ios_BundleIDs) {
  new Promise(async (resolve, reject) => {
    try {
      var apnProvider = new apn.Provider(options)
      for (const element of registrationIds) {
        var data = notificationData
        var note = new apn.Notification()
        note.expiry = Math.floor(Date.now() / 1000) + 3600 // Expires 1 hour from now.
        note.title = data.type == 'chat' ? data.name : ''
        note.topic = deviceType
        note.mutableContent = true
        note.contentAvailable = 1
        note.aps.data = data
        note.body = ''
        note.sound = ''
        let result = await apnProvider.send(note, element.deviceToken)
        if (result.sent.length > 0) {
          try {
            // console.log('Silent notification sent successfully');
          } catch (e) {
            console.log(e)
          }
        }
      }
      apnProvider.shutdown()
      resolve()
    } catch (error) {
      console.log(error)
    }
  })
}

async function sendPushToAllAndroidDevices(registrationIds, data) {
  try {
    const accessToken = await getAccessToken();
    const headers = {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    };

    const messages = registrationIds.map(device => ({
      message: {
        token: device.deviceToken,
        data: { content: JSON.stringify(data) },
      },
    }));

    // Send notifications one by one (FCM v1 does not support batch sends)
    const responses = await Promise.all(
      messages.map(async (messageObj, index) => {
        try {
          const res = await axios.post(FCM_URL, messageObj, { headers });
          return res.data;
        } catch (error) {
          const errData = error.response?.data || error.message;
          if (errData?.error?.status === 'NOT_FOUND') {
            console.error("Invalid device token. Consider removing:", registrationIds[index]?.deviceToken);
          }
          console.error("Error Sending Android Notification:", errData);
          return null;
        }
      })
    );

    console.log("Android Push Notifications Sent Successfully:", responses.filter(Boolean));
    // return responses.filter(Boolean);
  } catch (error) {
    console.error("Error Sending Android Notification:", error.response?.data || error.message);
    // throw error;
  }
}

async function sendPushToAllFCMIosDevices(registrationIds, data) {
  try {
    let dataToSend = JSON.parse(JSON.stringify(data))
    const accessToken = await getAccessToken();

    const headers = {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    };

    let title = "";
    if (data.type == 'chat') {
      title = data.name
    } else {
      title = ""
    }

    let messages = [];

    if (data.type != 'docketPrint' && data.type != 'tableNoChanged') {
      messages = registrationIds.map(device => ({
        message: {
          token: device.deviceToken,
          notification: {
            title: data.name,
            body: data.message,
          },
          apns: {
            payload: {
              aps: {
                sound: data.sound === "silent" ? "" : data.sound || "default",
                contentAvailable: 1,
              },
            },
          },
          data: { content: JSON.stringify(dataToSend) },
        },
      }));
    } else {
      messages = registrationIds.map(device => ({
        message: {
          token: device.deviceToken,
          notification: {
            title: title,
            body: ""
          },
          apns: {
            headers: {
              "apns-priority": "5",
              "apns-push-type": "background"
            },
            payload: {
              aps: {
                sound: "",
                contentAvailable: 1,
              },
            },
          },
          data: { content: JSON.stringify(dataToSend) }
        }
      }));
    }

    // Send notifications one by one (FCM v1 does not support batch sends)
    const responses = await Promise.all(
      messages.map(async (messageObj, index) => {
        try {
          const res = await axios.post(FCM_URL, messageObj, { headers });
          return res.data;
        } catch (error) {
          const errData = error.response?.data || error.message;
          if (errData?.error?.status === 'NOT_FOUND') {
            console.error("Invalid device token. Consider removing:", registrationIds[index]?.deviceToken);
          }
          console.error("Error Sending iOS Notification:", errData);
          return null;
        }
      })
    );
    console.log("iOS Push Notifications Sent Successfully:", responses.filter(Boolean));
  } catch (error) {
    const errData = error.response?.data || error.message;
    if (errData?.error?.status === 'NOT_FOUND') {
      console.error("Invalid device token. Consider removing:", message?.message?.token);
    }
    console.error("Error Sending iOS Notification:", errData);
  }
}

module.exports.getData = async function (url = '', headers = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await fetch(url, {
        method: 'GET',
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'same-origin',
        headers: { ...headers, 'Content-Type': 'application/json' },
        redirect: 'follow',
        referrerPolicy: 'no-referrer'
      });
      response.json().then(data => {
        if (response.status == 200 || response.status == 201) {
          resolve(data);
        }
        reject(data);
      });
    } catch (error) {
      console.log(error);
      reject(error);
    }
  });
}

module.exports.postData = async function (url = '', data = {}, headers = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await fetch(url, {
        method: 'POST',  // *GET, POST, PUT, DELETE, etc.
        mode: 'cors', // no-cors, *cors, same-origin
        cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
        credentials: 'same-origin', // include, *same-origin, omit
        headers: { ...headers, 'Content-Type': 'application/json' },
        redirect: 'follow', // manual, *follow, error
        referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
        body: JSON.stringify(data) // body data type must match "Content-Type" header
      });
      response.json().then(data => {
        if (response.status == 200 || response.status == 201) {
          resolve(data);
        }
        reject(data);
      });
    } catch (error) {
      console.log(error);
      reject(error);
    }
  });
}

module.exports.putData = async function (url = '', data = {}, headers = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await fetch(url, {
        method: 'PUT',  // *GET, POST, PUT, DELETE, etc.
        mode: 'cors', // no-cors, *cors, same-origin
        cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
        credentials: 'same-origin', // include, *same-origin, omit
        headers: headers,
        redirect: 'follow', // manual, *follow, error
        referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
        body: JSON.stringify(data) // body data type must match "Content-Type" header
      });
      response.json().then(data => {
        if (response.status == 200 || response.status == 201) {
          resolve(data);
        }
        reject(data);
      });
    } catch (error) {
      console.log(error);
      reject(error);
    }
  });
}

module.exports.deleteData = async function (url = '', headers = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await fetch(url, {
        method: 'DELETE',  // *GET, POST, PUT, DELETE, etc.
        mode: 'cors', // no-cors, *cors, same-origin
        cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
        credentials: 'same-origin', // include, *same-origin, omit
        headers: headers,
        redirect: 'follow', // manual, *follow, error
        referrerPolicy: 'no-referrer' // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
      });
      if (response.status == 200 || response.status == 201 || response.status == 204) {
        resolve();
      }
      reject();
    } catch (error) {
      console.log(error);
      reject(error);
    }
  });
}

module.exports.getDoshiAuthToken = function () {
  return new Promise((resolve, reject) => {
    jwt.sign({ clientId: env.DOSHI_APP_CLIENT_ID, timestamp: moment().unix() }, env.DOSHI_APP_CLIENT_SECRET, (err, token) => {
      if (err) resolve('');
      resolve(token);
    });
  });
}

module.exports.isVenueIdValid = async function (venueId) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'Accept': 'application/json',
        'Accept-encoding': 'gzip'
      }
      // let locations = await this.getData(env.DOSHI_APP_BASE_URL + '/locations', headers);
      // let checkLocation = locations.filter(location => location.id == venueId);
      // let isvalid = Array.isArray(checkLocation) && checkLocation.length == 1 ? true : false;
      // resolve(isvalid);
      let locations = await this.getData(env.DOSHI_APP_BASE_URL + '/locations/' + venueId, headers);
      if (typeof locations == 'object' && locations.hasOwnProperty('id')) {
        resolve(true);
      }
      resolve(false);
    } catch (error) {
      console.log(error);
      resolve(false);
    }
  });
}

module.exports.getDoshiVenueDetails = async function (venueId) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'Accept': 'application/json',
        'Accept-encoding': 'gzip'
      }
      let location = await this.getData(env.DOSHI_APP_BASE_URL + `/locations/${venueId}`, headers);
      resolve(location);
    } catch (error) {
      console.log(error);
      reject(error);
    }
  });
}

module.exports.subscribeVenue = async function (venueId) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'Accept': '*/*',
        'Content-Type': 'application/json'
      }
      let data = {
        "mappedLocationId": venueId,
        "useFilteredMenu": true
      };
      let result = await this.postData(env.DOSHI_APP_BASE_URL + `/locations/${venueId}/subscription`, data, headers);
      resolve(result);
    } catch (error) {
      resolve(error);
    }
  });
}

module.exports.unsubscribeVenue = async function (venueId) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'Accept': '*/*',
        'Content-Type': 'application/json'
      }
      let result = await this.deleteData(env.DOSHI_APP_BASE_URL + `/locations/${venueId}/subscription`, headers);
      resolve(result);
    } catch (error) {
      resolve(error);
    }
  });
}

module.exports.setWebhookForVenue = async function (webhookUrl, venueId, webhookEvent) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'doshii-location-id': venueId,
        'Accept': '*/*',
        'Content-Type': 'application/json'
      }
      let payload = {
        "event": webhookEvent,
        "webhookUrl": webhookUrl
      };
      let result = await this.postData(env.DOSHI_APP_BASE_URL + `/webhooks`, payload, headers);
      resolve(result);
    } catch (error) {
      reject(error);
    }
  });
}

module.exports.unsetWebhookForVenue = async function (venueId, webhookEvent) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'doshii-location-id': venueId,
        'Content-Type': 'application/json'
      }
      let result = await this.deleteData(env.DOSHI_APP_BASE_URL + `/webhooks/${webhookEvent}`, headers);
      resolve(result);
    } catch (error) {
      resolve(error);
    }
  });
}

module.exports.titleCase = (str) => {
  var splitStr = str.toLowerCase().split(' ');
  for (var i = 0; i < splitStr.length; i++) {
    splitStr[i] = splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
  }
  return splitStr.join(' ');
}

module.exports.getDoshiiMenuItems = async function (venueId) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'doshii-location-id': venueId,
        'Accept': 'application/json',
        'Accept-encoding': 'gzip'
      }
      let result = await this.getData(env.DOSHI_APP_BASE_URL + `/locations/${venueId}/menu?filtered=false`, headers);
      resolve(result);
    } catch (error) {
      reject(error);
    }
  });
}

module.exports.isVenueActive = async function (venueId) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'doshii-location-id': venueId,
        'Accept': 'application/json',
        'Accept-encoding': 'gzip'
      }
      let result = await this.getData(env.DOSHI_APP_BASE_URL + `/health/locations/${venueId}`, headers);
      let status = '';
      if (typeof result == 'object' && result.heartbeat != null && result.status == 'active') {
        status = 'active';
      } else {
        status = 'inactive';
      }
      resolve(status);
    } catch (error) {
      console.log(error);
      resolve('inactive');
    }
  });
}

module.exports.getNextId = async (tableName) => {
  const records = await sequelize.query(`SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = "mytap" AND TABLE_NAME = '${tableName}'`, { type: Sequelize.QueryTypes.SELECT });
  if (Array.isArray(records) && records.length == 1) {
    return records[0]['AUTO_INCREMENT'];
  }
  return 0;
}

module.exports.createOrderinDoshii = async function (venueId, orderId, serviceType) {
  try {
    var orderDetails = await order.findOne({
      where: {
        isDeleted: 'No',
        id: orderId
      },
      attributes: [
        'id',
        'orderNo',
        'pickupCode',
        [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
        'subTotal',
        'transactionFee',
        'tax',
        'total',
        'orderDate',
        'orderStatus',
        'orderServiceType',
        'paymentType',
        'refundStatus',
        'posFailedCount',
        'promocode_id',
        'promocode_amount',
        'promocode_discount',
        'cardType',
        'cardNumber',
        'userID',
        'barID',
        'createdAt',
        'refundedDate',
        'transactionID',
        'posOrderFee'
      ],
      include: [
        {
          attributes: [
            'id',
            'orderID',
            'productID',
            'price',
            'chargeAmount',
            'quantity',
            'specialRequest',
            'isCanceled',
            'refundAmount',
            'refundedQuantity',
            'waitTime',
            'orderStatus',
            'PreparingStartTime',
            'ReadyTime',
            'PickedupTime',
            [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
            [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
            [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
          ],
          model: orderItems,
          include: [
            {
              attributes: [
                'id',
                'name',
                'description',
                'avatar',
                'posID'
              ],
              model: product,
              include: [
                {
                  attributes: [
                    'id',
                    'description',
                    'address'
                  ],
                  model: pickupLocation,
                },
                {
                  attributes: [
                    'id',
                    'name',
                  ],
                  model: subCategory,
                }
              ]
            },
            {
              attributes: [
                'id',
                'orderItemID',
                'productExtrasID',
                'price'
              ],
              model: orderItemExtras,
              include: [
                {
                  attributes: [
                    'id',
                    'extraItem',
                    'posID',
                    'productOptionposID',
                    'productOptionName'
                  ],
                  model: productExtras,
                }
              ]
            },
            {
              attributes: [
                'id',
                'orderItemID',
                'productVariantsID',
                'price'
              ],
              model: orderItemVariants,
              include: [
                {
                  attributes: [
                    'id',
                    'variantType'
                  ],
                  model: productVariants,
                }
              ]
            },
            {
              attributes: [
                ['id', "orderProductVariantTypeID"],
                'orderItemID',
              ],
              where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
              model: orderProductVariantTypes,
              required: false,
              include: [
                {
                  attributes: [
                    ['id', "productVariantTypeID"],
                    'label',
                    'posID'
                  ],
                  model: productVariantTypes,
                  required: true,
                  include: [
                    {
                      attributes: [
                        ['id', "orderProductVariantSubTypeID"],
                        'orderItemID',
                      ],
                      where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                      model: orderProductVariantSubTypes,
                      include: [
                        {
                          attributes: [
                            ['id', "productVariantSubTypeID"],
                            ['variantType', "extraItem"],
                            'price',
                            'posID'
                          ],
                          model: productVariantSubTypes,
                        }
                      ]
                    }
                  ]
                }
              ],
            }
          ]
        },
        {
          required: true,
          model: bar,
          attributes: ['id', 'restaurantName', 'email', 'businessRegisterId', 'address']
        },
        {
          model: coupons,
          attributes: ['id', 'code', 'name', 'description', 'is_fixed', 'discount_amount']
        },
        {
          attributes: [
            'id', 'name', 'percentage', "taxID", "amount"
          ],
          model: orderTax,
        },
        {
          attributes: [
            'id', 'name', 'percentage', "taxID", "amount"
          ],
          model: orderRefundTax,
        },
        {
          attributes: [
            'id', 'fullName', 'mobile', 'countryCode', 'email'
          ],
          model: user,
        },
      ]
    });

    let stripePerTransactionFee = (orderDetails.total * (1.75 / 100)) + 0.3;
    const stripeFees = {
      name: "Stripe Transaction Fee",
      type: 'absolute',
      amount: parseInt(stripePerTransactionFee * 100),
      value: parseInt(stripePerTransactionFee * 100)
    }

    let mytabFees = (orderDetails.subTotal * (1.5 / 100));
    const myTabPOSfees = {
      name: "MyTab Premium+ POS Fee",
      type: 'absolute',
      amount: parseInt(mytabFees * 100),
      value: parseInt(mytabFees * 100)
    }

    let mytabTransactionFees = (orderDetails.transactionFee);
    const myTabTransactionFeefees = {
      name: "MyTab Customer Transaction Fee",
      type: 'absolute',
      amount: parseInt(mytabTransactionFees * 100),
      value: Math.round((mytabTransactionFees * 100))
    }

    let orderTaxArray = [];
    // orderTaxArray.push(stripeFees);
    // orderTaxArray.push(myTabPOSfees);
    // orderTaxArray.push(myTabTransactionFeefees);

    var totalPromoCodeAmount = 0;
    if (orderDetails.coupon) {
      let promoCode = {
        name: orderDetails.coupon.name,
        type: 'percentage',
        amount: orderDetails.coupon.discount_amount,
        value: parseInt((0 - (orderDetails.promocode_amount) * 100)),
      }
      totalPromoCodeAmount = orderDetails.promocode_amount
      orderTaxArray.push(promoCode);
    }

    let paymentMethod = 'credit'
    if (orderDetails.paymentType == '1') {
      if (orderDetails.cardType.toLowerCase() == 'visa' || orderDetails.cardType.toLowerCase() == 'mastercard') {
        paymentMethod = orderDetails.cardType
      } else {
        paymentMethod = 'other'
      }
    }

    var totalTaxAmount = 0;
    let orderTaxes = orderDetails.order_taxes.map(tax => {
      let singleOrderTax = {
        name: tax.name,
        type: 'percentage',
        amount: (tax.percentage),
        value: parseInt(0 + tax.dataValues.amount * 100)
      }
      totalTaxAmount = totalTaxAmount + tax.dataValues.amount
      orderTaxArray.push(singleOrderTax);
    })

    if (serviceType == 'pickup') {
      // var noteString = '';
      var orderItemsArr = orderDetails.order_items.map(orderItem => {
        // let extraItem = "";
        // if(orderItem.dataValues && orderItem.dataValues.order_item_extras) {
        //   orderItem.dataValues.order_item_extras.forEach(element => {
        //     if(element.dataValues.product_extra) {
        //       extraItem += element.dataValues.product_extra.dataValues.extraItem+", ";
        //     }
        //   });
        // }
        // if(orderItem.dataValues && orderItem.dataValues.order_product_variant_types) {
        //   orderItem.dataValues.order_product_variant_types.forEach(element => {
        //     if(element.dataValues.product_variant_type && element.dataValues.product_variant_type.dataValues.order_product_variant_sub_type && element.dataValues.product_variant_type.dataValues.order_product_variant_sub_type.dataValues.product_variant_sub_type) {
        //       extraItem += element.dataValues.product_variant_type.dataValues.order_product_variant_sub_type.dataValues.product_variant_sub_type.dataValues.extraItem+", ";
        //     }
        //   });
        // }

        // extraItem = extraItem.replace(/,(\s+)?$/, '');

        // if(noteString === ''){
        //   noteString = ''+orderItem.quantity+' X '+orderItem.product.name +', '+ extraItem + ': '+orderItem.specialRequest
        // }else{
        //   noteString = noteString+', '+orderItem.quantity+' X '+orderItem.product.name +', '+ extraItem +': '+orderItem.specialRequest
        // }

        let allOptions = []
        orderItem.order_product_variant_types.map((productVariantItem) => {
          allOptions.push({
            name: productVariantItem.product_variant_type.label,
            posId: productVariantItem.product_variant_type.posID,
            variants: [
              {
                name: productVariantItem.product_variant_type.order_product_variant_sub_type.product_variant_sub_type.dataValues.extraItem,
                posId: productVariantItem.product_variant_type.order_product_variant_sub_type.product_variant_sub_type.posID,
                price: productVariantItem.product_variant_type.order_product_variant_sub_type.product_variant_sub_type.price * 100
              }
            ]
          })
        })
        orderItem.order_item_extras.map((extra) => {
          allOptions.push({
            name: extra.product_extra.productOptionName,
            posId: extra.product_extra.productOptionposID,
            variants: [
              {
                name: extra.product_extra.extraItem,
                posId: extra.product_extra.posID,
                price: extra.price * 100
              }
            ]
          })
        })
        if (orderItem.specialRequest != '') {
          allOptions.push({
            name: 'Instructions',
            variants: [
              {
                name: orderItem.specialRequest,
                price: 0
              }
            ]
          })
        }
        return {
          name: orderItem.product.name,
          tags: [orderItem.product.sub_category.name],
          type: 'single',
          posId: orderItem.product.posID,
          options: [],
          quantity: orderItem.quantity,
          unitPrice: orderItem.price * 100,
          description: ' ',
          surcounts: [],
          totalBeforeSurcounts: Math.round((orderItem.chargeAmount * 100) * orderItem.quantity),
          totalAfterSurcounts: Math.round((orderItem.chargeAmount * 100) * orderItem.quantity),
          options: allOptions
        }
      })
      var orderSubTotal = orderDetails.subTotal;
      var orderTotal = orderDetails.total;
      var orderStripeFee = parseFloat(((orderDetails.total * (1.75 / 100)) + 0.3)); // Domestic
      // var orderStripeFee = parseFloat(((orderDetails.total * (2.9/100)) + 0.3)); // International
      var orderPremiumPOSFee = parseFloat((orderDetails.subTotal * (orderDetails.posOrderFee / 100)));
      var payload = {
        order: {
          type: 'pickup',
          externalOrderRef: '' + orderDetails.orderNo + ' #' + orderDetails.pickupCode,
          surcounts: orderTaxArray,
          items: orderItemsArr,
          notes: '' + orderDetails.orderNo + ' #' + orderDetails.pickupCode
        },
        transactions: [{
          reference: orderDetails.transactionID,
          amount: Math.round(((orderSubTotal * 100) + (totalTaxAmount * 100)) - (totalPromoCodeAmount * 100)),
          prepaid: true,
          method: paymentMethod,
          surcounts: []
        }],
        consumer: {
          name: orderDetails.user.fullName,
          phone: orderDetails.user.countryCode + '' + orderDetails.user.mobile,
          //email: orderDetails.user.email,
        }
      };
    } else {
      let tableCode = orderDetails.dataValues.tableCode
      let checkInPayload = {
        type: "table",
        tableNames: [
          tableCode
        ],
        consumer: {
          name: orderDetails.user.fullName,
          phone: orderDetails.user.countryCode + '' + orderDetails.user.mobile,
          //email: orderDetails.user.email,
        }
      }

      let checkInResult = await this.createDoshiiCheckIn(venueId, checkInPayload, orderId);
      // var noteString = '';
      var orderItemsArr = orderDetails.order_items.map(orderItem => {
        // if(orderItem.specialRequest != ''){
        // let extraItem = "";
        // if(orderItem.dataValues && orderItem.dataValues.order_item_extras) {
        //   orderItem.dataValues.order_item_extras.forEach(element => {
        //     if(element.dataValues.product_extra) {
        //       extraItem += element.dataValues.product_extra.dataValues.extraItem+", ";
        //     }
        //   });
        // }
        // if(orderItem.dataValues && orderItem.dataValues.order_product_variant_types) {
        //   orderItem.dataValues.order_product_variant_types.forEach(element => {
        //     if(element.dataValues.product_variant_type && element.dataValues.product_variant_type.dataValues.order_product_variant_sub_type && element.dataValues.product_variant_type.dataValues.order_product_variant_sub_type.dataValues.product_variant_sub_type) {
        //       extraItem += element.dataValues.product_variant_type.dataValues.order_product_variant_sub_type.dataValues.product_variant_sub_type.dataValues.extraItem+", ";
        //     }
        //   });
        // }

        // extraItem = extraItem.replace(/,(\s+)?$/, '');

        // if(noteString === ''){
        //   noteString = ''+orderItem.quantity+' X '+orderItem.product.name +', '+ extraItem + ': '+orderItem.specialRequest
        // }else{
        //   noteString = noteString+', '+orderItem.quantity+' X '+orderItem.product.name +', '+ extraItem +': '+orderItem.specialRequest
        // }
        // }
        let allOptions = []
        orderItem.order_product_variant_types.map((productVariantItem) => {
          allOptions.push({
            name: productVariantItem.product_variant_type.label,
            posId: productVariantItem.product_variant_type.posID,
            variants: [
              {
                name: productVariantItem.product_variant_type.order_product_variant_sub_type.product_variant_sub_type.dataValues.extraItem,
                posId: productVariantItem.product_variant_type.order_product_variant_sub_type.product_variant_sub_type.posID,
                price: productVariantItem.product_variant_type.order_product_variant_sub_type.product_variant_sub_type.price * 100
              }
            ]
          })
        })
        orderItem.order_item_extras.map((extra) => {
          allOptions.push({
            name: extra.product_extra.productOptionName,
            posId: extra.product_extra.productOptionposID,
            variants: [
              {
                name: extra.product_extra.extraItem,
                posId: extra.product_extra.posID,
                price: extra.price * 100
              }
            ]
          })
        })
        if (orderItem.specialRequest != '') {
          allOptions.push({
            name: 'Instructions',
            variants: [
              {
                name: orderItem.specialRequest,
                price: 0
              }
            ]
          })
        }
        return {
          name: orderItem.product.name,
          tags: [orderItem.product.sub_category.name],
          type: 'single',
          posId: orderItem.product.posID,
          options: [],
          quantity: orderItem.quantity,
          unitPrice: orderItem.price * 100,
          description: ' ',
          surcounts: [],
          totalBeforeSurcounts: Math.round((orderItem.chargeAmount * 100) * orderItem.quantity),
          totalAfterSurcounts: Math.round((orderItem.chargeAmount * 100) * orderItem.quantity),
          options: allOptions
        }
      })
      var orderSubTotal = orderDetails.subTotal;
      var orderTotal = orderDetails.total;
      var orderStripeFee = parseFloat(((orderDetails.total * (1.75 / 100)) + 0.3)); // Domestic
      // var orderStripeFee = parseFloat(((orderDetails.total * (2.9/100)) + 0.3)); // International
      var orderPremiumPOSFee = parseFloat((orderDetails.subTotal * (orderDetails.posOrderFee / 100)));
      var payload = {
        order: {
          type: 'dinein',
          checkinId: checkInResult.id,
          externalOrderRef: '' + orderDetails.orderNo,
          surcounts: orderTaxArray,
          items: orderItemsArr,
          notes: '' + orderDetails.orderNo
        },
        transactions: [{
          reference: orderDetails.transactionID,
          amount: Math.round(((orderSubTotal * 100) + (totalTaxAmount * 100)) - (totalPromoCodeAmount * 100)),
          prepaid: true,
          method: paymentMethod,
          surcounts: []
        }],
        consumer: {
          name: orderDetails.user.fullName,
          phone: orderDetails.user.countryCode + '' + orderDetails.user.mobile,
          //email: orderDetails.user.email,
        }
      };
      // if (noteString !== "") {
      //   payload.order.notes =  ''+orderDetails.orderNo+' - '+noteString;
      // }else{
      //   payload.order.notes =  ''+orderDetails.orderNo;
      // }
    }
    var token = await this.getDoshiAuthToken();
    let headers = {
      'Authorization': 'Bearer ' + token,
      'doshii-location-id': venueId,
      'Content-Type': 'application/json',
      'Accept-encoding': 'gzip'
    }
    let result = await this.postData(env.DOSHI_APP_BASE_URL + `orders`, payload, headers);
    if (typeof result == 'object' && result.hasOwnProperty('id')) {
      await order.update({
        posOrderStatus: 'Pending',
        posOrderId: result.id,
        posOrderVersion: result.version,
        posTransactionId: result.transactions[0].id,
        posTransactionVersion: result.transactions[0].version
      }, {
        where: {
          orderNo: orderDetails.orderNo
        }
      });
    }
    return result
  } catch (error) {
    console.log(error);
    var orderDetails = await order.findOne({
      where: {
        isDeleted: 'No',
        id: orderId
      },
    });
    await order.update({
      posFailedCount: parseInt(orderDetails.posFailedCount + 1)
    }, {
      where: {
        id: orderId
      }
    });
    return 0;
  }
}

module.exports.createDoshiiCheckIn = async function (venueId, payload, orderID) {
  try {
    var token = await this.getDoshiAuthToken();
    let headers = {
      'Authorization': 'Bearer ' + token,
      'doshii-location-id': venueId,
      'Content-Type': 'application/json',
      'Accept-encoding': 'gzip'
    }
    let result = await this.postData(env.DOSHI_APP_BASE_URL + `checkins?checkTables=false`, payload, headers);
    if (typeof result == 'object' && result.hasOwnProperty('id')) {
      await order.update({
        posCheckInId: result.id
      }, {
        where: {
          id: orderID
        }
      });
      return result;
    } else {
      var orderDetails = await order.findOne({
        where: {
          isDeleted: 'No',
          id: orderID
        },
      });
      await order.update({
        posFailedCount: orderDetails.posFailedCount + 1
      }, {
        where: {
          id: orderID
        }
      });
    }
    return result;
  } catch (error) {
    console.log(error);
    var orderDetails = await order.findOne({
      where: {
        isDeleted: 'No',
        id: orderID
      },
    });
    await order.update({
      posFailedCount: orderDetails.posFailedCount + 1
    }, {
      where: {
        id: orderID
      }
    });
    return error;
  }
}

module.exports.getOrderDetailsFromDoshii = async function (venueId, orderId) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'doshii-location-id': venueId,
        'Content-Type': 'application/json',
        'Accept-encoding': 'gzip'
      }
      let result = await this.getData(env.DOSHI_APP_BASE_URL + `/orders/${orderId}`, headers);
      if (typeof result == 'object' && result.hasOwnProperty('id')) {
        resolve(result);
      }
      reject(result);
    } catch (error) {
      console.log(error);
      reject(error);
    }
  });
}

module.exports.updateOrderinDoshii = async function (venueId, orderId, payload) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'doshii-location-id': venueId,
        'Content-Type': 'application/json',
        'Accept-encoding': 'gzip'
      }
      let result = await this.putData(env.DOSHI_APP_BASE_URL + `orders/${orderId}`, payload, headers);
      resolve(result);
    } catch (error) {
      console.log(error);
      reject(error);
    }
  });
}

module.exports.getDoshiiMenuItemByposID = async function (venueId, posId) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'doshii-location-id': venueId,
        'Accept': 'application/json',
        'Accept-encoding': 'gzip'
      }
      let result = await this.getData(env.DOSHI_APP_BASE_URL + `/locations/${venueId}/menu/products/${posId}?filtered=false`, headers);
      resolve(result);
    } catch (error) {
      reject(error);
    }
  });
}

module.exports.updateOrderTransactioninDoshii = async function (venueId, transactionId, payload) {
  return new Promise(async (resolve, reject) => {
    try {
      var token = await this.getDoshiAuthToken();
      let headers = {
        'Authorization': 'Bearer ' + token,
        'doshii-location-id': venueId,
        'Content-Type': 'application/json',
        'Accept-encoding': 'gzip'
      }
      let result = await this.putData(env.DOSHI_APP_BASE_URL + `/transactions/${transactionId}`, payload, headers);
      resolve(result);
    } catch (error) {
      console.log(error);
      reject(error);
    }
  });
}

module.exports.checkBarIsOpen = (barHours, activeHours = null, inActiveHours = null, weekDay = null) => {
  let isOpen;
  if (!barHours.length)
    return 0;

  if (activeHours && inActiveHours) {
    let lastDay = weekDay - 1;
    if (weekDay == 0) {
      lastDay = 6;
    }

    let todayData = barHours.find(operating_hour => operating_hour.weekDay == weekDay);
    let previousDayData = barHours.find(operating_hour => operating_hour.weekDay == lastDay);

    if (!todayData) {
      return 0;
    }
    if ((todayData.openingHours <= activeHours && todayData.closingHours >= activeHours) && (todayData.openingHours <= inActiveHours && todayData.closingHours >= inActiveHours)) {
      isOpen = 1;
    } else if (todayData.openingHours >= todayData.closingHours && (todayData.openingHours <= activeHours && todayData.closingHours <= inActiveHours)) {
      isOpen = 1;
    } else if (previousDayData && previousDayData.openingHours >= previousDayData.closingHours && (previousDayData.closingHours >= activeHours && previousDayData.closingHours >= inActiveHours)) {
      isOpen = 1;
    } else {
      isOpen = 0;
    }
  } else {
    let today = moment().tz('Australia/Perth').isoWeekday() - 1;
    let lastDay = today - 1;
    if (today == 0) {
      lastDay = 6;
    }

    let currentTime = moment().tz('Australia/Perth').format('HH:mm:ss');
    let currentDate = moment().tz('Australia/Perth').format('YYYY-MM-DD');
    let currentDateTime = moment().tz('Australia/Perth');
    let todayData = barHours.find(operating_hour => operating_hour.weekDay === today);
    let previousDayData = barHours.find(operating_hour => operating_hour.weekDay === lastDay);

    if (!todayData) {
      return 0;
    }
    if (todayData.openingHours < currentTime && todayData.closingHours > currentTime) {
      isOpen = 1;
      let venueClosingDateTime = moment(currentDate + ' ' + todayData.closingHours).tz('Australia/Perth', true)
      var duration = moment.duration(venueClosingDateTime.diff(currentDateTime));
      var hours = duration.asMinutes();
      if (hours <= 30 && hours > 0) {
        isOpen = 2;
      }
    } else if (todayData.openingHours > todayData.closingHours && (todayData.openingHours < currentTime && todayData.closingHours < currentTime)) {
      isOpen = 1;
      let venueClosingDateTime = moment(currentDate + ' ' + todayData.closingHours).tz('Australia/Perth', true)
      var duration = moment.duration(venueClosingDateTime.diff(currentDateTime));
      var hours = duration.asMinutes();
      if (hours <= 30 && hours > 0) {
        isOpen = 2;
      }
    } else if (previousDayData && previousDayData.openingHours > previousDayData.closingHours && previousDayData.closingHours > currentTime) {
      isOpen = 1;
      let venueClosingDateTime = moment(currentDate + ' ' + previousDayData.closingHours).tz('Australia/Perth', true)
      var duration = moment.duration(venueClosingDateTime.diff(currentDateTime));
      var hours = duration.asMinutes();
      if (hours <= 30 && hours > 0) {
        isOpen = 2;
      }
    } else {
      isOpen = 0;
    }
  }
  return isOpen;
}

// Validate ABN number
module.exports.validateABN = (abn) => {

  if (abn.length != 11 || isNaN(parseInt(abn))) return false

  let weighting = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19]
  let firstDigitProcessed = parseInt(abn.charAt(0)) - 1
  let weighted = firstDigitProcessed * weighting[0]

  for (var i = 1; i < abn.length; i++) {
    weighted += (parseInt(abn.charAt(i)) * weighting[i])
  }

  return (weighted % 89) == 0
};

// Validate ACN number
module.exports.validateACN = (acn) => {

  if (acn.length != 9 || isNaN(parseInt(acn))) return false

  var weighting = [8, 7, 6, 5, 4, 3, 2, 1]
  var weighted = 0
  for (var i = 0; i < weighting.length; i++) {
    weighted += (parseInt(acn.charAt(i)) * weighting[i]);
  }
  let checkDigit = 10 - (weighted % 10)
  checkDigit = checkDigit == 10 ? 0 : checkDigit
  return checkDigit == acn[8]
};

// Split time in specified size and return array
module.exports.timeChunkArray = (startTime, endTime, chunkSize) => {
  const result = [];
  let format = 'HH:mm';
  let prevTime = moment(startTime, format);
  let time = moment(startTime, format).add(chunkSize, 'minutes');

  while (moment(time).isBetween(moment(startTime, format), moment(endTime, format))) {
    result.push({
      'startTime': moment(prevTime, format).format('HH:mm'),
      'endTime': moment(time, format).format('HH:mm'),
    });
    prevTime = moment(time, format);
    time = moment(time, format).add(chunkSize, 'minutes');
  }

  if (moment(time, format).format('HH:mm') === moment(endTime, format).format('HH:mm')) {
    result.push({
      'startTime': moment(prevTime, format).format('HH:mm'),
      'endTime': moment(endTime, format).format('HH:mm'),
    });
  } else if (moment(time, format) > moment(endTime, format)) {
    result.push({
      'startTime': moment(prevTime, format).format('HH:mm'),
      'endTime': moment(endTime, format).format('HH:mm'),
    });
  }

  return result;
};

module.exports.getProductWaitingTime = async (productID) => {
  try {
    let currentDay = moment().tz('Australia/Perth').isoWeekday()
    const result = await product.findOne({
      attributes: [
        'id',
        'barID',
        'categoryID',
        'subCategoryID',
        [
          Sequelize.literal(`
            (select waitTime from sub_category_wait_time 
              where 
                barID = product.barID and
                subCategoryID = product.subCategoryID and
                weekDay = '${currentDay - 1}' and
                startTime <= '${moment().tz('Australia/Perth').format('HH:mm:ss')}' and
                endTime >= '${moment().tz('Australia/Perth').format('HH:mm:ss')}'
            )`
          ), 'waitTime'
        ],
        [
          Sequelize.literal(`
            (select closingHours from operating_hours 
              where 
                barID = product.barID and
                weekDay = '${currentDay - 1}' and
                isClosed = '0'
            )`
          ), 'closingHours'
        ],
      ],
      where: {
        id: productID
      }
    });
    if (result) {
      let closingHours = result.dataValues.closingHours
      if (closingHours) {
        let current_date = moment().tz('Australia/Perth').format('Y-MM-D');
        var expectedTime = moment().tz('Australia/Perth').add(result.dataValues.waitTime).format('Y-MM-D H:m:s');
        var venueClosingTime = current_date + ' ' + closingHours;
        if (new Date(venueClosingTime) < new Date(expectedTime)) {
          var now = moment().tz('Australia/Perth').format('Y-MM-D H:m:ss');
          const diffInMs = Math.abs(new Date(venueClosingTime) - new Date(now));
          const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
          const diffInHours = Math.floor(diffInMinutes / 60);
          return Math.floor(diffInHours) + ":" + Math.floor(diffInMinutes % 60) + ":00";
        } else {
          return result.dataValues.waitTime
        }
      } else {
        return null
      }
    } else {
      return null
    }
  } catch (error) {
    return null
  }
}

module.exports.getProductDetail = async (productID) => {
  try {
    const currentDateTimeUTC = moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const [currentDay, currentTime] = [moment(currentDateTimeUTC).isoWeekday() - 1, moment(currentDateTimeUTC).format('HH:mm:ss')];

    const result = await product.findOne({
      where: { id: productID },
      attributes: [
        'id', 'barID', 'categoryID', 'subCategoryID', 'name', 'description', 'avatar',
        'price', 'pickupLocationID', 'posID', 'stock', 'dailyStockRenewal', 'isStockLimit',
        'isDailyStockRenewal', 'status', 'createdAt', 'updatedAt', 'isDeleted',
        'fromPosId', 'serviceType', 'isUpdateByUser',
        [
          Sequelize.literal(`
            (SELECT waitTime FROM bar_sub_category_wait_time_utc 
             WHERE barID = product.barID 
               AND subCategoryID = product.subCategoryID 
               AND weekDay = '${currentDay}' 
               AND startTime <= '${currentTime}' 
               AND endTime >= '${currentTime}')
          `), 'waitTime'
        ],
        [
          Sequelize.literal(`
            (SELECT id FROM bar_opening_hours_utc 
             WHERE barID = product.barID 
               AND weekDay = '${currentDay}' 
               AND isClosed = '0' 
               AND '${currentTime}' BETWEEN openingHours AND closingHours)
          `), 'barOpeningHoursUTCID'
        ],
      ]
    });

    if (!result) return null;

    const barOpeningHoursUTCID = result.dataValues.barOpeningHoursUTCID;
    const operatingHours = await barOpeningHoursUTC.findByPk(barOpeningHoursUTCID);

    if (!operatingHours) {
      result.productWaitTime = null;
      return result;
    }

    const otherSameDayOperatingHours = await barOpeningHoursUTC.count({
      where: { barOpeningHoursID: operatingHours.dataValues.barOpeningHoursID }
    });

    if (operatingHours.closingHours == '23:59:59' && otherSameDayOperatingHours > 1) {
      result.productWaitTime = result.dataValues.waitTime;
      return result;
    }

    const currentDate = moment().format('YYYY-MM-DD');
    const expectedTime = moment().add(result.dataValues.waitTime).format('YYYY-MM-DD HH:mm:ss');
    const venueClosingTime = `${currentDate} ${operatingHours.closingHours}`;

    if (new Date(venueClosingTime) < new Date(expectedTime)) {
      const now = moment().format('YYYY-MM-DD HH:mm:ss');
      const diffInMinutes = Math.abs(moment(venueClosingTime).diff(moment(now), 'minutes'));
      const diffInHours = Math.floor(diffInMinutes / 60);
      const calculatedWaitTime = `${diffInHours}:${diffInMinutes % 60}:00`;

      result.productWaitTime = diffInMinutes > moment.duration(result.dataValues.waitTime).asMinutes()
        ? result.dataValues.waitTime
        : calculatedWaitTime;
    } else {
      result.productWaitTime = result.dataValues.waitTime;
    }

    return result;
  } catch (error) {
    console.error(error);
    return error;
  }
};


module.exports.generateRandomString = (length = 8) => {
  try {
    var result = '';
    var characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(
        Math.floor(Math.random() * charactersLength)
      );
    }
    return result;
  } catch (error) {
    console.log(error)
    return error
  }
}

function getMultipleFindInSetWhereClause(IDs, columnName, allowNull = 0) {
  try {
    let whereClauseFindInSet = IDs.map((id) => {
      return Sequelize.where(Sequelize.fn('FIND_IN_SET', id, Sequelize.col(columnName)), '>', 0)
    })
    if (allowNull) {
      var whereClause = {
        [Op.or]: [
          {
            ...whereClauseFindInSet
          },
          Sequelize.where(Sequelize.col(columnName), null)
        ]
      }
    } else {
      var whereClause = {
        [Op.or]: [
          {
            ...whereClauseFindInSet
          },
        ]
      }
    }
    return whereClause
  } catch (error) {
    return error
  }
}

module.exports.convertOpeningHours = (timeSlot, timezone, weekDay) => {
  try {
    const localWeekDay = parseInt(weekDay, 10);
    const utcEntries = [];
    const localOpen = moment
      .tz(`${localWeekDay} ${timeSlot.openingHours}`, 'e HH:mm', timezone)
      .utc();
    const localClose = moment
      .tz(`${localWeekDay} ${timeSlot.closingHours}`, 'e HH:mm', timezone)
      .utc();
    const utcOpenDay = localOpen.isoWeekday() % 7;
    const utcCloseDay = localClose.isoWeekday() % 7;

    if (utcOpenDay === utcCloseDay) {
      utcEntries.push({
        openingHours: localOpen.format('HH:mm:ss'),
        closingHours: localClose.format('HH:mm:ss'),
        weekDay: utcOpenDay
      });
    } else {
      utcEntries.push(
        {
          openingHours: localOpen.format('HH:mm:ss'),
          closingHours: '23:59:59',
          weekDay: utcOpenDay
        },
        {
          openingHours: '00:00:00',
          closingHours: localClose.format('HH:mm:ss'),
          weekDay: utcCloseDay
        }
      );
    }

    return utcEntries;
  } catch (error) {
    console.error('Error in converting opening hours:', error);
    return [];
  }
};