var Sequelize = require('sequelize');
var productVariantTypes = require('./productVariantTypes');
var productVariantSubTypes = require('./productVariantSubTypes');

var cart_product_variant_sub_types = sequelize.define(
  'cart_product_variant_sub_types',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    cartItemID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "cartItems",
        key: "id"
      }
    },
    cartProductVariantTypeID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "cart_product_variant_types",
        key: "id"
      }
    },
    productVariantTypeID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product_variant_types",
        key: "id"
      }
    },
    productVariantSubTypeID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product_variant_sub_types",
        key: "id"
      }
    },
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
);

cart_product_variant_sub_types.belongsTo(productVariantSubTypes, { foreignKey: 'productVariantSubTypeID' });
// productVariantTypes.hasMany(cart_product_variant_sub_types, { foreignKey: 'productVariantTypeID' });
// cart_product_variant_sub_types.belongsTo(productVariantTypes, { foreignKey: 'productVariantTypeID' });

module.exports = cart_product_variant_sub_types;
