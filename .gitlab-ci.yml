image: docker:latest

variables: 
  AWS_REGION: "ap-southeast-2"
  ECR_REGISTRY: "535002880427.dkr.ecr.ap-southeast-2.amazonaws.com/mytab-stg-api"
  IMAGE_TAG: "latest"
  ECS_CLUSTER: "mytab-stg-ecs-cluster"
  ECS_SERVICE: "mytab-customer-venue-app-api-stg-service"

stages:
  - build
  - deploy

before_script:
  - apk add --no-cache curl jq aws-cli
  - aws --version
  - echo "Logging into Amazon ECR..."
  - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY

build_and_push:
  environment: $CI_COMMIT_REF_NAME
  stage: build
  script: 
    - echo "Building  Docker Image..."
    - docker build -t $ECR_REGISTRY:$IMAGE_TAG .
    - echo "Pushing Image to ECR..."
    - docker push $ECR_REGISTRY:$IMAGE_TAG
  only:
    - staging

deploy_to_ecs:
  environment: $CI_COMMIT_REF_NAME
  stage: deploy
  script:
    - echo "Forcing new deployment of ECS service..."
    - aws ecs update-service --cluster $ECS_CLUSTER --service $ECS_SERVICE --force-new-deployment --region $AWS_REGION
    - echo "Waiting for deployment to complete..."
    - aws ecs wait services-stable --cluster $ECS_CLUSTER --services $ECS_SERVICE --region $AWS_REGION
    - echo "Deployment completed successfully!"
  only:
    - staging
