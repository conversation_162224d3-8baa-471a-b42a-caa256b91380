const env = require('../config/environment')
const nodemailer = require('nodemailer')
const multer = require('multer')
const AWS = require('aws-sdk')
const multerS3 = require('multer-s3')
const bar = require('../models/bar')

AWS.config.update({
  secretAccessKey: env.awsSecretAccessKey,
  accessKeyId: env.awsAccessKey,
  region: env.awsRegion
})

function sendEmail(emailOptions) {
  try {
    return new Promise((resolve, reject) => {
      const smtpTransport = nodemailer.createTransport({
        host: env.sesEmailHost,
        port: 587,
        secure: false,
        auth: {
          user: env.sesUser,
          pass: env.sesPassword
        }
      });

      smtpTransport.sendMail(emailOptions)
        .then(response => resolve(response))
        .catch(err => reject(err));
    })
  } catch (e) {
    console.log(e)
  }
}

function uploadFile(file, path) {
  const s3 = new AWS.S3()
  try {
    return new Promise((resolve, reject) => {
      var uploadImage = multer({
        storage: multerS3({
          s3: s3,
          acl: 'public-read',
          contentType: multerS3.AUTO_CONTENT_TYPE,
          bucket: function (req, file, cb) {
            cb(
              null,
              env.awsMainBucket + path
            )
          },
          key: function (req, file, cb) {
            let extArray = file.mimetype.split('/')
            let extension = extArray[extArray.length - 1]
            cb(null, Date.now().toString() + '.' + extension) //new Date().toISOString() +
          }
        })
      })
      console.log(uploadImage)
    });
  } catch (e) {
    console.log(e)
  }
}

function deleteFile(filename, path) {
  var s3 = new AWS.S3()
  return new Promise((resolve, reject) => {
    var params = {
      Bucket:
        env.awsMainBucket + path,
      Key: filename
    }
    s3.deleteObject(params, function (err, data) {
      if (data) {
        console.log('File deleted successfully')
        resolve(data);
      } else {
        console.log('Check if you have sufficient permissions : ' + err)
        reject(err);
      }
    })
  })
}

function deletePublicFile(filename, path) {
  const s3 = new AWS.S3();
  return new Promise((resolve, reject) => {
    var params = {
      Bucket:
        env.awsPublicBucket + path,
      Key: filename
    }
    s3.deleteObject(params, function (err, data) {
      if (data) {
        console.log('File deleted successfully')
        resolve(data);
      } else {
        console.log('Check if you have sufficient permissions : ' + err)
        reject(err);
      }
    })
  })
}

module.exports = {
  sendEmail,
  uploadFile,
  deleteFile,
  deletePublicFile
}
