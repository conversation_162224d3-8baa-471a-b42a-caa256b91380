var Sequelize = require('sequelize')
const Op = Sequelize.Op
const moment = require('moment')
const message = require('../../config/message')
const cartItems = require('../../models/cartItems')
const bar = require('../../models/bar')
const barOpeningHoursUTC = require('../../models/barOpeningHoursUTC')
const barSubCategoryWaitTime = require('../../models/barSubCategoryWaitTime')
const barSubCategoryWaitTimeUTC = require('../../models/barSubCategoryWaitTimeUTC')
const productTax = require("../../models/product_tax")
const user = require('../../models/user')
var env = require('../../config/environment')
const common = require('../common')
var jwt = require('jsonwebtoken');
const sequelize = require('sequelize')
const { checkBarIsOpen } = require('../../common/commonFunction')
const randtoken = require('rand-token').generator();

exports.getAccountDeleteForm = async (req, res) => {
  try {

    const DELETE_FORMS = {
      PAYMENT_REQUIRED: `https://n1g6fmcikpp.typeform.com/to/vzhKKZXL#bar_id=${res.locals.barID}`,
      NO_PAYMENT_REQUIRED: `https://n1g6fmcikpp.typeform.com/to/ty0lOSW4#bar_id=${res.locals.barID}`
    };

    let barDetails = await bar.findOne({
      where: {
        id: res.locals.barID
      }
    });

    if (barDetails) {
      let data = {}
      const createdAt = barDetails.dataValues.createdAt;
      const oneYearAgo = moment().subtract(1, 'year');
      const isOlderThanOneYear = moment(createdAt).isBefore(oneYearAgo);
      const venueName = barDetails.dataValues?.restaurantName;
      const venueAddress = barDetails.dataValues?.address;
      const managerName = barDetails.dataValues?.managerName;
      const countryCode = barDetails.dataValues?.countryCode;
      const prefixCountryCode = countryCode
					? countryCode.startsWith('+')
						? countryCode
						: `+${countryCode}`
					: '';
      const managerMobile = barDetails.dataValues?.mobile;
      const managerEmail = barDetails.dataValues?.email;
      const venueCreated = moment(createdAt)
        .tz('Australia/Perth')
        .format('DD-MM-YYYY');
      const today = new Date();
      const year = today.getFullYear();
      const subject = `Request to Delete Venue Account: ${venueName}`

      var emailContent = `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8"><base href="/"/><title>${subject}</title>
            </head>
            <body style="padding:0; margin:0; color:#6e6e6e; font-family:Verdana; -webkit-text-size-adjust: none;">
              <table style="-webkit-text-size-adjust: none;" width="100%" cellspacing="0" cellpadding="0" border="0">
                <tr>
                  <td align="center">
                    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="min-width:200px; border: 1px solid #6e6e6e; max-width:600px; ">
                      <tr style="border-bottom: 3px solid #58585a">
                        <td>
                          <center>
                            <a style="color:#6e6e6e; text-decoration:none;" href="#">
                              <h1 style="margin-top:30px">
                                <img src=${process.env.logo} alt=MyTab style="border-width:0; max-width:164px;height:55px; display:block; " />
                              </h1>
                            </a>
                          </center>
                        </td>
                      </tr>
                      <tr>
                        <td style="padding: 15px; font-size: 10pt; line-height: 22px;">
                          <div>` +
        '<p>Hello,</p>' +
        '<p>We have received a request to delete the following venue account. ' +
					'Below are the details of the account that initiated the deletion process:' +
					'</p><p><strong>Venue Name</strong>: ' +
        venueName +
        '</p><p><strong>Venue Address</strong>: ' +
        venueAddress +
        '</p><p><strong>Venue Owner/Manager Name</strong>: ' +
        managerName +
        '</p><p><strong>Venue Owner/Manager Mobile</strong>: ' + prefixCountryCode + ' '+
      managerMobile +
        '</p><p><strong>Venue Owner/Manager Email</strong>: <a href="mailto:' +
        managerEmail +
        '">' +
        managerEmail +
        '</a></p><p><strong>Date Venue Account Was Created</strong>: ' +
        venueCreated +
        '</p>' +
        '<p>Best Regards,</p>' +
        '<p><strong>MyTab</strong></p>' +
        `</div>
                        </td>
                      </tr>
                      <tr>
                        <td style="text-align: center; background-color:#58585a; color: #fff; padding-top: 5px; padding-bottom: 5px; font-weight: bold; font-size: 30px;" colspan="2"></td>
                      </tr>
                      <tr>
                        <td style="text-align: center; background-color: #58585a; color: #fff; padding: 0px 0px 10px;" colspan="2">
                          <p style="font-size: 12px;line-height: 1.6;font-weight: normal;"> &copy; ${year} MyTab | All&nbsp;Rights&nbsp;Reserved</p>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </body>
          </html>`;


      let mailOptions = {
        from: '"MyTab" <' + env.fromEmailVenueAdmin + '>',
        to: process.env.deleteAccountEmail,
        subject: subject,
        html: emailContent
      }

      await common.sendEmail(mailOptions)

      if (isOlderThanOneYear) {
        data = { link: DELETE_FORMS.NO_PAYMENT_REQUIRED };
      } else {
        data = { link: DELETE_FORMS.PAYMENT_REQUIRED };
      }
      res.status(200).send({
        success: 1,
        data: data,
        message: message.bar.accountDeleteFormFetched
      })
    }
    else {
      res.status(200).send({
        success: 0,
        message: message.bar.notFound
      })
    }

  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
},

exports.getProductTax = async (req, res) => {
  try {
    productTax
      .findOne({
        where: {
          id: 1
        }
      })
      .then(productTaxResponse => {
        if (productTaxResponse && Object.keys(productTaxResponse).length > 0) {
          res.status(200).send({
            success: 1,
            data: productTaxResponse,
            message: message.bar.productTaxFetched
          })
        } else {
          res.status(200).send({
            success: 0,
            message: message.bar.productTaxNotFound
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.editProfileEmailOTP = async (req, res) => {
  try {
    const { email } = req.body;
    const barID = res.locals.barID;

    // Check if the email already exists for a different bar
    const checkBarExists = await bar.findOne({
      where: {
        email,
        isDeleted: 'No',
        id: { $ne: barID },
      },
    });

    if (checkBarExists) {
      return res.status(200).send({
        success: 0,
        message: message.bar.emailExists,
      });
    }

    // Fetch the bar details for the current barID
    const barDetails = await bar.findOne({ where: { id: barID } });

    if (!barDetails) {
      return res.status(200).json({
        success: 0,
        message: 'Account does not exist',
        data: {},
      });
    }

    // Generate the OTP
    const sixDigitCode = randtoken.generate(6, '**********');
    const subject = 'MyTab Account Verification Code';
    const year = new Date().getFullYear();

    // Email options
    const mailOptions = {
      from: `MyTab <${env.fromEmailAdmin}>`,
      to: email,
      subject: subject,
      html: `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8"><base href="/"/><title>${subject}</title>
        </head>
        <body style="padding:0; margin:0; color:#6e6e6e; font-family:Verdana; -webkit-text-size-adjust: none;">
          <table style="-webkit-text-size-adjust: none;" width="100%" cellspacing="0" cellpadding="0" border="0">
            <tr>
              <td align="center">
                <table width="100%" cellpadding="0" cellspacing="0" border="0" style="min-width:200px; border: 1px solid #6e6e6e; max-width:600px;">
                  <tr style="border-bottom: 3px solid #58585a">
                    <td>
                      <center>
                        <a style="color:#6e6e6e; text-decoration:none;" href="https://www.mytabinfo.com">
                          <h1 style="margin-top:30px">
                            <img src=${process.env.logo} alt=MyTab style="border-width:0; max-width:164px;height:55px; display:block;" />
                          </h1>
                        </a>
                      </center>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 15px; font-size: 10pt; line-height: 22px;">
                      <div>
                        <p>Hello ${barDetails.restaurantName},</p>
                        <p>Please verify your MyTab account email by entering the 6-digit verification code below in the app.</p>
                        <p>One-Time code: <b>${sixDigitCode}</b></p>
                        <p>If you did not request this verification, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a> for assistance.</p>
                        <p>Kindest,<br/> MyTab Venue Support Team <br/> <a style="color:#ff6460" href="https://www.mytabinfo.com">www.mytabinfo.com</a><p/>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td style="text-align: center; background-color:#58585a; color: #fff; padding-top: 5px; padding-bottom: 5px; font-weight: bold; font-size: 30px;" colspan="2"></td>
                  </tr>
                  <tr>
                    <td style="text-align: center; background-color: #58585a; color: #fff; padding: 0px 0px 10px;" colspan="2">
                      <p style="font-size: 12px;line-height: 1.6;font-weight: normal;"> &copy; ${year} MyTab | All&nbsp;Rights&nbsp;Reserved</p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </body>
        </html>`,
    };

    // Send the email (commented out in your code)
    await common.sendEmail(mailOptions);

    await bar.update(
      { oneTimePassword: sixDigitCode },
      { where: { id: barID } }
    );

    return res.status(200).json({
      success: 1,
      message: 'A code has been sent to your email',
      data: {},
    });
  } catch (error) {
    console.error('Error:', error);
    return res.status(500).send({
      success: 0,
      message: 'An error occurred!',
    });
  }
};

exports.editProfileVerifyOTP = async (req, res) => {
  try {
    const barID = res.locals.barID;
    const OTP = req.body.otp;
    bar
      .findOne({
        where: {
          id: barID,
          status: 'Active',
          isDeleted: 'No'
        }
      })
      .then(async(barDataResponse) => {
        if(barDataResponse){
          if(barDataResponse.oneTimePassword == OTP){
            await barDataResponse.update({ oneTimePassword: 0 });
            return res.status(200).json({
              success: 1,
              message: 'Your email address is verified',
              data: {}
            })
          }else{
            return res.status(200).json({
              success: 0,
              message: 'Please enter the correct verification code',
              data: {}
            })
          }
        }else{
          return res.status(200).send({
            success: 0,
            message: 'Account does not exist'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getSubHeadingWaitTime = async (req, res) => {
  try {
    const barID = res.locals.barID;
    var subCategoryID = req.body.subCategoryID
    barSubCategoryWaitTime
      .findAll({
        where: { subCategoryID: subCategoryID, barID: barID },
        attributes: [
          [sequelize.literal("waitTimeType"), 'type'],
          [sequelize.literal("`weekDay`,JSON_ARRAYAGG(JSON_OBJECT('id',`id`,'startTime',TIME_FORMAT(`startTime`,'%H:%i'),'endTime',TIME_FORMAT(`endTime`,'%H:%i'),'waitTime',TIME_FORMAT(`waitTime`,'%H:%i')))"), 'weekDay_WaitingTime'],
        ],
        group: ['weekDay'],
        order: ['weekDay'],
        raw: true,
      })
      .then(async response => {
        if (response.length > 0) {
          let newResponse = response.map((day) => {
            let weekDay_WaitingTime = day.weekDay_WaitingTime.sort(function (a, b) {
              return a.startTime.localeCompare(b.startTime);
            });
            return { ...day, weekDay_WaitingTime: weekDay_WaitingTime }
          })
          res.status(200)
            .json({
              success: 1,
              message: 'Wait time retrieved successfully.',
              data: newResponse
            })
        } else {
          res.status(200)
            .send({
              success: 0,
              message: "No wait time found for this sub category. Please add the kitchen time slot first.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.updateSubHeadingWaitTime = async (req, res) => {
  try {
    const barID = res.locals.barID;
    const waitTimeType = req.body.type
    const subCategoryID = req.body.subCategoryID
    const waitTimeArray = req.body.data;
    const barDetails = await bar.findByPk(barID);

    let subCategoryWaitTime = await barSubCategoryWaitTime.findOne({
      where: { subCategoryID, barID }
    });

    if (!subCategoryWaitTime) return 0;

    await Promise.all([
      barSubCategoryWaitTime.update(
        { waitTimeType },
        { where: { subCategoryID, barID } }
      ),
      barSubCategoryWaitTimeUTC.update(
        { waitTimeType },
        { where: { subCategoryID, barID } }
      )
    ]);

    if (waitTimeArray) {
      for (let day of waitTimeArray) {
        if (!day?.weekDay_WaitingTime) continue;
        await Promise.all(
          day.weekDay_WaitingTime.map(async (item) => {
            await Promise.all([
              barSubCategoryWaitTimeUTC.update(
                {
                  waitTime: item.waitTime >= '00:00' ? item.waitTime : '00:10'
                },
                { where: { barSubCategoryWaitTimeID: item.id } }
              ),
              barSubCategoryWaitTime.update(
                {
                  waitTime: item.waitTime >= '00:00' ? item.waitTime : '00:10'
                },
                { where: { id: item.id } }
              )
            ]);
          })
        );
      }
    }
    return res.status(200).json({ success: 1, message: 'Wait time updated successfully.', data: {}})
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.getBars = async (req, res) => {
  const userID = res.locals.userID;
  try {
    var { search, latitude, longitude } = req.body
    if (latitude == '' || longitude == '') {
      latitude = env.latitude
      longitude = env.longitude
    }

    let selectedServiceType = req.body.serviceType ? req.body.serviceType : 'BOTH';
    let distanceKm = req.body.distanceKm ? parseFloat(req.body.distanceKm) : ***************;

    let page = req.body.page ? req.body.page : 1;
    let per_page = req.body.per_page ? parseInt(req.body.per_page) : 10;

    if (req.body.show_all && req.body.show_all == '1') {
      per_page = ***************;
    }

    let offset = (page - 1) * per_page;
    let limit = per_page;

    var whereClause = []
    whereClause.push({
      isDeleted: 'No',
      status: 'Active',
      accountVerified: 'Approved',
      restaurantName: {
        [Op.like]: '%' + search + '%'
      },
      [Op.and]: [{ stripeID: { [Op.ne]: null } }, { stripeID: { [Op.ne]: '' } }],
    });

    if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
      whereClause.push({
        [Op.or]: [{ serviceType: selectedServiceType }, { serviceType: 'BOTH' }]
      });
    }

    const currentDateTimeUTC = req.body.currentDateTimeUTC ? req.body.currentDateTimeUTC : moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const currentDay = moment(currentDateTimeUTC).isoWeekday() - 1;
    const currentTime = moment(currentDateTimeUTC).format('HH:mm:ss');

    let queryOptions = {
      attributes: [
        'id',
        'restaurantName',
        'managerName',
        'email',
        'countryCode',
        'mobile',
        'address',
        'latitude',
        'longitude',
        'serviceType',
        'avatar',
        'isVenueServeAlcohol',
        'liquorLicenseNumber',
        [
          sequelize.literal(`
            CASE 
              WHEN EXISTS (
                SELECT 1
                FROM bar_opening_hours_utc AS OP
                WHERE OP.barID = bar.id
                  AND OP.weekDay = ${currentDay}
                  AND OP.isClosed = 0
                  AND '${currentTime}' BETWEEN OP.openingHours AND OP.closingHours
              )
              THEN 1
              ELSE 0
            END
          `),
          'operatingFlag'
        ],
        [sequelize.literal("coalesce(waitTimeDrink, 0)"), "waitTimeDrink"],
        [sequelize.literal("coalesce(waitTimeFood, 0)"), "waitTimeFood"],
        [sequelize.literal("ROUND(" + env.DISVAL + " * acos(cos(radians(" + latitude + ")) * cos(radians(latitude)) * cos(radians(longitude) - radians(" + longitude + ")) + sin(radians(" + latitude + ")) * sin(radians(latitude))), 2)"), 'distance'],
        [sequelize.literal("'" + env.DISTEXT + "'"), 'distance_ext'],
        [
          sequelize.literal(`(
                SELECT EXISTS(
                  SELECT 1 
                  FROM user_fav_venue 
                  WHERE barID = bar.id AND userID = ${userID} 
                  LIMIT 1
                )
              )`),
          "isVenueFavorite",
        ],
        [
          sequelize.literal(`(
              SELECT COUNT(1) 
              FROM orders 
              WHERE orders.paymentStatus = 'received' 
              AND orders.barID = bar.id 
              AND orders.isDeleted = 'No'
              AND orders.isCanceled = 'No'
              AND orders.refundStatus != 'Refunded'
            )`),
          "totalOrders",
        ],

      ],
      include: [
        {
          model: barOpeningHoursUTC,
          attributes: [
            'id',
            'weekDay',
            'barOpeningHoursID',
            'openingHours',
            'closingHours',
            'isClosed'
          ],
          required: false,
          where: { 
            isClosed: 0,
            weekDay: currentDay,
            openingHours: { [Op.lte]: currentTime },
            closingHours: { [Op.gte]: currentTime }
          }
        }
      ],
      where: whereClause,
      distinct: true,
      having: {
        distance: { $lt: distanceKm }
      },
    };

    let barCount = await bar.findAll(queryOptions);

    bar
      .findAll({
        ...queryOptions,
        order: [sequelize.literal('operatingFlag DESC'), sequelize.literal('distance ASC')],
        offset: offset,
        limit: limit,
      })
      .then(async response => {
        if (barCount.length > 0) {
          response.forEach(async (element, index) => {
            const data = response[index].dataValues;
            const barHours = data.bar_opening_hours_utcs[0]?.dataValues;
            const closingTime = barHours?.closingHours;
            if (data.operatingFlag == '1') {
              if (closingTime == '23:59:59') {
                const otherRecord = await barOpeningHoursUTC.findOne({
                  where: {
                    barOpeningHoursID: barHours.barOpeningHoursID,
                    [Op.not]: { id: barHours.id, weekDay: currentDay }
                  }
                });
                data.isOpen = otherRecord ? '1' : moment(closingTime, 'HH:mm:ss').diff(moment(currentTime, 'HH:mm:ss'), 'minutes') < 30 ? '2' : '1';
              } else {
                data.isOpen = moment(closingTime, 'HH:mm:ss').diff(moment(currentTime, 'HH:mm:ss'), 'minutes') < 30 ? '2' : '1';
              }
            } else {
              data.isOpen = '0';
            }
            delete response[index].dataValues.bar_opening_hours_utcs;
          });

          let resArray = {}
          const [cartData, userData] = await Promise.all([
            cartItems.findOne({
              where: { userID },
              include: [{
                model: bar,
                attributes: ['id', 'restaurantName', 'avatar']
              }]
            }),
            user.findOne({
              where: { id: userID },
              attributes: ['readPopup']
            })
          ]);

          resArray.barlist = response
          resArray.count = barCount.length
          resArray.cartData = {};
          resArray.userData = {};
          resArray.cartData.cartItemAvailable = (cartData) ? 'Yes' : 'No'
          resArray.cartData.restaurantName = (cartData) ? cartData.bar.restaurantName : ''
          resArray.cartData.barID = (cartData) ? cartData.bar.id : ''
          resArray.cartData.cartServiceType = (cartData) ? cartData.cartServiceType : ''
          resArray.cartData.avatar = (cartData) ? cartData.bar.avatar : '',
            resArray.userData.readPopup = (userData) ? userData.dataValues.readPopup : '',
            res.status(200).send({
              success: 1, 
              message: 'Results Found',
              data: resArray
            })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: {}
          })
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}





