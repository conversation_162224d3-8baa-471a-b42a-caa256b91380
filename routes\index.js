const express = require('express')
const router = express()

const userRoutes = require('./user')
const page = require('./page')
const barRoutes = require('./bar')
const pickupLocationRoutes = require('./pickupLocation')
const categoryRoutes = require('./category')
const productRoutes = require('./product')
const paymentMethodRoutes = require('./paymentMethod')
const orderRoutes = require('./order')
const cartRoutes = require('./cart')
const reportRoutes = require('./report')
const posSpecificRoutes = require('./posSpecific')
const userSubscriptionRoutes = require('./userSubscription')
const v2BarRoutes=require("./v2/bar")
const v2ProductRoutes=require("./v2/product")
const v2UpsellRoutes=require("./v2/upsell")
const v2PickupLocationRoutes=require("./v2/pickupLocation")
const v2CategoryRoutes=require("./v2/category")
const v2OpeningHours=require("./v2/openingHours")
const v2Order=require("./v2/order")
const v2User=require("./v2/user")
const v2DiscountRoutes=require("./v2/discount")
const v2CartRoutes=require("./v2/cart")
const advertiserRoutes = require('./v2/advertiser')


router.use('/user', userRoutes)
router.use('/page', page)
router.use('/bar', barRoutes)
router.use('/pickupLocation', pickupLocationRoutes)
router.use('/category', categoryRoutes)
router.use('/product', productRoutes)
router.use('/paymentMethod', paymentMethodRoutes)
router.use('/order', orderRoutes)
router.use('/cart', cartRoutes)
router.use('/report', reportRoutes)
router.use('/pos', posSpecificRoutes)
router.use('/userSubscription', userSubscriptionRoutes)
router.use('/v2/bar', v2BarRoutes)
router.use('/v2/product', v2ProductRoutes)
router.use('/v2/upsell', v2UpsellRoutes)
router.use('/v2/pickupLocation', v2PickupLocationRoutes)
router.use('/v2/category', v2CategoryRoutes)
router.use('/v2/openingHours', v2OpeningHours)
router.use('/v2/order', v2Order)
router.use('/v2/user', v2User)
router.use('/v2/discount', v2DiscountRoutes)
router.use('/v2/cart', v2CartRoutes)
router.use('/v2/ads', advertiserRoutes)
module.exports = router
