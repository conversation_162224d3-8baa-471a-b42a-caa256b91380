// 8.  Ability to rearrange menu products under subheadings start
const Sequelize = require('sequelize')
const bar = require('./bar')
const category = require('./category')
const subCategory = require('./subCategory')
const product = require('./product')

const bar_product_sequence = sequelize.define(
	'bar_product_sequence',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barId: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "bar",
				key: "id"
			}
		},
		categoryId: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "category",
				key: "id"
			}
		},
		subCategoryId: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "sub_category",
				key: "id"
			}
		},
    productId: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "product",
				key: "id"
			}
		},
		productSequence: Sequelize.INTEGER,
	},
	{
		freezeTableName: true,
		timestamps: false
	}
)

bar_product_sequence.belongsTo(category, { foreignKey: 'categoryId' })
category.hasMany(bar_product_sequence, { foreignKey: 'categoryId', as: 'bar_product_sequence' })

bar_product_sequence.belongsTo(subCategory, { foreignKey: 'subCategoryId' })
subCategory.hasMany(bar_product_sequence, { foreignKey: 'subCategoryId', as: 'bar_product_sequence' })

bar_product_sequence.belongsTo(product, { foreignKey: 'productId' })
product.hasMany(bar_product_sequence, { foreignKey: 'productId', as: 'bar_product_sequence' })

bar_product_sequence.belongsTo(bar, { foreignKey: 'barId' })
bar_product_sequence.belongsTo(product, { foreignKey: 'productId' })

module.exports = bar_product_sequence
// 8. Ability to rearrange menu products under subheadings end