const Sequelize = require('sequelize')
const bar = require('./bar')

const operatingHours = sequelize.define(
	'operating_hours',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "bar",
				key: "id"
			}
		},
		openingHours: Sequelize.TIME,
		closingHours: Sequelize.TIME,
		weekDay: Sequelize.SMALLINT,
		isClosed: Sequelize.BOOLEAN,
		createdAt: Sequelize.DATE,
		updatedAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false
	}
)

operatingHours.belongsTo(bar, { foreignKey: 'barID' })
bar.hasMany(operatingHours, { foreignKey: 'barID' })

module.exports = operatingHours