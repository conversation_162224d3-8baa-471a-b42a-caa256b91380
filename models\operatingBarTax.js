const Sequelize = require('sequelize')
const tax = require('./tax')

const operatingBarTax = sequelize.define(
	'operating_bar_tax',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		taxID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "tax",
				key: "id"
			}
		},
		percentage: Sequelize.FLOAT,
		openingHours: Sequelize.TIME,
		closingHours: Sequelize.TIME,
		weekDay: Sequelize.SMALLINT,
		isClosed: Sequelize.BOOLEAN,
		createdAt: Sequelize.DATE,
		updatedAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false
	}
)

operatingBarTax.belongsTo(tax, { foreignKey: 'taxID' })
tax.hasMany(operatingBarTax, { foreignKey: 'taxID' })

module.exports = operatingBarTax