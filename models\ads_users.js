var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')
// Remove circular dependency - ads relationship will be defined in ads.js

var env = require('../config/environment')

var ads_users = sequelize.define(
    'ads_users',
    {
        id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true
        },
        userID: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'user',
                key: 'id'
            }
        },
        adsID: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'ads',
                key: 'id'
            }
        },
        barID: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'bar',
                key: 'id'
            }
        }
    },
    {
        freezeTableName: true,
        timestamps: false
    }
)


// Relationship with ads is defined in ads.js to avoid circular dependency
// ads_users.belongsTo(ads, { foreignKey: 'adsID' });

ads_users.associate = function (models) {
    ads_users.belongsTo(models.ads, { foreignKey: 'adsID' });
};

ads_users.belongsTo(user, {
    foreignKey: 'userID'
});

ads_users.belongsTo(bar, {
    foreignKey: 'barID'
});
module.exports = ads_users