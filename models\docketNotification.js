var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')
var order = require('./orders')

var docketNotification = sequelize.define(
  'docket_notification',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orders",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    notification_type: Sequelize.TEXT,
    message: Sequelize.TEXT,
    status: Sequelize.ENUM('0', '1'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

docketNotification.belongsTo(order, { foreignKey: 'orderID' })
docketNotification.belongsTo(bar, { foreignKey: 'barID' })

module.exports = docketNotification