var Sequelize = require('sequelize')
var bar = require('./bar')

var userSubscription = sequelize.define(
  'user_subscriptions',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    product_id: {
      type: Sequelize.TEXT
    },
    trial_end: {
      type: Sequelize.INTEGER,
    },
    current_period_end: {
      type: Sequelize.INTEGER,
    },
    bar_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "bar",
        key: "id"
      }
    },
    status: {
      type: Sequelize.INTEGER
    },
    subscription_id: Sequelize.TEXT,
    receipt: Sequelize.TEXT,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

userSubscription.belongsTo(bar, { foreignKey: 'bar_id' })

module.exports = userSubscription