var Sequelize = require('sequelize');
const productVariantSubTypes = require("./productVariantSubTypes");
const cartProductVariantSubTypes = require("./cartProductVariantSubTypes");

var product_variant_types = sequelize.define(
  'product_variant_types',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    productID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product",
        key: "id"
      }
    },
    label: Sequelize.TEXT,
    status: Sequelize.ENUM('Active', 'Inactive'),
    posID: Sequelize.STRING,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No'),
    serviceType: Sequelize.ENUM('PICKUP', 'TABLE', 'BOTH'),
    isUpdateByUser: Sequelize.ENUM('Yes', 'No'),
  },
  {
    freezeTableName: true,
    timestamps: false
  }
);
product_variant_types.hasMany(productVariantSubTypes, { foreignKey: 'productVariantTypeID', as: 'productVariantSubTypes' });
product_variant_types.hasOne(cartProductVariantSubTypes, { foreignKey: 'productVariantTypeID' });
cartProductVariantSubTypes.belongsTo(product_variant_types, { foreignKey: 'productVariantTypeID' });

module.exports = product_variant_types;