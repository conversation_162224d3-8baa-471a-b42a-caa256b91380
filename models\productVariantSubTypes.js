var Sequelize = require('sequelize');

var product_variant_sub_types = sequelize.define(
  'product_variant_sub_types',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    productVariantTypeID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product_variant_types",
        key: "id"
      }
    },
    variantType: Sequelize.TEXT,
    price: Sequelize.FLOAT,
    posID: Sequelize.STRING,
    status: Sequelize.ENUM('Active', 'Inactive'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No'),
    extraSequence: Sequelize.INTEGER
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = product_variant_sub_types