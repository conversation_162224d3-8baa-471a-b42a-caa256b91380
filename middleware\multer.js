/*
 * Summary:     Multer is middleware for upload image
 */
const multer = require("multer");
const path = require("path");
const os = require("os");
const fs = require("fs");
const tmpdir = os.tmpdir();
const commonFunction = require("../common/commonFunction");

var storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, tmpdir);
  },
  filename: function (req, file, cb) {
    const imageName =
      commonFunction.generateRandomString(5) +
      "_" +
      Date.now() +
      path.extname(file.originalname);

    const filepath = path.join(tmpdir, imageName);

    file.originalname = imageName;
    
    fs.mkdtemp(filepath, (err, folder) => {
      if (err) throw err;
      cb(null, imageName);
    });
  },
});

exports.singleUserProfilePic = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }
}).single("avatar");

exports.singleProfilePic = multer({
  storage: storage,
}).single("image");

exports.singleBarPic = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }
}).single("avatar");

exports.singleProductPic = multer({
  storage: storage,
}).single("avatar");

exports.multiProfilePic = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }
}).any();
