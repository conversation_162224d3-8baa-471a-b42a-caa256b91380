const {Op, literal, col} = require('sequelize');

const bar = require('../models/bar')
const POSconfig = require('../models/POSconfig')

const product = require('../models/product')
const subCategory = require('../models/subCategory')
const category = require('../models/category')
const productExtras = require('../models/productExtras')
const productVariants = require('../models/productVariants')
const users = require('../models/user')

const orders = require('../models/orders')
const orderItems = require('../models/orderItems')
const orderItemExtras = require('../models/orderItemExtras')
const orderItemVariant = require('../models/orderItemVariants')

const transactionErrorLogs = require('../models/transactionErrorLogs')
const transactionLogs = require('../models/transactionLogs')

const barPOSMigration = require('../models/barPOSMigration')
const env = require("../config/environment");
const jwt = require("jsonwebtoken");
const axios = require("axios");
const moment = require("moment");
const crypto = require("crypto");
const {sendEmail} = require("./common");
const fs = require('fs');

const {categoryPosHandling} = require("./category");
const {productPosHandling} = require("./product");

const subcategory = require("../models/subCategory");
const productModel = require("../models/product");
const {orderPosHandling} = require("./order");
const coupons = require("../models/coupons");
const order = require("../models/orders");
const commonFunction = require("../common/commonFunction");
const userNotification = require("../models/userNotification");

const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const orderTableNumber = require('../models/orderTableNumber');
const redirectBaseUrl = env.API_URL

// const redirectBaseUrl = 'https://fd19-103-81-92-241.ngrok.io'

class POS {
	constructor(barId) {
		this.barId = barId
		this.barPOSDetails = null
		this.barDetails = null
		this.posSubmissionDetails = {}
	}

	async pushNotificationForOrders(orderDetails, status) {
		let {id, orderServiceType, pickupCode, userID} = orderDetails
		let notification_type, posPayload, message;
		// Table code seperated for new change
		let table_code = await orderTableNumber.findOne({
			attributes: ['tableCode'],
			where: {orderID: id},
			order: [['id', 'DESC']]
		})
		const tableCode = table_code && table_code.tableCode

		switch (status) {
			case 'Preparing':
				message = orderServiceType === 'PICKUP' ? `Your order ${pickupCode} is confirmed and being prepared.` : `Order for table #${tableCode} is confirmed and being prepared.`;
				notification_type = 'orderPreparing'
				posPayload = {};
				break;

			case 'Pickup':
				message = orderServiceType === 'PICKUP' ? `Your order ${pickupCode} is ready to collect.` : `Your order for table #${tableCode} is ready.`;
				notification_type = 'orderReady';
				posPayload = {};
				break;

			case 'Pickedup':
				message = orderServiceType === 'PICKUP' ? `Thank you for collecting your order ${pickupCode}. Enjoy!` : `Your order has been delivered to your table number #${tableCode}. Enjoy!`;
				notification_type = 'orderPickedup'
				posPayload = {};
				break;

			case 'NotPickedup':
				message = orderServiceType === 'PICKUP' ? `Your order ${pickupCode} has been marked as not collected. Please contact the venue.` : `Your order for table #${tableCode} has been marked as not collected. Please contact the venue.`;
				notification_type = 'orderNotPickedup'
				break;
		}

		if (message !== '') {
			await userNotification.create({
				barID: orderDetails.barID,
				notification_type: notification_type,
				userID: userID,
				dataID: orderDetails.id,
				message: message,
				createdAt: new Date()
			})
			await commonFunction.orderStatusNotificationToUser(userID, id, notification_type, message)
		}

	}

	async updateVenuePosHandle(optedPosId, posState) {
		const barPosConnection = await barPOSMigration.findOne({where: {barId: this.barId}})
		if (barPosConnection) {
			let data = {optedPosId: optedPosId, status: posState}
			if (posState === 'UN_CONFIGURED')
				data = {...data, venuePosConfigDetails: {}}
			await barPosConnection.update({optedPosId: optedPosId, ...data})
		}
		else {
			await barPOSMigration.create({barId: this.barId, optedPosId: optedPosId})
		}

		return barPosConnection
	}

	async fetchVenuePOSMigrationDetails() {
		const [posConfigDetails, isInserted] = await barPOSMigration.findOrCreate({
			defaults: {barId: this.barId, venuePosConfigDetails: {}, status: 'UN_CONFIGURED'},
			attributes: ['barId', 'status', 'optedPosId', 'venuePosConfigDetails'],
			where: {barId: this.barId},
			include: [{
				model: bar,
				include: [POSconfig]
			}, POSconfig],
			order: [['createdAt', 'DESC']]
		});
		if (!isInserted) {
			this.barPOSDetails = posConfigDetails
			return posConfigDetails
		} else {
			return this.fetchVenuePOSMigrationDetails()
		}
	}

	async fetchOptedPosConfig() {
		return await bar.findOne({
			attributes: ['attachedPosConfig'],
			where: {id: this.barId}
		})
	}

	async exportCSVFile(optedPosId) {
		try {
			this.barDetails = await bar.findOne({where: {id: this.barId}})
			const csvHeaders = (csvType) => [
				{id: 'orderDate', title: 'ORDER DATE'},
				{id: 'orderNo', title: 'ORDER NUMBER'},

				{id: 'pickUpCode', title: 'PICK-UP CODE'},
				{id: 'tableCode', title: 'TABLE CODE'},

				{id: 'product_name', title: csvType === 'KITCHEN' ? 'KITCHEN ITEMS (FOOD)' : 'DRINK ITEMS'},
				{id: 'product_price', title: 'PRICE'},
				{id: 'product_quantity', title: 'SALE QUANTITY'},

				{id: 'order_item_extra_name', title: 'EXTRAS'},
				{id: 'order_item_extra_price', title: 'PRICE'},
				{id: 'product_extra_quantity', title: 'EXTRAS SALE QUANTITY'},

				{id: 'coupon_code', title: 'PROMO CODE USED'},
				{id: 'promocode_amount', title: 'PROMO CODE AMOUNT'},

				{id: 'isCanceled', title: 'ORDER NOT PICKED-UP?'},
				{id: 'refundStatus', title: 'REFUNDED'},

				{id: 'product_refund_price', title: 'TOTAL AMOUNT REFUNDED'},
				{id: 'product_refund_quantity', title: 'REFUNDED MENU ITEM QUANTITY'},
				{id: 'product_extra_refund_quantity', title: 'REFUNDED EXTRAS QUANTITY'},
				// {id: 'merchant_platform_fee', title: 'REFUNDED EXTRA NAME'},

				{id: 'initial_customer_payment', title: 'INITIAL CUSTOMER PAYMENT'},
				{id: 'total', title: 'FINAL CUSTOMER PAYMENT'},
				{id: 'transactionFee', title: 'MYTAB CUSTOMER FEE'},
				{id: 'merchant_platform_fee', title: 'MERCHANT PLATFORM FEE'},
				{id: 'venue_earnings', title: 'NET MYTAB VENUE REVENUE'},
			]
			let exportDetail = (serviceWhere) => order.findAll({
				attributes: ['id', 'orderNo', 'pickUpCode', [literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'], 'isCanceled', 'refundStatus', 'total', 'transactionFee', 'orderDate', 'promocode_amount',
					[literal("(select round(sum(total + ((total * 2.9 / 100 + 0.31) + (IF((`orderDate` < DATE('2021-06-25')), ((total * 0.25 / 100) + 0.25), 0)))), 2) from orders as o where o.id = `orders`.id)"), 'initial_customer_payment'],
					[literal("(select round(sum((IF((`orderDate` < DATE('2021-06-25')), ((total * 0.25 / 100) + 0.25), 0))), 2) from orders as o where o.id = `orders`.id)"), 'merchant_platform_fee'],
					[literal("(select round(sum(total - ((total * 2.9 / 100 + 0.31) + (IF((`orderDate` < DATE('2021-06-25')), ((total * 0.25 / 100) + 0.25), 0)))), 2) from orders as o where o.id = `orders`.id)"), 'venue_earnings'],
					[col('`order_items->product`.`name`'), 'product_name'],

					[col('`order_items`.`price`'), 'product_price'],
					[col('`order_items`.`quantity`'), 'product_quantity'],
					[literal('(SELECT count(order_item_extras.productExtrasID) from order_item_extras inner join order_items oi on order_item_extras.orderItemID = oi.id where oi.refundedQuantity = 0 and oi.orderID = `orders`.id)'), 'product_extra_quantity'],

					[col('`order_items`.`refundAmount`'), 'product_refund_price'],
					[col('`order_items`.`refundedQuantity`'), 'product_refund_quantity'],
					[literal('(SELECT count(order_item_extras.productExtrasID) from order_item_extras inner join order_items oi on order_item_extras.orderItemID = oi.id where oi.refundedQuantity > 0 and oi.orderID = `orders`.id)'), 'product_extra_refund_quantity'],

					[col('`order_items->order_item_extras`.`price`'), 'order_item_extra_price'],
					[col('`order_items->order_item_extras->product_extra`.`extraItem`'), 'order_item_extra_name'],
					[col('`coupon`.`code`'), 'coupon_code'],
				],
				where: {barID: this.barId, ...serviceWhere},
				include: [{
					attributes: ['productID'],
					model: orderItems,
					include: [
						{
							model: orderItemExtras,
							attributes: [],
							include: [{
								model: productExtras,
								attributes: [],
							},
							]
						},
						{
							model: product,
							attributes: [],
							required: false,
						}]
				}, {
					model: coupons,
					attributes: [],
				}],
				raw: true,
				subQuery: false
			})

			const CSVPromise = [
				exportDetail({'$order_items->product.categoryID$': [-1, 1]}),
				exportDetail({'$order_items->product.categoryID$': [-1, 2]})
			]

			const currentTime = moment().format('x')
			const loweVenueName = this.barDetails.restaurantName.toLowerCase()
			const csvPath = `./exported_csv/${loweVenueName}/${currentTime}`
			await fs.promises.mkdir(csvPath, {recursive: true})

			Promise.all(CSVPromise).then(
				CSVContent => {
					let [kitchenContent, drinkContent] = CSVContent
					// Kitchen CSV
					const kitchenCSVWriter = createCsvWriter({
						path: `${csvPath}/kitchen.csv`,
						header: csvHeaders('KITCHEN'),
						headerIdDelimiter: '.',
					})

					kitchenCSVWriter
						.writeRecords(kitchenContent)
						.then(() => console.log('kitchen Exported to CSV'))

					// Drinks CSV
					const drinksCSVWriter = createCsvWriter({
						path: `${csvPath}/drinks.csv`,
						header: csvHeaders('DRINK'),
						headerIdDelimiter: '.'
					})

					drinksCSVWriter
						.writeRecords(drinkContent)
						.then(async () => {
							let mailOptions = {
								from: `MyTab <${env.fromEmailAdmin}>`,
								to: this.barDetails.email, // '<EMAIL>'
								subject: `CSV file with MyTab data for venue ${this.barDetails.restaurantName.toLowerCase()}`,
								html: `
            <html lang="en">
							<body>
								<p>Hello ${this.barDetails.restaurantName},</p>
								<p>Congratulations, you are one step closer to finalising your POS integration with your MyTab Venue account. </p>
								<p>Please find attached your MyTab Venue CSV files for both your kitchen (food) sales and your bar (drink) sales.</p>
								<p>Please make sure that you cross reference and verify the data on these CSV files with your MyTab Venue account under order history. Once you finalise your POS integration you will no longer have access to these sales data from your MyTab Venue account, only on the attached CSV files.</p>
								<p>If you need any assistance, please email us at ${env.barEmailTo}</p>
								<br>
								<p>Thank you!</p>
								<p>MyTab Venue Support Team</p>
								<p>My Venue | My Way | MyTab</p>
								<p>www.MyTabinfo.com</p>
								<img src="cid:mytab@logo" alt="MyTab Logo" height="150" width="150"/>
							</body>
            </html>`,
								attachments: [{
									filename: `${currentTime}_current_stats_drinks.csv`,
									path: `${csvPath}/drinks.csv`
								}, {
									filename: `${currentTime}_current_stats_kitchen.csv`,
									path: `${csvPath}/kitchen.csv`
								}, {
									filename: 'logo.png',
									path: './static/mailLogo.png',
									cid: 'mytab@logo' //same cid value as in the html img src
								}]
							}
							await sendEmail(mailOptions)
						})
				}
			)
			await this.updateVenuePosHandle(optedPosId, 'CSV_EXPORT_PHASE')
			return {
				success: -2,
				message: 'An email with your attached CSV file has now been sent with the existing data related to your MyTab Venue account. Please make sure you cross reference and verify all the data on the CSV file with your MyTab Venue past order history to ensure all data has been recorded. Please do not proceed further until this has been verified, as all sales data prior to POS integration will be removed on the next step. Please contact us if you require further support.',
				data: await this.fetchVenuePOSMigrationDetails()
			}
		} catch (err) {
			console.log(err)
		}
	}

	async finalWipe(currentPosId) {
		const productToWipe = await product.findAll({
			where: {
				barID: this.barId,
				[Op.and]: [
					{fromPosId: currentPosId},
					{posId: {[Op.not]: null}},
				],
				isDeleted: 'No'
		}})
		const productIds = productToWipe.map(productDetail => productDetail.id)

		// Product Extra wipe
		await productExtras.update({posID: null, fromPosId: null}, {where: {productID: productIds}})
		// Product wipe
		await product.update({posID: null, fromPosId: null}, {where: {id: productIds}})
		// SubCategory wipe
		// if multiple venue have opted to same pos and then one opt out each of them will get removed
		// as there is no personalised subcategory for the venue
		await subcategory.update({posID: null, fromPosId: null}, {where: {fromPosId: currentPosId, posID: {[Op.not]: null}}})

		await this.removeWebhookLinks()
		// Bar remove label
		await bar.update({attachedPosConfig: null}, {where: {id: this.barId}})
		await this.updateVenuePosHandle(-1, 'UN_CONFIGURED')
	}

	async countActiveOrders() {
		return await orders.count({
			include: [{
				model: orderItems,
				required: true,
				include: [{
					model: product,
					required: true,
				}]
			}],
			where: {
				barID: this.barId,
				orderStatus: ['New', 'Preparing', 'Pickup'],
				isCanceled: 'No',
				isDeleted: 'No',
				paymentStatus: 'received',
			},
			distinct: ['id']
		})
	}

	async resetVenueToLastPosState() {
		const venueDetails = await this.fetchVenuePOSMigrationDetails()
		if (venueDetails && venueDetails.bar.attachedPosConfig)
			await this.updateVenuePosHandle(venueDetails.bar.attachedPosConfig, 'POS_CONFIGURED')
		else {
			await this.removeWebhookLinks()
			await this.updateVenuePosHandle(null, 'UN_CONFIGURED')
		}
		return this.fetchVenuePOSMigrationDetails()
	}

	async removeWebhookLinks() {
		try {
			let result = {};
			const venuePosConfigDetails = await this.fetchVenuePOSMigrationDetails()
			const posID = venuePosConfigDetails.bar.attachedPosConfig

		 	switch (posID) {
				case 1:
					const posKounta = new kountaApiHandler(this.barId)
					await posKounta.LOAD_POS_DETAILS_FROM_VENUE_TABLE_FUNCTION
					result = posKounta.removeKountaWebhooks()
					break;
			}
			return result;

		} catch (e) {
			console.log(e)
		}
	}

	async handleDataWipe(optedPosId, confirmWiping, currentPosId) {
		if (confirmWiping) {
			const orderCount = await this.countActiveOrders()
			if (orderCount === 0) {
				await this.finalWipe(currentPosId)
				return {
					success: 1,
					message: `Successfully completed.`,
					data: {}
				};
			} else {
				//  Reset functionality
				await this.resetVenueToLastPosState()
				return {
					success: -1,
					message: `There are ${orderCount} ongoing orders, please process them to continue with the POS.`,
					data: {}
				};
			}
		} else {
			await this.resetVenueToLastPosState()
			return {
				success: -1,
				message: `System will now go back to the last configured state.`,
				data: {}
			};
		}
	}

	// API handler open functions

	// keeping this for product add
	async createPosProduct(productDetails) {
		try {
			let result = {};
			const venuePosConfigDetails = await this.fetchVenuePOSMigrationDetails()
			const posID = venuePosConfigDetails.bar.attachedPosConfig

			switch (posID) {
				case 1:
					const posKounta = new kountaApiHandler(this.barId)
					await posKounta.LOAD_POS_DETAILS_FUNCTION
					result = posKounta.createKountaProducts(productDetails)
					break;
			}
			return result;

		} catch (e) {
			console.log(e)
		}
	}

	// keeping this for product update
	async updatePosProduct(productDetails) {
		try {
			let result = {};
			const venuePosConfigDetails = await this.fetchVenuePOSMigrationDetails()
			const posID = venuePosConfigDetails.bar.attachedPosConfig

			switch (posID) {
				case 1:
					const posKounta = new kountaApiHandler(this.barId)
					await posKounta.LOAD_POS_DETAILS_FUNCTION
					result = posKounta.updateKountaProducts(productDetails)
					break;
			}
			return result;

		} catch (e) {
			console.log(e)
		}
	}

	async updatePosProductExtras(productExtraDetails) {
		try {
			let result = {};
			const venuePosConfigDetails = await this.fetchVenuePOSMigrationDetails()
			const posID = venuePosConfigDetails.bar.attachedPosConfig

			switch (posID) {
				case 1:
					const posKounta = new kountaApiHandler(this.barId)
					await posKounta.LOAD_POS_DETAILS_FUNCTION
					result = posKounta.updateKountaProductsExtras(productExtraDetails)
					break;
			}
			return result;

		} catch (e) {
			console.log(e)
		}
	}

	// not feasible option with kounta as they have option set over options
	async addPosProductExtras(productExtraDetails) {
		try {
			let result = {};
			const venuePosConfigDetails = await this.fetchVenuePOSMigrationDetails()
			const posID = venuePosConfigDetails.bar.attachedPosConfig

			switch (posID) {
				case 1:
					const posKounta = new kountaApiHandler(this.barId)
					await posKounta.LOAD_POS_DETAILS_FUNCTION
					result = posKounta.addKountaProductExtras(productExtraDetails)
					break;
			}
			return result;

		} catch (e) {
			console.log(e)
		}
	}

	async handlePosOrder(orderStateDetails, whichState, orderCurrentStateDetails, isStillConfiguring = true) {
		try {
			let result = {};
			const venuePosConfigDetails = await this.fetchVenuePOSMigrationDetails()
			const posID = venuePosConfigDetails.bar.attachedPosConfig

			switch (posID) {
				case 1:
					const posKounta = new kountaApiHandler(this.barId)
					await posKounta.LOAD_POS_DETAILS_FUNCTION
					if (isStillConfiguring)
						result = await posKounta.configuringKountaPosOrder(orderCurrentStateDetails, orderStateDetails, whichState)
					else
						result = await posKounta.submitKountaOrder(orderCurrentStateDetails)

					break;

				// case 3:
				// 	const posDoshii = new doshiiApiHandler(this.barId)
				// 	result = posDoshii.submitKountaOrder(orderDetails)
				// 	break;
			}
			return result;

		} catch (e) {
			console.log(e)
		}
	}

	async handlePosOrderRefund(posOrderID, refundAmount, refundedItems, stripeRefundsData) {
		try {
			let result = {};
			const venuePosConfigDetails = await this.fetchVenuePOSMigrationDetails()
			const posID = venuePosConfigDetails.bar.attachedPosConfig

			switch (posID) {
				case 1:
					const posKounta = new kountaApiHandler(this.barId)
					await posKounta.LOAD_POS_DETAILS_FUNCTION
					result = await posKounta.processKountaPosOrderRefund(posOrderID, refundAmount, refundedItems, stripeRefundsData)
					break;
			}
			return result;

		} catch (e) {
			console.log(e)
		}
	}

	async submitPosOrder(orderCurrentStateDetails, orderID) {
		try {
			await this.handlePosOrder(null, null, orderCurrentStateDetails, false)
				.then(posResponse => {
						order.update({posOrderId: posResponse}, {where: {id: orderID}})
					}
				)
		} catch (e) {
			console.log(e)
		}
	}

	async intoxicatePosOrder(posOrderID) {
		return await this.alterPosOrderStatus(posOrderID, 'Intoxicated')
	}

	async alterPosOrderStatus(posOrderID, status) {
		try {
			let result = {};
			const venuePosConfigDetails = await this.fetchVenuePOSMigrationDetails()
			const posID = venuePosConfigDetails.bar.attachedPosConfig

			switch (posID) {
				// Kounta has no intoxication policy hence this will not function
				case 1:
					const posKounta = new kountaApiHandler(this.barId)
					await posKounta.LOAD_POS_DETAILS_FUNCTION
					result = await posKounta.updateKountaOrderStatus(posOrderID, status, false)

					break;

				// case 3:
				// 	const posDoshii = new doshiiApiHandler(this.barId)
				// 	result = posDoshii.submitKountaOrder(orderDetails)
				// 	break;

				default:
					const orderDetails = await order.findByPk(posOrderID)
					if (orderDetails) {
						result = await this.pushNotificationForOrders(orderDetails, status)
						const updateOrderData = {
							orderStatus: status,
							updatedAt: new Date()
						}
						switch (status) {
							case 'Preparing':
								updateOrderData['PreparingStartTime'] = new Date();
								break;
							case 'Pickup':
								updateOrderData['ReadyTime'] = new Date();
								break;
							case 'Pickedup':
								updateOrderData['PickedupTime'] = new Date();
								break;
							case 'NotPickedup':
								updateOrderData['isCanceled'] = 'Yes'
								break;
						}
						await orderDetails.update(updateOrderData)
					} else
						result = 'No Details found for this order, please try again.'
			}
			return result;

		} catch (e) {
			console.log(e)
		}
	}

	async alterOrderStatusWithoutPOS(posOrderID, status) {
		try {
			const orderDetails = await order.findByPk(posOrderID)
			if (orderDetails) {
				await this.pushNotificationForOrders(orderDetails, status)
				const updateOrderData = {
					orderStatus: status,
					updatedAt: new Date()
				}
				switch (status) {
					case 'Preparing':
						updateOrderData['PreparingStartTime'] = new Date();
						break;
					case 'Pickup':
						updateOrderData['ReadyTime'] = new Date();
						break;
					case 'Pickedup':
						updateOrderData['PickedupTime'] = new Date();
						break;
					case 'NotPickedup':
						updateOrderData['isCanceled'] = 'Yes'
						break;
				}
				await orderDetails.update(updateOrderData)
			}
		} catch (e) {
			console.log(e)
		}
	}
}

class POSKounta extends POS {
	constructor(barId) {
		super(barId)
		this.companyId = null
		this.siteId = null

		this.supportedOptions = [
			'cash', 'visa', 'mastercard', 'eftpos', 'amex', 'diners', 'giftcard', 'loyalty', 'credit',
			'crypto', 'directdeposit', 'cheque', 'alipay', 'wechatpay', 'zip', 'moto', 'paypal', 'other'
		];

		this.LOAD_POS_DETAILS_FUNCTION = this.fetchKountaConstraints()
		this.LOAD_POS_DETAILS_FROM_VENUE_TABLE_FUNCTION = this.fetchVenueKountaConstraints()

		this.SERVER_KOUNTA_REDIRECT_URL = null
		this.CLIENT_ID = null
		this.CLIENT_SECRET = null
		this.SHARED_SIGNATURE = null
	}

	async updateKountaOrderStatus(id, status, fromPos) {
		let posStatus, localStatus;
		if (fromPos) {
			const orderStatusConversion = {
				ON_HOLD: 'Pickup',
				PENDING: 'New',
				REJECTED: 'NotPickedup',
				COMPLETE: 'Pickedup',
			}
			localStatus = orderStatusConversion[status] || 'Preparing'
			posStatus = status

		} else {
			const orderStatusConversion = {
				// New: 'SUBMITTED',
				// To handle pass thru printing order must be in either on_hold or pending.
				New: 'PENDING',
				Preparing: 'PENDING',

				Pickedup: 'COMPLETE',
				Pickup: 'ON_HOLD',

				NotPickedup: 'REJECTED',
				Intoxicated: 'REJECTED',
			}
			posStatus = orderStatusConversion[status];
			localStatus = status
		}

		const orderDetails = await order.findOne({
			where: {
				[Op.or]: [
					{posOrderId: id},
					{id: id}
				]
			}
		})
		if (orderDetails) {
			const updateOrderData = {
				orderStatus: localStatus,
				updatedAt: new Date(),
				posOrderStatus: posStatus
			}
			if (posStatus === 'NotPickedup' || localStatus === 'NotPickedup')
				updateOrderData['isCanceled'] = 'Yes'

			switch (localStatus) {
				case 'Preparing':
					updateOrderData['PreparingStartTime'] = new Date();
					break;
				case 'Pickup':
					updateOrderData['ReadyTime'] = new Date();
					break;
				case 'Pickedup':
					updateOrderData['PickedupTime'] = new Date();
					break;
				case 'Intoxicated':
					updateOrderData['intoxicatedDate'] = new Date();
					break;
			}
			await orderDetails.update(updateOrderData)

			if (localStatus !== 'Intoxicated') {
				await this.pushNotificationForOrders(orderDetails, localStatus)
			}

			if (localStatus !== 'Intoxicated' && !fromPos && orderDetails.posOrderId) {
				let registerId
				const posKountaOrderDetails = await this.fetchApi(this.URL_CONVERSION.KOUNTA_VIEW_ORDER_URL(orderDetails.posOrderId))
				const posKountaAllRegisters = await this.fetchApi(this.URL_CONVERSION.KOUNTA_LIST_REGISTER_URL())
				for (const posKountaAllRegister of posKountaAllRegisters) {
					const regDetails = await this.fetchApi(this.URL_CONVERSION.KOUNTA_VIEW_REGISTER_URL(posKountaAllRegister.id))
					if (posKountaOrderDetails.staff_member_id === regDetails.current_staff_member.id) {
						registerId = posKountaAllRegister.id
						break
					}
				}

				await this.putApi(this.URL_CONVERSION.KOUNTA_VIEW_ORDER_URL(orderDetails.posOrderId), {
					status: posStatus,
					register_id: registerId
				})
			}
		}
		return orderDetails
	}

	async fetchKountaConstraints() {
		const posDetail = await barPOSMigration.findOne({
			include: [{
				model: POSconfig,
				required: true,
				where: {posName: 'KOUNTA'}
			}],
			where: {barId: this.barId}
		})
		if (posDetail) {
			this.CLIENT_ID = posDetail.pos_conf.posConf.client_id
			this.CLIENT_SECRET = posDetail.pos_conf.posConf.client_secret
			this.SHARED_SIGNATURE = posDetail.pos_conf.posConf.x_signature

			this.SERVER_KOUNTA_REDIRECT_URL = posDetail.pos_conf.posConf.redirect_uri

			this.companyId = posDetail.venuePosConfigDetails.company_id
			this.siteId = posDetail.venuePosConfigDetails.site_id
		}
		return posDetail
	}

	async fetchVenueKountaConstraints() {

		const pos = await POSconfig.findOne({
			where: {posName: 'KOUNTA'}
		})
		this.CLIENT_ID = pos.posConf.client_id
		this.CLIENT_SECRET = pos.posConf.client_secret

		const posDetail = await barPOSMigration.findOne({
			where: {barId: this.barId}
		})
		this.companyId = posDetail.venuePosConfigDetails.company_id
		this.siteId = posDetail.venuePosConfigDetails.site_id

		return posDetail
	}

	async verifyWebhookAndSetConfigurations(receivedSignatureHeader, sharedSignature) {
		const computedSignature = crypto.createHmac("sha256", sharedSignature).update(receivedSignatureHeader).digest("hex");

		const computedSignatureBuffer = Buffer.from(computedSignature, 'hex');
		const retrievedSignatureBuffer = Buffer.from(receivedSignatureHeader, 'hex');

		return crypto.timingSafeEqual(computedSignatureBuffer, retrievedSignatureBuffer);
	}

	async createKountaProducts(productDetail) {
		try {

			const kountaProductDetails = {
				name: productDetail.name,
				image: productDetail.avatar ? productDetail.avatar : undefined,
				description: productDetail.description ? productDetail.description : undefined,
				unit_price: parseFloat(productDetail.price),
			}
			const serverResponse = await this.postApi(this.URL_CONVERSION.KOUNTA_BASE_PRODUCTS_URL(), kountaProductDetails);
			// remember to convert any id with different key like abcId to id so the callback works
			const posID = serverResponse.headers.location.split('/').pop().replace('.json', '')

			// For subcategory [Kounta's optionsets]
			// Only any category that is synced via webhook will be considered
			if (productDetail.sub_category && productDetail.sub_category.posID) {
				await this.putApi(this.URL_CONVERSION.KOUNTA_ADD_CATEGORY_PRODUCT_URL(productDetail.sub_category.posID), [parseInt(posID)]);
			}
			return posID

		} catch (e) {
			console.log(e)
		}
	}

	async configuringKountaPosOrder(orderCurrentStateDetails, orderStateDetails, configuringState) {
		try {
			switch (configuringState) {
				case 'ORDER':
					const {pickupCode, orderServiceType, orderNo, id, createdAt, promocode_amount} = orderStateDetails
					const tableCode = orderStateDetails.dataValues.tableCode
					console.log(`table code: ${tableCode}`)
					const orderDetails = {
						"status": "PENDING",
						"notes": `"MYTAB -"${orderStateDetails.pickupCode ? orderStateDetails.pickupCode : "TABLE: " + orderStateDetails.tableCode}/${orderStateDetails.orderNo}`,
						"site_id": parseInt(this.siteId),
						"order_type": orderStateDetails.orderServiceType === 'TABLE' ? 'Dine in' : 'Pickup',
						// "customer_id": orderStateDetails.userID,
						"lines": [],
						"payments": [],
						"callback_uri": `${redirectBaseUrl}/pos/webhooks/kounta/update_via_order_status/?id=${orderStateDetails.id}`,
						"complete_when_paid": false,
						"pass_thru_printing": true,
						"placed_at": orderStateDetails.createdAt,
						"fulfil_at": moment().tz('Australia/Perth').subtract(1, 'days').format('YYYY-MM-DDT13:00:00+00:00'),
					}
					if (orderStateDetails.promocode_amount)
						orderDetails.price_fixed_variation = -orderStateDetails.promocode_amount

					return orderDetails

				case 'ITEMS':
					const modifiers = []
					const {price, posID, quantity, specialRequest, productItemExtras} = orderStateDetails
					for (const productItemExtra of productItemExtras) {
						if (productItemExtra && productItemExtra.posID !== '')
							modifiers.push(parseInt(productItemExtra.posID))
					}
					orderCurrentStateDetails.lines.push({
						"product_id": parseInt(posID),
						"quantity": parseInt(quantity),
						// This price should be excluding tax this will create a mess if there is a tax variation
						// "unit_price": parseFloat(price),
						"modifiers": modifiers,
						"notes": specialRequest ? `Special Request: ${specialRequest}` : '',
					})
					return orderCurrentStateDetails

				case 'TRANSACTION':
					const methodId = await this.fetchApi(this.URL_CONVERSION.KOUNTA_BASE_PAYMENT_METHOD_URL())
					const payableMethod = methodId.filter(({name}) => name.toUpperCase() === 'MYTAB')
					const totalAmount = (orderStateDetails.subTotal - orderStateDetails.promocodeAmount).toFixed(2)
					// using first payment method
					orderCurrentStateDetails.payments = [{
						"method_id": payableMethod[0].id,
						"amount": parseFloat(totalAmount),
						"ref": orderStateDetails.reference
					}]
					return orderCurrentStateDetails

				default:
					break;

				// No variants in case of kounta
				// case 'VARIANTS':
				// 	break;

			}

			return this.posSubmissionDetails

		} catch (e) {
			console.log(e)
		}
	}

	async submitKountaOrder(orderCurrentStateDetails) {
		try {
			const serverResponse = await this.postApi(this.URL_CONVERSION.KOUNTA_BASE_ORDER_URL(), orderCurrentStateDetails);
			return serverResponse.headers.location.split('/').pop().replace('.json', '')

		} catch (e) {
			console.log(e)
		}
	}

	async processKountaPosOrderRefund(posOrderId, refundAmount, refundedItems, stripeRefundsData) {
		try {
			const posKountaOrderDetails = await this.fetchApi(this.URL_CONVERSION.KOUNTA_VIEW_ORDER_URL(posOrderId))
			const requestPayload = {
				"status": "COMPLETE",
				"notes": "Refunded via MyTab",
				"register_id": posKountaOrderDetails.register_id,
				"site_id": posKountaOrderDetails.site_id,
				"lines": [],
				"payments": [],
				"original_order_id": parseInt(posOrderId),
			}
			requestPayload.payments.push({
				method_id: posKountaOrderDetails.payments[0].method.id,
				ref: stripeRefundsData.id,
				amount: -refundAmount
			})

			// posKountaOrderDetails.payments.map(payment => {
			// 	requestPayload.payments.push({...payment, amount: -refundAmount})
			// })
			refundedItems.map(refundedItem => {
				requestPayload.lines.push({
					"product_id": parseInt(refundedItem.posID),
					"quantity": parseInt(refundedItem.quantity),
					// "unit_price": parseFloat(refundedItem.price),
				})
			})
			await this.postApi(this.URL_CONVERSION.KOUNTA_BASE_ORDER_URL(), requestPayload);

		} catch (e) {
			console.log(e)
		}
	}

	async updateKountaProductsExtras(productExtraDetails) {
		try {
			let result = {};
			if (productExtraDetails.posID) {
				const kountaProductExtrasDetails = {
					name: productExtraDetails.extraItem,
					unit_price: parseFloat(productExtraDetails.price),
				}
				result = await this.putApi(this.URL_CONVERSION.KOUNTA_UPDATE_PRODUCT_URL(productExtraDetails.posID), kountaProductExtrasDetails);
			}
			return result

		} catch (e) {
			if (e.response && e.response.status === 401) {
				console.log(`Data extras ${productExtraDetails.posID} has been marked as gone by Kounta so is being removed from Mytab database`)
				// productExtras.destroy({where: {id: productExtraDetails.id}})
				// Can use the deletedAt but it is not being properly used throughout POS so removing the record for now
				// productDetail.update({isDeleted: 'Yes'})
			}
			console.log(e)
		}
	}

	async updateKountaProducts(productDetail) {
		try {
			let result = {};
			if (productDetail.posID) {
				// KOUNTA DON'T ACCEPT THESE PARAMETERS SO THAT WAY ONLY THING THAT GETS UPDATED IS DESCRIPTION and SUBCATEGORY
				const kountaProductDetails = {
					name: productDetail.name,
					description: productDetail.description ? productDetail.description : undefined,
					unit_price: parseFloat(productDetail.price),
					image: productDetail.avatar ? productDetail.avatar : undefined,
				}
				result = await this.putApi(this.URL_CONVERSION.KOUNTA_UPDATE_PRODUCT_URL(productDetail.posID), kountaProductDetails);

				// For subcategory [Kounta's optionsets]
				// Only any category that is synced via webhook will be considered
				if (productDetail.sub_category && productDetail.sub_category.posID) {
					await this.putApi(this.URL_CONVERSION.KOUNTA_ADD_CATEGORY_PRODUCT_URL(productDetail.sub_category.posID), [parseInt(productDetail.posID)]);
				}
			}
			return result

		} catch (e) {
			if (e.response.status === 401) {
				console.log(`Data ${productDetail.posID} has been marked as gone by Kounta so is being removed from Mytab database`)
				// productExtras.destroy({where: {productID: productDetail}})
				// productDetail.destroy()
				// Can use the deletedAt but it is not being properly used throughout POS so removing the record for now
				// productDetail.update({isDeleted: 'Yes'})
			}
			console.log(e)
		}
	}
}

class kountaApiHandler extends POSKounta {
	constructor(barId) {
		super(barId)
		this.KOUNTA_BASE_URL = env.KOUNTA_API_BASE_URL
		this.KOUNTA_AUTHORIZATION_BASE_URL = env.KOUNTA_AUTHORIZATION_BASE_URL

		// this.companyId = companyId
		// this.siteId = siteId

		this.URL_CONVERSION = {
			AUTHORIZATION_URL: `${this.KOUNTA_AUTHORIZATION_BASE_URL}/authorize`,
			ACCESS_TOKEN_URL: `token`,

			KOUNTA_BASE_PRODUCTS_URL: () => `companies/${this.companyId}/sites/${this.siteId}/products`,
			KOUNTA_VIEW_PRODUCT_URL: (posId) => `companies/${this.companyId}/sites/${this.siteId}/products/${posId}`,
			KOUNTA_UPDATE_PRODUCT_URL: (posId) => `companies/${this.companyId}/products/${posId}`,

			KOUNTA_LIST_PRODUCT_EXTRAS_URL: () => `companies/${this.companyId}/optionsets`,
			KOUNTA_VIEW_PRODUCT_EXTRA_URL: (posId) => `companies/${this.companyId}/sites/${this.siteId}/optionsets/${posId}`,

			KOUNTA_LIST_CATEGORIES_URL: () => `companies/${this.companyId}/categories`,
			KOUNTA_ADD_CATEGORY_PRODUCT_URL: (posId) => `companies/${this.companyId}/categories/${posId}/products.json`,

			KOUNTA_BASE_ORDER_URL: () => `companies/${this.companyId}/orders/`,
			KOUNTA_LIST_ORDER_URL: () => `companies/${this.companyId}/sites/${this.siteId}/orders`,
			KOUNTA_VIEW_ORDER_URL: (posId) => `companies/${this.companyId}/orders/${posId}`,

			KOUNTA_BASE_WEBHOOK_URL: () => `companies/${this.companyId}/webhooks/`,
			KOUNTA_VIEW_WEBHOOK_URL: (webhookId) => `companies/${this.companyId}/webhooks/${webhookId}`,

			KOUNTA_BASE_PAYMENT_METHOD_URL: () => `companies/${this.companyId}/payment_methods/`,

			KOUNTA_LIST_REGISTER_URL: () => `companies/${this.companyId}/sites/${this.siteId}/registers`,
			KOUNTA_VIEW_REGISTER_URL: (regId) => `companies/${this.companyId}/registers/${regId}`,
		}

		this.instance = axios.create({
			baseURL: this.KOUNTA_BASE_URL,
			timeout: 10000,
			headers: {
				'Content-Type': 'application/json'
			},
			// validateStatus: status => {
			// 	return status >= 200 && status < 300;
			// },
		});
	}

	async fetchApi(GET_URL, URL_PARAMS = {}) {
		const finalApiResponse = await this.getApi(GET_URL, URL_PARAMS)
		return finalApiResponse.data ? finalApiResponse.data : {};
	}

	async getApi(GET_URL, URL_PARAMS = {}) {
		await this._kountaCheckAccessTokenAndRefresherTokenHandler()
		return this.instance.get(GET_URL, URL_PARAMS)
	}

	async deleteApi(DELETE_URL, URL_PARAMS = {}) {
		await this._kountaCheckAccessTokenAndRefresherTokenHandler()
		return this.instance.delete(DELETE_URL, URL_PARAMS)
	}

	async postApi(POST_URL, PAYLOAD) {
		await this._kountaCheckAccessTokenAndRefresherTokenHandler()
		return await this.instance.post(POST_URL, PAYLOAD)
	}

	async putApi(POST_URL, PAYLOAD) {
		await this._kountaCheckAccessTokenAndRefresherTokenHandler()
		return await this.instance.put(POST_URL, PAYLOAD)
	}

	async _handleUpdatedAccessToken(payload) {
		try {
			const apiResponse = await this.instance.post(this.URL_CONVERSION.ACCESS_TOKEN_URL, payload)
			const venuePOS = await barPOSMigration.findOne({where: {barID: this.barId}})
			if (venuePOS && apiResponse) {
				this.instance.defaults.headers.common['Authorization'] = `${apiResponse.data.token_type} ${apiResponse.data.access_token}`
				await venuePOS.update({venuePosConfigDetails: {...venuePOS.venuePosConfigDetails, ...apiResponse.data}})
			}
			return venuePOS

		} catch (e) {
			console.log(e)
		}
	}

	async _handleWebhookData(webhookPayload) {
		try {
			await this.instance.post(this.URL_CONVERSION.KOUNTA_BASE_WEBHOOK_URL(), {
				...webhookPayload,
				"format": "json",
				"headers": {
					"X-barId": this.barId
				},
			})
		} catch (e) {
			console.log(e)
		}
	}

	async generateAccessToken(CLIENT_ID, CLIENT_SECRET, SERVER_KOUNTA_REDIRECT_URL, CODE) {
		const payload = {
			"client_id": CLIENT_ID,
			"client_secret": CLIENT_SECRET,
			"redirect_uri": SERVER_KOUNTA_REDIRECT_URL,
			"code": CODE,
			"grant_type": "authorization_code"
		}
		await this._handleUpdatedAccessToken(payload)
		await this.updateVenuePosHandle(1, 'POS_CONFIGURED')
		await this.createKountaWebhooks()
	}

	async createKountaWebhooks() {
		this.removeKountaWebhooks()

		const payload = [{
			"topic": "products/deleted",
			"address": `${redirectBaseUrl}/pos/webhooks/kounta/remove_product/`,
		}, {
			"topic": "products/created",
			"address": `${redirectBaseUrl}/pos/webhooks/kounta/handle_products/`,
		}, {
			"topic": "products/updated",
			"address": `${redirectBaseUrl}/pos/webhooks/kounta/handle_products/`,
		}, {
			"topic": "categories/updated",
			"address": `${redirectBaseUrl}/pos/webhooks/kounta/handle_category/`,
		}, {
			"topic": "categories/created",
			"address": `${redirectBaseUrl}/pos/webhooks/kounta/handle_category/`,
		}, {
			"topic": "categories/deleted",
			"address": `${redirectBaseUrl}/pos/webhooks/kounta/remove_category/`,
		}, {
			"topic": "orders/completed",
			"address": `${redirectBaseUrl}/pos/webhooks/kounta/update_order_status/`,
		},]
		for (const payloadElement of payload) {
			await this._handleWebhookData(payloadElement)
		}
	}

	async _kountaCheckAccessTokenAndRefresherTokenHandler() {
		let payload;

		await barPOSMigration.findOne({
			where: {barId: this.barId}
		}).then(async (venuePosStatus) => {
			const {expires_at, started_at} = venuePosStatus.venuePosConfigDetails

			if (!moment().isBetween(started_at, expires_at)) {
				payload = {
					"client_id": this.CLIENT_ID,
					"client_secret": this.CLIENT_SECRET,
					"refresh_token": venuePosStatus.venuePosConfigDetails.refresh_token,
					"grant_type": "refresh_token"
				}
				await this._handleUpdatedAccessToken(payload);
			} else {
				venuePosStatus.reload()
				let {access_token, token_type} = venuePosStatus.venuePosConfigDetails
				this.instance.defaults.headers.common['Authorization'] = `${token_type} ${access_token}`
			}
			return venuePosStatus.venuePosConfigDetails.access_token
		})
	}

	async removeKountaWebhooks() {
		try {
			const existingHooks = await this.getApi(this.URL_CONVERSION.KOUNTA_BASE_WEBHOOK_URL())
			existingHooks ? existingHooks.data.map((hook) => this.deleteApi(this.URL_CONVERSION.KOUNTA_VIEW_WEBHOOK_URL(hook.id))) : undefined

		} catch (e) {
			console.log(e)
		}
	}

}

class routeHandlers {

	listPOS = async (req, res, next) => {
		try {
			const availablePOS = await POSconfig.findAll({
				attributes: ['id', 'posName', 'posConf'],
				where: {id: {[Op.gt]: 0}}
			})
			if (availablePOS)
				res.status(200).json({success: 1, message: '', data: availablePOS});
			else
				res.status(200).json({success: 0, message: '', data: availablePOS});

		} catch (error) {
			console.log(error);
			return res.status(500).json({success: 0, message: 'Something went wrong, please try again', data: {}});
		}
	}

	resetVenueToLastPosStateRoute = async (req, res, next) => {
		try {
			const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
			const barID = sessionData.barID

			const response = await new POS(barID).resetVenueToLastPosState()
			res.status(200).json({success: 1, message: 'POS reset successfully.', data: response});

		} catch (error) {
			console.log(error);
			return res.status(500).json({success: 0, message: 'Problem resetting venue to last state', data: error});
		}
	}

	redirectKountaInfo = async (req, res) => {
		try {
			const {code, state} = req.query
			const venueKounta = new kountaApiHandler(state)
			await venueKounta.LOAD_POS_DETAILS_FUNCTION
			const notDeclined = req.query.error_description && req.query.error_description

			if (!notDeclined) {
				await venueKounta.generateAccessToken(venueKounta.CLIENT_ID, venueKounta.CLIENT_SECRET, venueKounta.SERVER_KOUNTA_REDIRECT_URL, code)
				// changing configuration status of attached POS to kounta of the Venue
				await bar.update({attachedPosConfig: 1}, {where: {id: state}})

				return res.status(200).send('<html lang="en">Congratulations, you have now successfully integrated your MyTab Venue account with your chosen POS system, Kounta. <br/>To exit this page and continue using the application, please press <strong>DONE</strong>. <br/><br/>Thank you, <br/><strong>MyTab Venue Support Team</strong>.</html>')
			} else {
				await venueKounta.updateVenuePosHandle(1, 'POS_CONFIGURATION_DECLINED')
				return res.status(401).send(`Your request has been failed with reason "<b> ${notDeclined} </b>", please move back complete finalise process to move forward with POS integration.`)
			}

		} catch (error) {
			console.log(error)
			return res.status(500).send('Something went wrong, please contact administrator and show them this code PYTFAIL');
		}
	}

	kountaGettingStarted = async (req, res, next) => {
		try {
			const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
			const barID = sessionData.barID
			const posKounta = new kountaApiHandler(barID)

			const pDetails = await posKounta.LOAD_POS_DETAILS_FUNCTION
			const reqBody = req.body

			if (reqBody.company_id && reqBody.site_id) {
				await pDetails.update({venuePosConfigDetails: reqBody})
				await pDetails.reload()

				const posSpecificConfig = pDetails.pos_conf.posConf

				if (posSpecificConfig) {
					const clientId = posKounta.CLIENT_ID
					const redirectUri = posKounta.SERVER_KOUNTA_REDIRECT_URL

					res.status(200).json({
						success: 1,
						message: 'POS configuration details updated successfully.',
						data: pDetails,
						redirectUrl: `${posKounta.URL_CONVERSION.AUTHORIZATION_URL}?client_id=${clientId}&redirect_uri=${redirectUri}&state=${barID}&response_type=code`
					});
				} else {
					res.status(200).json({
						success: 0,
						message: 'Some POS configuration details are missing, please contact the administrator.',
						data: {},
						redirectUrl: ''
					});
				}
			} else {
				return res.status(200).json({
					success: 0,
					message: 'Configuration details are required',
					data: {}
				});
			}

		} catch (error) {
			console.log(error);
			if (error instanceof SyntaxError) {
				return res.status(500).json({success: 0, message: 'Improper JSON syntax in configuration', data: {}});
			} else {
				return res.status(500).json({success: 0, message: 'Something went wrong, please try again', data: {}});
			}
		}
	}

}


class webHookHandler {
	handleKountaProductWebhook = async (req, res) => {
		try {
			console.time("ProductUpdateKounta");
			const barID = req.barID
			let productExtras = req.body.option_sets ? req.body.option_sets : []

			const productInstance = new productPosHandling(barID)
			// this is needed to protect circular import in POS and Product file
			productInstance.posConfigured = new kountaApiHandler(barID)
			await productInstance.posConfigured.LOAD_POS_DETAILS_FUNCTION

			await productInstance.upsertKountaProducts(req.body)
			if (productExtras.length > 0) {
				const productExtrasMapped = productExtras.map(productExtraId => {
					return {
						num_options: 1,
						id: productExtraId,
					}
				})

				const singleProductExtraUpdateParameter = {
					singularUpdate: true,
					productPosId: req.body.id,
					existingExtras: productExtras,
				}

				productInstance.upsertKountaProductExtras(productExtrasMapped, singleProductExtraUpdateParameter)
			}

			console.timeEnd("ProductUpdateKounta");
			res.status(200).send();

		} catch (error) {
			console.log(error)
			return res.status(500).send(error);
		}
	}

	handleProductDeletionWebhook = async (req, res) => {
		try {
			console.time("ProductRemoveKounta");
			let {id} = req.body
			await productModel.findOne({where: {posID: id}})
				.then((productRemoval) => {
					if (productRemoval) {
						productExtras.update({isDeleted: 'Yes'}, {where: {productID: productRemoval.id}})
						productRemoval.update({isDeleted: 'Yes'})
					}
				})
			console.timeEnd("ProductRemoveKounta");
			return res.status(200).send();

		} catch (error) {
			console.log(error)
			return res.status(500).send();
		}
	}

	handleCategoryDeletionWebhook = async (req, res) => {
		try {
			console.time("ProductRemoveKounta");
			let {id} = req.body
			await subcategory.findOne({where: {posID: id}})
				.then(categoryRemoval => {
					if (categoryRemoval) {
						// Will need to confirm if category is removed products gets removed
						// this is kept because without category product doesn't exists
						productModel.findOne({where: {subCategoryID: categoryRemoval}})
							.then(productRemoval => {
								if (productRemoval) {
									productExtras.destroy({where: {productID: productRemoval}})
									productRemoval.destroy()
								}
							})
						categoryRemoval.destroy()
					}
				})
			console.time("ProductRemoveKounta");
			return res.status(200).send();

		} catch (error) {
			console.log(error)
			return res.status(500).send();
		}
	}

	handleKountaOrderStatusWebhook = async (req, res) => {
		try {
			let barID
			let status = req.body.status

			let id
			id = req.query.id
			if (!id) {
				id = req.body.id
				barID = req.barID
			} else {
				const orderDetails = await order.findOne({where: {id: id}})
				barID = orderDetails.barID
			}

			const kountaConfigured = new kountaApiHandler(barID)
			await kountaConfigured.LOAD_POS_DETAILS_FUNCTION

			await kountaConfigured.updateKountaOrderStatus(id, status, true)

			return res.status(200).send();

		} catch (error) {
			console.log(error)
			return res.status(500).send();
		}
	}

	handleKountaCategoryWebhook = async (req, res) => {
		try {
			let categoryList = req.body

			// uncomment these if: dont remove a category if there are products in it
			// if (req.body.products.count > 0) {
			// return res.status(402).send();
			// }
			const categoryInstance = new categoryPosHandling()
			await categoryInstance.upsertKountaCategoryList(categoryList)

			return res.status(200).send();

		} catch (error) {
			console.log(error)
			return res.status(500).send();
		}
	}

	handleKountaOrderStatusUpdateWebhook = async (req, res) => {
		try {
			let categoryList = req.body

			// uncomment these if: dont remove a category if there are products in it
			// if (req.body.products.count > 0) {
			// return res.status(402).send();
			// }
			const categoryInstance = new categoryPosHandling()
			await categoryInstance.upsertKountaCategoryList(categoryList)

			return res.status(200).send();

		} catch (error) {
			console.log(error)
			return res.status(500).send();
		}
	}
}

class posUtils {
	constructor(barID) {
		this.posConfigured = new POS(barID);
		this.barID = barID;
		this.optedPosId = null;
	}

	async syncPosSystem() {
		const attachedPos = await this.posConfigured.fetchOptedPosConfig()
		this.optedPosId = attachedPos.attachedPosConfig

		switch (this.optedPosId) {
			// Kounta POS
			case 1:
				return await this.syncViaKounta()

			default:
				return {success: 0, message: 'POS configuration error.', data: {}}
		}
	}

	async syncViaKounta() {
		try {
			const posConfiguredKounta = new kountaApiHandler(this.barID)
			await posConfiguredKounta.LOAD_POS_DETAILS_FUNCTION

			// Below are functions to pull data from POS to Our database
			let categories = await posConfiguredKounta.fetchApi(posConfiguredKounta.URL_CONVERSION.KOUNTA_LIST_CATEGORIES_URL());
			if (categories && categories.length) {
				const categoryInstance = new categoryPosHandling()
				await categoryInstance.upsertKountaCategoryList(categories)
			}
			const productInstance = new productPosHandling(this.barID)
			// this is to prevent circular import in POS and product
			productInstance.posConfigured = posConfiguredKounta

			let products, productsArr = [];
			let hasNextPage = posConfiguredKounta.URL_CONVERSION.KOUNTA_BASE_PRODUCTS_URL();
			while (hasNextPage) {
				products = await posConfiguredKounta.getApi(hasNextPage);
				if (products.data)
					productsArr = [...productsArr, ...products.data]
				hasNextPage = products.headers['x-next-page']
			}
			if (productsArr && productsArr.length) {
				await productInstance.upsertKountaProducts(productsArr)
			}

			let productExtras, productExtrasArr = [];
			hasNextPage = posConfiguredKounta.URL_CONVERSION.KOUNTA_LIST_PRODUCT_EXTRAS_URL();
			while (hasNextPage) {
				productExtras = await posConfiguredKounta.getApi(hasNextPage);
				if (productExtras.data)
					productExtrasArr = [...productExtrasArr, ...productExtras.data]
				hasNextPage = products.headers['x-next-page']
			}
			if (productExtrasArr && productExtrasArr.length) {
				await productInstance.upsertKountaProductExtras(productExtrasArr)
			}

			let orderItems = await posConfiguredKounta.fetchApi(posConfiguredKounta.URL_CONVERSION.KOUNTA_LIST_ORDER_URL());
			if (orderItems && orderItems.length) {
				const orderInstance = new orderPosHandling(this.barID)
				orderInstance.posConfigured = posConfiguredKounta
				await orderInstance.upsertKountaOrderItems(orderItems)
			}

			// Below are functions to add any data tha has been in our system and not synced to POS
			const {
				unSyncedProducts,
				ExistingSyncedProducts
			} = await productInstance.fetchUnSyncedProducts(this.optedPosId, this.barID)
			// for (let unSyncedProduct of unSyncedProducts)
			// await this.posConfigured.createPosProduct(unSyncedProduct)
			// 	.then(productPosUpdated => {
			// 		if (productPosUpdated)
			// 			unSyncedProduct.update({posID: productPosUpdated})
			// 	})

			for (let ExistingSyncedProduct of ExistingSyncedProducts)
				await this.posConfigured.updatePosProduct(ExistingSyncedProduct)

			return {success: 1, message: 'POS synchronized successfully.', data: {}}

		} catch (e) {
			console.log(e)
			return {success: 0, message: 'POS configuration error.', data: e}
		}
	}
}

// use this for Doshii POS configuration
// submitDoshiiOrder = async (requestOrderItems) => {
// 	// start doshii order logic
// 	const products = [];
// 	for (let orderItem of requestOrderItems) {
// 		let item = {};
// 		let productPrice = 0;
//
// 		let productData = await product.findOne({
// 			attributes: ['id', 'categoryID', 'subCategoryID', 'name', 'description', 'price', 'posID'],
// 			where: {
// 				id: orderItem.productID,
// 				posID: {[Op.ne]: null}
// 			},
// 			raw: true
// 		});
//
// 		if (productData) {
// 			if (productData.categoryID != categoryData.id) {
// 				continue;
// 			}
// 			// combine item special request into doshii notes
// 			orderNotes += (orderItem.specialRequest && orderItem.specialRequest.trim().length > 0) ? productData.name + ': ' + orderItem.specialRequest + '\n' : '';
// 			item.name = productData.name;
// 			item.description = productData.description;
// 			item.quantity = parseInt(orderItem.quantity);
// 			// item.unitPrice = Math.round(parseFloat(productData.price) * 100);
// 			item.posId = productData.posID;
// 			item.type = 'single';
// 			// item.totalBeforeSurcounts = Math.round(parseFloat(productData.price) * 100);
// 			// item.totalAfterSurcounts = Math.round(parseFloat(productData.price) * 100);
// 			// item.includedItems = [];
// 			item.surcounts = [];
// 			item.options = [];
//
// 			var productItemExtras = orderItem.productItemExtras;
// 			if (Array.isArray(productItemExtras) && productItemExtras.length > 0) {
// 				let variantOptions = [];
// 				for (let productItemExtra of productItemExtras) {
// 					let newOption = {posId: '', name: '', variants: []};
// 					let productExtraData = await productExtras.findOne({
// 						attributes: ['id', 'extraItem', 'price', 'productOptionposID', 'productOptionName', 'posID'],
// 						where: {
// 							id: productItemExtra.productExtrasID,
// 							posID: {[Op.ne]: null}
// 						},
// 						raw: true
// 					});
// 					if (productExtraData) {
// 						let productItemOptionIndex = variantOptions.findIndex(function (option) {
// 							return option.posId == productExtraData.productOptionposID && option.name == productExtraData.productOptionName;
// 						});
// 						// when items have multiple options
// 						if (productItemOptionIndex != -1) {
// 							newOption.posId = productExtraData.productOptionposID;
// 							newOption.name = productExtraData.productOptionName;
// 							newOption.variants.push({
// 								posId: productExtraData.posID,
// 								name: productExtraData.extraItem,
// 								price: Math.round(parseFloat(productExtraData.price) * 100)
// 							})
// 							productPrice += Math.round(parseFloat(productExtraData.price) * 100);
// 							variantOptions.push(newOption);
// 						}
// 						// when items have single option
// 						else {
// 							newOption.posId = productExtraData.productOptionposID;
// 							newOption.name = productExtraData.productOptionName;
// 							newOption.variants.push({
// 								posId: productExtraData.posID,
// 								name: productExtraData.extraItem,
// 								price: Math.round(parseFloat(productExtraData.price) * 100)
// 							})
// 							productPrice += Math.round(parseFloat(productExtraData.price) * 100);
// 							variantOptions.push(newOption);
// 						}
// 					}
// 				}
// 				item.options = variantOptions;
// 			}
// 			// if (productPrice == 0) {
// 			//   productPrice += Math.round(parseFloat(productData.price) * 100);
// 			// }
// 			productPrice += Math.round(parseFloat(productData.price) * 100);
// 			item.unitPrice = Math.round(parseFloat(productData.price) * 100);
// 			item.totalBeforeSurcounts = Math.round(parseFloat(productPrice) * parseInt(orderItem.quantity));
// 			item.totalAfterSurcounts = Math.round(parseFloat(productPrice) * parseInt(orderItem.quantity));
// 			products.push(item);
// 		}
// 	}
// 	let posOrderData = {
// 		consumer: {
// 			name: userData.fullName,
// 			email: userData.email,
// 			phone: userData.countryCode + userData.mobile
// 		},
// 		order: {
// 			type: "pickup",
// 			phase: "ordered",
// 			status: "pending",
// 			externalOrderRef: orderResponse.orderNo,
// 			manuallyProcessed: false,
// 			posCreatedAt: new Date(),
// 			items: products,
// 			surcounts: [
// 				// {
// 				//   name: 'Transaction Fees',
// 				//   type: 'absolute',
// 				//   value: Math.round(req.body.transactionFee * 100),
// 				//   amount: Math.round(req.body.transactionFee * 100)
// 				// }
// 			]
// 		},
// 		transactions: [],
// 		notes: orderNotes ? orderNotes : undefined
// 	};
// 	// add promocode in doshii surcount
// 	if (req.body.promocode_id) {
// 		const couponData = await coupons.findOne({
// 			where: {id: req.body.promocode_id}
// 		});
// 		if (couponData) {
// 			let surcountData = {
// 				name: couponData.name,
// 				type: 'percentage',
// 				value: `-${Math.round(orderResponse.promocode_amount * 100)}`,
// 				amount: `-${couponData.discount_amount}`
// 			};
// 			couponData.description ? surcountData.description = couponData.description : null;
// 			posOrderData.order.surcounts.push(surcountData);
// 		}
// 	}
// 	// end doshii order logic
//
// }
// updateDoshiiOrder = async (requestOrderItems) => {
// 	let doshiOrderDetails = await commonFunction.getOrderDetailsFromDoshii(barData.venueId, orderDetails.posOrderId);
// 	if (doshiOrderDetails.version && ['Pending', 'Accepted'].includes(orderDetails.posOrderStatus)) {
// 		let doshiiUpdateOrder = await commonFunction.updateOrderinDoshii(barData.venueId, orderDetails.posOrderId, {
// 			"status": "venue_cancelled",
// 			// "mealPhase": "ordered",
// 			"version": doshiOrderDetails.version
// 		});
// 		if (typeof doshiiUpdateOrder == 'object' && doshiiUpdateOrder.hasOwnProperty('id')) {
// 			await orderDetails.update({ posOrderStatus: 'Venue Cancelled' });
// 		}
// 	}
//
// }

module.exports = {
	POS,
	POSKounta,
	routeHandlers,
	webHookHandler,
	posUtils,
}
