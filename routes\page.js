const express = require('express')
const router = express()
const page = require('../controller/page')

router.post('/privacyPolicy', page.privacyPolicy)
router.post('/faq', page.faq)
router.post('/terms', page.terms)
router.post('/barAboutUs', page.barAboutUs)
router.post('/barPrivacyPolicy', page.barPrivacyPolicy)
router.post('/barFaq', page.barFaq)
router.post('/barTerms', page.barTerms)
router.post('/crashLogs', page.crashLogs)
router.post('/getcrashLogs', page.getcrashLogs)
// router.get('/getcrashLogs', page.getcrashLogs)
module.exports = router