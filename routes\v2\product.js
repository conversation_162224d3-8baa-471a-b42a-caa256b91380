const express = require('express')
const router = express()
var multer = require('multer')
const checkAuth = require('../../middleware/barCheckAuth')
const checkUserAuth = require('../../middleware/checkAuth')
const product = require('../../controller/v2/product')

var upload = multer({})

router.post('/getSegmentTags', checkAuth, product.getSegmentTags)
router.post('/getItemProfile', checkUserAuth, product.getItemProfile)

router.post('/getProducts', upload.array(), checkUserAuth, product.getProducts)

module.exports = router