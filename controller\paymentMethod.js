var Sequelize = require('sequelize')
const Op = Sequelize.Op

const message = require('../config/message')
const common = require('./common')
const user = require('../models/user')
const bar = require('../models/bar')

var env = require('../config/environment')
var jwt = require('jsonwebtoken');
var stripe = require('stripe')(env.stripe_secret_key)

exports.list = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  var whereClause = {
    id: userID,
    isDeleted: 'No'
  }
  try {
    user
      .findOne({
        attributes: [
          'id',
          'email',
          'fullName',
          'stripeID'
        ],
        where: whereClause
      })
      .then(async userData => {
        if (userData) {
          var userStripeID = userData.stripeID
          if (userStripeID != '') {
            // First, fetch the customer to get the default_source
            stripe.customers.retrieve(userStripeID, async (err, customer) => {
              if (err) {
                return res.status(200).send({
                  success: 0,
                  message: 'Failed to retrieve customer',
                });
              }
          
              const defaultCardId = customer.default_source;
              
              // Then, fetch all card sources
              stripe.customers.listSources(
                userStripeID,
                { object: 'card' },
                (err, cards) => {
                  if (err) {
                    return res.status(200).send({
                      success: 0,
                      message: 'Failed to retrieve cards',
                    });
                  }
          
                  const cardList = cards.data.map(card => ({
                    ...card,
                    is_default: card.id === defaultCardId,
                  }));
          
                  return res.status(200).send({
                    success: 1,
                    data: cardList,
                    message: 'Card list retrieved successfully',
                  });
                }
              );
            });
          } else {
            res.status(200).send({
              success: 0,
              message: 'Please add your payment details'
            })
          }
        } else {
          res.status(200).send({
            success: 0,
            message: 'Something went wrong!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.defaultCardSet = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  var whereClause = {
    id: userID,
    isDeleted: 'No'
  }

  try {
    user
      .findOne({
        attributes: [
          'id',
          'email',
          'fullName',
          'stripeID'
        ],
        where: whereClause
      })
      .then(async userData => {
        if (userData) {
          const userStripeID = userData.stripeID;
          const newDefaultCardId = req.body.cardid;

          stripe.customers.update(userStripeID, {
            default_source: newDefaultCardId,
          }, (err, customer) => {
            if (err) {
              return res.status(200).send({
                success: 0,
                message: 'Failed to set default card',
              });
            }            
            return res.status(200).send({
              success: 1,
              data: customer,
              message: 'Default card updated successfully',
            });
          });          
        } else {
          res.status(200).send({
            success: 0,
            message: 'Something went wrong!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.add = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  var whereClause = {
    id: userID,
    isDeleted: 'No'
  }
  try {
    user
      .findOne({
        attributes: [
          'id',
          'email',
          'fullName',
          'stripeID'
        ],
        where: whereClause
      })
      .then(async userData => {
        if (userData) {
          var userStripeID = userData.stripeID
          if (userStripeID == '') {
            stripe.customers.create(
              {
                description: userID + '_' + userData.fullName,
                email: userData.email
              },
              async function (err, customer) {
                if (err == null) {
                  userStripeID = customer.id
                  await user.update(
                    {
                      stripeID: userStripeID
                    },
                    {
                      where: {
                        id: userID
                      }
                    }
                  )
                  stripe.customers.createSource(
                    userStripeID,
                    {
                      source: req.body.token
                    },
                    async function (err, card) {
                      if (err == null) {
                        res.status(200).send({
                          success: 1,
                          data: card,
                          message: 'card saved successfully'
                        })
                      } else {
                        res.status(200).send({
                          success: 0,
                          message: err.message
                        })
                      }
                    }
                  )
                } else {
                  res.status(200).send({
                    success: 0,
                    message: err.message
                  })
                }
              }
            )
          } else {
            stripe.customers.createSource(
              userStripeID,
              {
                source: req.body.token
              },
              async function (err, card) {
                if (err == null) {
                  res.status(200).send({
                    success: 1,
                    data: card,
                    message: 'card saved successfully'
                  })
                } else {
                  res.status(200).send({
                    success: 0,
                    message: err.message
                  })
                }
              }
            )
          }
        } else {
          res.status(200).send({
            success: 0,
            message: "Something went wrong!"
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.delete = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  var whereClause = {
    id: userID,
    isDeleted: 'No'
  }
  try {
    user
      .findOne({
        attributes: [
          'id',
          'email',
          'fullName',
          'stripeID'
        ],
        where: whereClause
      })
      .then(async userData => {
        if (userData) {
          var userStripeID = userData.stripeID
          if (userStripeID != '') {
            stripe.customers.deleteSource(userStripeID, req.body.id, async function (
              err, card
            ) {
              if (err == null) {
                if (card.deleted) {
                  res.status(200).send({
                    success: 1,
                    message: 'Your card was deleted successfully'
                  })
                } else {
                  res.status(200).send({
                    success: 0,
                    message: 'Card not deleted. Please try again!'
                  })
                }
              } else {
                res.status(200).send({
                  success: 0,
                  message: 'Card not deleted. Please try again!'
                })
              }
            })
          } else {
            res.status(200).send({
              success: 0,
              message: "Your Stripe ID cannot be found. Please contact an administrator."
            })
          }
        } else {
          res.status(200).send({
            success: 0,
            message: "Something went wrong!"
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.bankToken = async (req, res, next) => {
  // const account = await stripe.accounts.update(
  //   'acct_1HNDrvHFNIW2IfkA',
  //   {
  //     tos_acceptance: {
  //       date: Math.floor(Date.now() / 1000),
  //       ip: req.connection.remoteAddress, // Assumes you're not using a proxy
  //     },
  //   }
  // );
  // const token = await stripe.refunds.create({
  //   charge: 'ch_1HNWkjJPU0uRL2zZkuHCctnd',
  //   amount: 100,
  //   reverse_transfer: true,
  //   refund_application_fee: true
  // });
  const token = await stripe.tokens.create({
    bank_account: {
      country: 'AU',
      currency: 'AUD',
      account_holder_name: 'Jenny Rosen',
      account_holder_type: 'individual',
      routing_number: '110000',
      account_number: '*********',
    },
  });
  res.status(200).send({
    success: 1,
    data: token,
    message: 'Bank token'
  })
}

exports.banklist = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  var whereClause = {
    id: barID,
    isDeleted: 'No'
  }
  try {
    bar
      .findOne({
        attributes: [
          'id',
          'email',
          'restaurantName',
          'address',
          'mobile',
          'stripeID'
        ],
        where: whereClause
      })
      .then(async barData => {
        if (barData) {
          var barStripeID = barData.stripeID
          if (barStripeID != '') {
            const stripeDetail = await stripe.accounts.retrieve(barStripeID)
            stripe.accounts.listExternalAccounts(barStripeID, { object: 'bank_account' }, async function (
              err,
              bankData
            ) {
              if (err == null) {
                res.status(200).send({
                  success: 1,
                  data: {bank_detail: bankData.data, stripe_account: stripeDetail},
                  message: 'Bank account list retrieved successfully.'
                })
              } else {
                res.status(200).send({
                  success: 0,
                  message: 'Something went wrong!'
                })
              }
            })
          } else {
            res.status(200).send({
              success: 0,
              message: 'Bank account not found!'
            })
          }
        } else {
          res.status(200).send({
            success: 0,
            message: 'Could not fetch venue details! Please try again.'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.sentOtp = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  bar
    .findOne({
      attributes: ['id', 'email'],
      where: {
        id: barID,
        isDeleted: 'No'
      }
    })
    .then(async barFetchResponse => {
      if (barFetchResponse) {
        try {
          var randtoken = require('rand-token').generator()
          var sixDigitCode = randtoken.generate(6, '**********')

          var emailContent = 'Your Verification code is: ' + sixDigitCode

          let mailOptions = {
            from: '"MyTab" <' + env.fromEmailAdmin + '>',
            to: barFetchResponse.email,
            subject: 'Verification Code',
            html: emailContent
          }

          const emailRes = await common.sendEmail(mailOptions)
            .then(response => {
              if (response) {
                bar
                  .update(
                    {
                      resetPasswordCode: sixDigitCode,
                      updatedAt: new Date()
                    },
                    {
                      returning: true,
                      where: {
                        id: barFetchResponse.id
                      }
                    }
                  )
                  .then(function () {
                    res.status(200).json({
                      success: 1,
                      message: 'Email sent successfully',
                      data: {
                        email: barFetchResponse.email
                      }
                    })
                  })
              }
            })
        } catch (e) {
          res.status(200).json({
            success: 0,
            message: e.message
          })
        }
      } else {
        res.status(200).send({
          success: 0,
          message: message.landing.emailNotRegistered,
          data: {}
        })
      }
    })
}

exports.bankadd = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  var whereClause = {
    id: barID,
    isDeleted: 'No'
  }
  try {
    bar
      .findOne({
        attributes: [
          'id',
          'email',
          'restaurantName',
          'address',
          'mobile',
          'stripeID',
          'resetPasswordCode'
        ],
        where: whereClause
      })
      .then(async barData => {
        if (barData) {
          if(req.body.otp === undefined || req.body.otp !== barData.resetPasswordCode){
            return res.status(200).send({
              success: 0,
              message: message.user.otpVerificationFailed,
              data: {}
            })
          }
          const barStripeID = barData.stripeID
          if (barStripeID === '' || barStripeID === null) {
            await stripe.accounts.create(
              {
                type: 'standard',
                country: 'AU',
                email: barData.email,
                // capabilities: {
                //   card_payments: {requested: true},
                //   transfers: {requested: true},
                // },
                settings: {payments: {statement_descriptor: 'MYTAB PTY LTD'}},
              }, async (err, bank) => {
                console.log("bank", err, bank);
                if (err == null) {
                  await stripe.accountLinks.create({
                    account: bank.id,
                    refresh_url: `${env.API_URL}/paymentMethod/bankadd`,
                    return_url: `${env.API_URL}/paymentMethod/complete_connect/${barID}`,
                    type: "account_onboarding",
                  }).then(async accountLink => {
                    await bar.update({stripeID: bank.id}, {where: {id: barData.id}})
                    // console.log(accountLink)
                    // let mailOptions = {
                    //   from: `MyTab <${env.fromEmailAdmin}>`,
                    //   to: barData.email,
                    //   subject: 'MyTab Onboarding',
                    //   html: `
                    //     <html lang="en">
                    //       <body>
                    //           <p>Please follow the link to complete account setup: ${accountLink.url}. make sure you fill in every details in the form</p>
                    //           <p>This link will be valid till <b>${new Date(accountLink.expires_at*1000)}<b></p>
                    //           <strong>Above Link is for one time use only so please complete the details at first go.</strong>
                    //       </body>
                    //     </html>`
                    // }

                    // await common.sendEmail(mailOptions)
                    //   .then(response => {
                    //   }).catch(err => {
                    //     console.log(err)
                    //     res.status(200).send({
                    //       success: 0,
                    //       message: 'Could not send email.'
                    //     })
                    //   })
                      res.status(200).send({
                        success: 1,
                        data: accountLink.url,
                        message: 'Please fill in the Stripe verification document to connect your banking details to your MyTab Venue account.'
                      })
                  }).catch(err => {
                      console.log(err)
                      res.status(200).send({
                        success: 0,
                        message: 'Something went wrong.'
                      })
                    }
                  );

                } else {
                  res.status(200).send({
                    success: 0,
                    message: "Something went wrong, while account on-boarding!",
                    payload: err
                  })
                }
              }
            )

          } else {
            let response_url = await stripe.account.createLoginLink(barStripeID)
              .catch(err => err);
            if (response_url && response_url.statusCode)
              return res.status(200).send({
                success: 0,
                data: response_url,
                message: response_url.message
              })

            // let mailOptions = {
            //   from: `MyTab <${env.fromEmailAdmin}>`,
            //   to: req.body.email,
            //   subject: 'Updating payment integration profile',
            //   html: `
            //       <html lang="en">
            //         <body>
            //             <p>Please follow the link to complete account setup: ${response_url.url}. make sure you fill in every details in the form</p>
            //              <p>This link will be valid till ${new Date(response_url.expires_at*1000)}</p>
            //             <strong>Above Link is for one time use only so please complete the details at first go.</strong>
            //         </body>
            //       </html>`
            // }

            // return await common.sendEmail(mailOptions)
            //   .then(response => {
            //     return res.status(200).send({
            //       success: 1,
            //       data: response_url.url,
            //       message: 'You will now receive a link via email to fill in the Stripe verification document to connect your banking details to your MyTab Venue account. Once this link is clicked, you must fill this form out instantly as you can not complete in two stages. Thank you. NOTE: If you can not find the email in your inbox, please check your junk folder.'
            //     })
            //   }).catch(err => {
            //       console.log(err)
            //       res.status(200).send({
            //         success: 0,
            //         message: 'Could not send email.'
            //       })
            //     }
            //   )

            return res.status(200).send({
              success: 1,
              data: response_url.url,
              message: 'Please fill in the Stripe verification document to connect your banking details to your MyTab Venue account.'
            })

            // if (req.body.token) {
            //   stripe.accounts.createExternalAccount(barStripeID, {external_account: req.body.token},
            //     (err, bankData) => {
            //       if (err == null) {
            //         res.status(200).send({
            //           success: 1,
            //           data: bankData,
            //           message: 'Bank account added successfully.'
            //         })
            //       } else {
            //         res.status(200).send({
            //           success: 0,
            //           message: "Something went wrong!",
            //           data: err
            //         })
            //       }
            //     }
            //   )
            // }
            // else {
            //   res.status(200).send({
            //     success: 0,
            //     message: "Bar details not found!"
            //   })
            // }
          }
        } else {
          res.status(200).send({
            success: 0,
            message: "Bar details not found!"
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.bankdelete = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  var whereClause = {
    id: barID,
    isDeleted: 'No'
  }
  try {
    bar
      .findOne({
        attributes: [
          'id',
          'email',
          'restaurantName',
          'address',
          'mobile',
          'stripeID'
        ],
        where: whereClause
      })
      .then(async barData => {
        if (barData) {
          var barStripeID = barData.stripeID
          if (barStripeID != '') {
            stripe.accounts.deleteExternalAccount(barStripeID, req.body.id, async function (
              err, bankData
            ) {
              if (err == null) {
                if (bankData.deleted) {
                  res.status(200).send({
                    success: 1,
                    message: 'Bank account deleted successfully.'
                  })
                } else {
                  res.status(200).send({
                    success: 0,
                    message: 'Bank account not deleted. Please try again!'
                  })
                }
              } else {
                res.status(200).send({
                  success: 0,
                  message: err.message
                })
              }
            })
          } else {
            res.status(200).send({
              success: 0,
              message: "Your Stripe ID cannot be found. Please contact an administrator."
            })
          }
        } else {
          res.status(200).send({
            success: 0,
            message: "Something went wrong!"
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.completeConnect = async (req, res, next) => {
  let html;
  const barId = req.params.id
  const barDetails = await bar.findOne({where: {id: barId}})
  if (barDetails.accountVerified === 'Approved') {
    html = `
                  <html lang="en">
                    <body>
                        <h2>Your bank account details are already added</h2>
                        <p>you can close this tab.</p>
                    </body>
                  </html>`
  } else {
    html = `
                  <html lang="en">
                    <body>
                        <h2>Bank details added successfully</h2>
                        <p>you can close this tab.</p>
                    </body>
                  </html>`
    await bar.update({accountVerified: 'Approved'}, {where: {id: barId}})
  }
  res.writeHeader(200, {"Content-Type": "text/html"});
  res.write(html);
  res.end()
}

exports.updateDefaultForCurrency = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID
    const bankID = req.body.bankID
    const whereClause = {
      id: barID,
      isDeleted: 'No'
    }
    bar
      .findOne({
        attributes: [
          'id',
          'email',
          'restaurantName',
          'address',
          'mobile',
          'stripeID'
        ],
        where: whereClause
      })
      .then(async barData => {
        if (barData !== '') {
          const updatedCardDetails = await stripe.accounts.updateExternalAccount(barData.stripeID, bankID, true);
          res.status(200).send({
            success: 1,
            data: updatedCardDetails,
            message: 'Card details'
          })
        } else {
          res.status(200).send({
            success: 0,
            message: 'Cannot find bank account for this venue'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

// API to delete Stripe Account from Stripe and not used in integration with the app. Only used by backend dev when client require to delete any accoun directly from stripe.....
exports.deleteStripeAccount = async (req, res) => {
  
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  var whereClause = {
    id: barID,
    isDeleted: 'No'
  }
  try {
    bar
      .findOne({
        attributes: [
          'id',
          'email',
          'restaurantName',
          'address',
          'mobile',
          'stripeID'
        ],
        where: whereClause
      })
      .then(async barData => {
        if (barData) {
          var barStripeID = barData.stripeID
          if (barStripeID != '') {
            const stripe = require('stripe')(env.stripe_secret_key);

            const deleted = await stripe.accounts.del(
              barStripeID
            );
            // if (err == null) {
              if (deleted != '') {
                res.status(200).send({
                  success: 1,
                  message: 'Stripe account deleted successfully.'
                })
              } else {
                res.status(200).send({
                  success: 0,
                  message: 'Stripe account not deleted. Please try again!'
                })
              }
            // } else {
            //   res.status(200).send({
            //     success: 0,
            //     message: err.message
            //   })
            // }
          } else {
            res.status(200).send({
              success: 0,
              message: "Your Stripe ID cannot be found. Please contact an administrator."
            })
          }
        } else {
          res.status(200).send({
            success: 0,
            message: "Something went wrong!"
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}