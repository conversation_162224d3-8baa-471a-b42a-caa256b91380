var Sequelize = require('sequelize')

var env = require('../config/environment')
var bar = require('./bar')

var docketSubscription = sequelize.define(
  'docketSubscription',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    // paymentStatus: Sequelize.ENUM('Done', 'Failed'),
    // status: Sequelize.ENUM('Active', 'Inactive'),
    subscriptionTime: Sequelize.TIME,
    createdAt: Sequelize.DATE,
    // updatedAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

docketSubscription.belongsTo(bar, { foreignKey: "barID"})

module.exports = docketSubscription