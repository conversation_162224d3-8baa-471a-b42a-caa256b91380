var Sequelize = require('sequelize')
const Op = Sequelize.Op
const moment = require('moment')
const sequelize = require('sequelize')

const order = require('../../models/orders')
const orderItems = require('../../models/orderItems')
const orderItemExtras = require('../../models/orderItemExtras')
const orderItemVariants = require('../../models/orderItemVariants')
const coupons = require('../../models/coupons')
const orderProductVariantTypes = require('../../models/orderProductVariantTypes')
const orderProductVariantSubTypes = require('../../models/orderProductVariantSubTypes')
const productVariantTypes = require('../../models/productVariantTypes')
const productVariantSubTypes = require('../../models/productVariantSubTypes')
const user = require('../../models/user')
const bar = require('../../models/bar')
const product = require('../../models/product')
const productVariants = require('../../models/productVariants')
const productExtras = require('../../models/productExtras')
const pickupLocation = require('../../models/pickupLocation')
const subCategory = require('../../models/subCategory')
const orderTax = require('../../models/orderTax')
const orderRefundTax = require('../../models/orderRefundTax')
const orderTableNumber = require('../../models/orderTableNumber')
const barAccessToken = require('../../models/barAccessToken')
const barOpeningHoursUTC = require('../../models/barOpeningHoursUTC')
const commonFunction = require('../../common/commonFunction')
const orderItemWaitTimeNotifications = require('../../models/orderItemWaitTimeNotifications')
const transLogsModel = require('../../models/transactionLogs')
const transErrorLogsModel = require('../../models/transactionErrorLogs')
const cartItems = require('../../models/cartItems')
const userConsentVenue = require('../../models/userConsentVenue')
const barNotification = require('../../models/barNotification')
const docketNotification = require('../../models/docketNotification')
var env = require('../../config/environment')
var jwt = require('jsonwebtoken')
const categoryModel = require('../../models/category')
const segmentProductTags = require('../../models/segmentProductTags')
const userBarTags = require('../../models/userBarTags')
var stripe = require('stripe')(env.stripe_secret_key)
const taxModel = require('../../models/tax')
const orderDiscount = require('../../models/orderDiscount');
const appliedDiscounts = require('../../models/appliedDiscounts')
const pickupCodeModel = require('../../models/pickupCode')
const adsModel = require('../../models/ads');
const adsSegmentModel = require('../../models/ads_segment');
const segmentUserVenueModel = require('../../models/segmentUserVenue');
const segmentModel = require('../../models/segment');
// const advertiserUserModel = require('../../models/advertiser_user');

async function createorderrandomnumber(barData) {
  var min = 1000000
  var max = 9999999
  var randomNum = Math.floor(Math.random() * (max - min + 1)) + min

  if (barData && barData.restaurantName) {
    var orderNo = barData.restaurantName.slice(0, 3).toUpperCase() + randomNum
  } else {
    var orderNo = randomNum
  }

  const orderData = await order.findAll({
    attributes: ['orderNo'],
    where: {
      orderNo: orderNo
    }
  })

  if (orderData.length > 0) {
    return createorderrandomnumber(barData)
  } else {
    return orderNo
  }
}

async function createPickupCode(barData) {
  const pickupCodeData = await pickupCodeModel.findOne({
    where: {
      pickupcode: { [Op.notIn]: Sequelize.literal(`(select pickupCode from orders where barID = ${barData.id} and orderServiceType = 'PICKUP' and orderDate >= DATE_ADD(CURDATE(), INTERVAL -15 DAY))`) }
    },
    order: Sequelize.literal('rand()'),
  })

  return pickupCodeData ? pickupCodeData.pickupcode.toUpperCase() : 'ROSE'
}

function groupBy(xs, key) {
  return xs.reduce((rv, x) => ((rv[x[key]] = rv[x[key]] || []).push(x), rv), {});
}

function groupByOrderItems(orderItems, orderServiceType = 'PICKUP', refundStatus = 'no') {
  const order_items = JSON.parse(JSON.stringify(orderItems));
  const newValueItemsArr = [];

  if (orderServiceType === 'PICKUP') {
    Object.entries(groupBy(order_items, 'pickupLocation')).forEach(([pickupLocation, items]) => {
      Object.entries(groupBy(items, 'waitTime')).forEach(([_, waitTimeItems]) => {
        newValueItemsArr.push({
          pickup_location: pickupLocation,
          wait_time: waitTimeItems[0]?.expectedTime,
          orderStatus: waitTimeItems[0]?.orderStatus,
          refundStatus,
          items: waitTimeItems
        });
      });
    });
  } else {
    Object.entries(groupBy(order_items, 'waitTime')).forEach(([_, waitTimeItems]) => {
      newValueItemsArr.push({
        wait_time: waitTimeItems[0]?.expectedTime,
        orderStatus: waitTimeItems[0]?.orderStatus,
        refundStatus,
        items: waitTimeItems
      });
    });
  }

  return newValueItemsArr.sort((a, b) => new Date(a.wait_time) - new Date(b.wait_time));
}


exports.barOrderList = async (req, res) => {
  try {
    const barID = res.locals.barID;

    const currentDateTimeUTC = req.body.currentDateTimeUTC ? req.body.currentDateTimeUTC : moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const currentDay = moment(currentDateTimeUTC).isoWeekday() - 1;
    const currentTime = moment(currentDateTimeUTC).format('HH:mm:ss');

    const barAndAccess = await bar.findOne({
      attributes: [
        'id',
        'restaurantName',
        'managerName',
        'email',
        'countryCode',
        'mobile',
        'address',
        'latitude',
        'longitude',
        'serviceType',
        'avatar',
        'isVenueServeAlcohol',
        'liquorLicenseNumber',
        'posStatus',
        'docketStatus',
        'pauseOrderStartTime',
        'pauseOrderLimit',
        [
          sequelize.literal(`
              CASE 
                WHEN EXISTS (
                  SELECT 1
                  FROM bar_opening_hours_utc AS OP
                  WHERE OP.barID = bar.id
                    AND OP.weekDay = ${currentDay}
                    AND OP.isClosed = 0
                    AND '${currentTime}' BETWEEN OP.openingHours AND OP.closingHours
                )
                THEN 1
                ELSE 0
              END
            `),
          'operatingFlag'
        ],
      ],
      where: { id: barID },
      include: [
        {
          model: barAccessToken,
          required: true,
          where: {
            barID: barID,
            accessToken: req.headers.accesstoken,
          },
          attributes: ["subCategoryIDs"],
        },
        {
          model: barOpeningHoursUTC,
          attributes: [
            'id',
            'weekDay',
            'barOpeningHoursID',
            'openingHours',
            'closingHours',
            'isClosed'
          ],
          required: false,
          where: {
            isClosed: 0,
            weekDay: currentDay,
            openingHours: { [Op.lte]: currentTime },
            closingHours: { [Op.gte]: currentTime }
          }
        }
      ],
    });

    const getUserCategory = barAndAccess?.bar_accesstokens && barAndAccess?.bar_accesstokens.length
      ? barAndAccess.bar_accesstokens[0]?.subCategoryIDs
        ? barAndAccess.bar_accesstokens[0]?.subCategoryIDs.split(",")
        : []
      : [];

    const whereClauseProduct = [];
    const dashboardOrder = {}
    let pickupOrder = []
    let newOrder = []

    if (getUserCategory.length > 0) {
      whereClauseProduct.push({
        subCategoryID: getUserCategory
      });
    }

    const orderAttributes = [
      'id',
      'orderNo',
      'subTotal',
      'transactionFee',
      'refundTransactionFee',
      'tax',
      'total',
      'orderDate',
      'orderStatus',
      'refundStatus',
      'promocode_id',
      'promocode_amount',
      'promocode_discount',
      "totalDiscountedAmount",
      'userID',
      'barID',
      'posOrderStatus',
      'orderServiceType',
      'createdAt',
      'docketPrintingStatus',
      'pickupCode',
    ]
    const itemAttributes = [
      'id',
      'orderID',
      'productID',
      'price',
      'quantity',
      'specialRequest',
      'isCanceled',
      'refundAmount',
      'refundedQuantity',
      "newRefundAmount",
      'waitTime',
      'orderStatus',
      'PreparingStartTime',
      'ReadyTime',
      'PickedupTime',
      "discountedAmount",
      [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
      [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
      [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
    ]
    const productAttributes = [
      'id',
      'name',
      'categoryID',
      'description',
      'avatar',
      'subCategoryID'
    ]
    newOrder = await order
      .findAll({
        where: [{
          isDeleted: 'No',
          paymentStatus: 'received',
          barID: barID,
          isCanceled: 'No',
          [Op.or]: [
            {
              orderStatus: 'New'
            },
            {
              orderStatus: 'Preparing'
            }
          ],
          [Op.not]: [
            {
              orderStatus: 'Intoxicated'
            },
          ]
        }],
        attributes: orderAttributes,
        include: [
          {
            required: true,
            attributes: itemAttributes,
            where: [
              {
                [Op.or]: [
                  {
                    orderStatus: 'New'
                  },
                  {
                    orderStatus: 'Preparing'
                  }
                ],
                isCanceled: 'No'
              },
            ],
            model: orderItems,
            include: [
              {
                where: [...whereClauseProduct],
                attributes: productAttributes,
                model: product,
                include: [
                  {
                    attributes: [
                      'id',
                      'description',
                      'address'
                    ],
                    model: pickupLocation,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productExtrasID',
                  'price'
                ],
                model: orderItemExtras,
                include: [
                  {
                    attributes: [
                      'id',
                      'extraItem'
                    ],
                    model: productExtras,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productVariantsID',
                  'price'
                ],
                model: orderItemVariants,
                include: [
                  {
                    attributes: [
                      'id',
                      'variantType'
                    ],
                    model: productVariants,
                  }
                ]
              },
              {
                attributes: [
                  ['id', "orderProductVariantTypeID"],
                  'orderItemID',
                ],
                where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                model: orderProductVariantTypes,
                required: false,
                include: [
                  {
                    attributes: [
                      ['id', "productVariantTypeID"],
                      'label',
                    ],
                    model: productVariantTypes,
                    required: true,
                    include: [
                      {
                        attributes: [
                          ['id', "orderProductVariantSubTypeID"],
                          'orderItemID',
                        ],
                        where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                        model: orderProductVariantSubTypes,
                        include: [
                          {
                            attributes: [
                              ['id', "productVariantSubTypeID"],
                              ['variantType', "extraItem"],
                              'price',
                            ],
                            model: productVariantSubTypes,
                          }
                        ]
                      }
                    ]
                  }
                ],
              }
            ]
          },
          {
            required: true,
            model: user,
            attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
          },
          {
            model: coupons,
            attributes: ['id', 'code', 'name', 'description']
          },
          {
            model: orderTableNumber,
            attributes: ['tableCode']
          },
          {
            attributes: [
              'id', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ],
        order: [['createdAt', 'ASC']],
        distinct: true,
        duplicating: false,
      });

    pickupOrder = await order
      .findAll({
        where: [{
          orderStatus: {
            [Op.notIn]: ['Intoxicated', 'Pickedup']
          },
          isDeleted: 'No',
          barID: barID,
          isCanceled: 'No',
          paymentStatus: 'received',
        }],
        attributes: [...orderAttributes],
        include: [
          {
            required: true,
            attributes: itemAttributes,
            model: orderItems,
            where: {
              orderStatus: 'Pickup',
              isCanceled: 'No'
            },
            include: [
              {
                where: [...whereClauseProduct],
                attributes: productAttributes,
                model: product,
                include: [
                  {
                    attributes: [
                      'id',
                      'description',
                      'address'
                    ],
                    model: pickupLocation,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productExtrasID',
                  'price'
                ],
                model: orderItemExtras,
                include: [
                  {
                    attributes: [
                      'id',
                      'extraItem'
                    ],
                    model: productExtras,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productVariantsID',
                  'price'
                ],
                model: orderItemVariants,
                include: [
                  {
                    attributes: [
                      'id',
                      'variantType'
                    ],
                    model: productVariants,
                  }
                ]
              },
              {
                attributes: [
                  ['id', "orderProductVariantTypeID"],
                  'orderItemID',
                ],
                where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                model: orderProductVariantTypes,
                required: false,
                include: [
                  {
                    attributes: [
                      ['id', "productVariantTypeID"],
                      'label',
                    ],
                    model: productVariantTypes,
                    required: true,
                    include: [
                      {
                        attributes: [
                          ['id', "orderProductVariantSubTypeID"],
                          'orderItemID',
                        ],
                        where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                        model: orderProductVariantSubTypes,
                        include: [
                          {
                            attributes: [
                              ['id', "productVariantSubTypeID"],
                              ['variantType', "extraItem"],
                              'price',
                            ],
                            model: productVariantSubTypes,
                          }
                        ]
                      }
                    ]
                  }
                ],
              }
            ]
          },
          {
            required: true,
            model: user,
            attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
          },
          {
            model: coupons,
            attributes: ['id', 'code', 'name', 'description']
          },
          {
            model: orderDiscount,
            attributes: [
              'id',
              'orderID',
              'discountID',
              'discountCode',
              'discountType',
              'type',
              'discountValue',
              'discountAmount',
              'createdAt',
            ]
          },
          {
            model: orderTableNumber,
            attributes: ['tableCode']
          },
          {
            attributes: [
              'id', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ],
        order: [['updatedAt', 'ASC']],
        distinct: true,
        duplicating: false
      });

    const today = moment(new Date()).format('YYYY-MM-DD')
    var todayEarningWhereClause = {
      isDeleted: 'No',
      barID: barID,
      paymentStatus: 'received',
    }
    let todayEarning = await order.findAll({
      attributes: [
        [
          Sequelize.fn('date_format', Sequelize.col('orderDate'), '%Y-%m-%d'),
          'oDate'
        ],
        [sequelize.literal("round(sum(total - (total * 2.9/ 100 + 0.31) - (total * 0.25 / 100 + 0.25) - transactionFee),2)"), 'amount'],
      ],
      group: ['oDate'],
      where: todayEarningWhereClause,
      having: {
        oDate: {
          $eq: sequelize.literal("'" + today + "'")
        }
      }
    })

    if (todayEarning[0]) {
      todayEarning = todayEarning[0].toJSON();
    }

    if (barAndAccess.dataValues.posStatus && barAndAccess.dataValues.posStatus === '1') {
      var whereClauseSubCategory = {
        categoryID: '-1',
        isDeleted: 'No'
      }
    } else {
      var whereClauseSubCategory = {
        [Op.not]: [{ categoryID: '-1' }],
        isDeleted: 'No'
      }
    }
    const subCategoryList = await subCategory
      .findAll({
        attributes: [
          [
            sequelize.literal(`
              (
                SELECT COALESCE(
                  (SELECT IF(
                    BSH.isClosed = 0 
                    AND CAST('${currentTime}' AS TIME) BETWEEN CAST(BSH.openingHours AS TIME) 
                    AND CAST(BSH.closingHours AS TIME), 1, 0
                  ) 
                  FROM bar_sub_category_opening_hours_utc AS BSH 
                  WHERE sub_category.id = BSH.subCategoryID 
                  AND BSH.barID = ${barID} 
                  AND BSH.weekDay = ${currentDay} 
                  AND CAST('${currentTime}' AS TIME) BETWEEN CAST(BSH.openingHours AS TIME) AND CAST(BSH.closingHours AS TIME)
                  ORDER BY BSH.id ASC 
                  ), 0
                )
              )
            `),
            'operatingFlag',
          ],
        ],
        where: whereClauseSubCategory
      });
    let barCategoryIsOpen = 0;
    subCategoryList.map(cat1 => {
      if (cat1.dataValues.operatingFlag == 1) {
        barCategoryIsOpen = 1;
      }
    })
    let barIsOpen = barAndAccess.dataValues.operatingFlag;
    let pauseStartTime = null;
    let pauseEndTime = null;
    let pauseOrderStatus = 0;
    if (barAndAccess.dataValues.pauseOrderStartTime) {
      let endTime = moment.utc(barAndAccess.dataValues.pauseOrderStartTime).add(barAndAccess.dataValues.pauseOrderLimit, 'minutes');
      if (endTime.isAfter(moment.utc())) {
        pauseStartTime = barAndAccess.dataValues.pauseOrderStartTime;
        pauseEndTime = endTime;
        pauseOrderStatus = 1;
      }
    }
    let newOrdersObj = newOrder.map((order) => {
      let newOrderItems = groupByOrderItems(order.order_items, order.orderServiceType, order.refundStatus);
      order.dataValues['order_items_group'] = newOrderItems;
      return order
    })

    dashboardOrder.newOrder = newOrdersObj

    let pickupOrderObj = pickupOrder.map((order) => {
      let newOrderItems = groupByOrderItems(order.order_items, order.orderServiceType, order.refundStatus);
      order.dataValues['order_items_group'] = newOrderItems;
      return order
    })

    dashboardOrder.pickupOrder = pickupOrderObj

    dashboardOrder.todayEarnings = (todayEarning && todayEarning.amount) ? todayEarning.amount : 0
    dashboardOrder.barDetails = barAndAccess
    dashboardOrder.barDetails.dataValues.barIsOpen = barIsOpen;
    dashboardOrder.barDetails.dataValues.pauseStartTime = pauseStartTime;
    dashboardOrder.barDetails.dataValues.pauseEndTime = pauseEndTime;
    dashboardOrder.barDetails.dataValues.pauseOrderStatus = pauseOrderStatus;
    dashboardOrder.barDetails.dataValues.barCategoryIsOpen = barCategoryIsOpen;

    res.status(200).send({
      success: 1,
      message: 'ORDER LIST RETRIEVED SUCCESSFULLY!',
      data: dashboardOrder
    })
  } catch (error) {
    console.log(error)
    res.status(500).send({
      success: 0,
      message: error
    })
  }
}

exports.barOrderListCount = async (req, res) => {
  try {
    const barID = res.locals.barID;

    const currentDateTimeUTC = req.body.currentDateTimeUTC ? req.body.currentDateTimeUTC : moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const currentDay = moment(currentDateTimeUTC).isoWeekday() - 1;
    const currentTime = moment(currentDateTimeUTC).format('HH:mm:ss');

    const barAndAccess = await bar.findOne({
      attributes: [
        'id',
        'restaurantName',
        'managerName',
        'email',
        'countryCode',
        'mobile',
        'address',
        'latitude',
        'longitude',
        'serviceType',
        'avatar',
        'isVenueServeAlcohol',
        'liquorLicenseNumber',
        'posStatus',
        'docketStatus',
        'pauseOrderStartTime',
        'pauseOrderLimit',
        [
          sequelize.literal(`
            CASE 
              WHEN EXISTS (
                SELECT 1
                FROM bar_opening_hours_utc AS OP
                WHERE OP.barID = bar.id
                  AND OP.weekDay = ${currentDay}
                  AND OP.isClosed = 0
                  AND '${currentTime}' BETWEEN OP.openingHours AND OP.closingHours
              )
              THEN 1
              ELSE 0
            END
          `),
          'operatingFlag'
        ],
      ],
      where: { id: barID },
      include: [
        {
          model: barAccessToken,
          required: true,
          where: {
            barID: barID,
            accessToken: req.headers.accesstoken,
          },
          attributes: ["subCategoryIDs"],
        },
        {
          model: barOpeningHoursUTC,
          attributes: [
            'id',
            'weekDay',
            'barOpeningHoursID',
            'openingHours',
            'closingHours',
            'isClosed'
          ],
          required: false,
          where: {
            isClosed: 0,
            weekDay: currentDay,
            openingHours: { [Op.lte]: currentTime },
            closingHours: { [Op.gte]: currentTime }
          }
        }
      ],
    });

    const getUserCategory = barAndAccess?.bar_accesstokens && barAndAccess?.bar_accesstokens.length
      ? barAndAccess.bar_accesstokens[0]?.subCategoryIDs
        ? barAndAccess.bar_accesstokens[0]?.subCategoryIDs.split(",")
        : []
      : [];

    const whereClauseProduct = [];
    let pickupOrder = []
    let newOrder = []

    if (getUserCategory.length > 0) {
      whereClauseProduct.push({
        subCategoryID: getUserCategory
      });
    }

    const orderAttributes = [
      'id',
      'orderNo',
      'subTotal',
      'transactionFee',
      'tax',
      'total',
      'orderDate',
      'orderStatus',
      'refundStatus',
      'promocode_id',
      'promocode_amount',
      'promocode_discount',
      "totalDiscountedAmount",
      'userID',
      'barID',
      'posOrderStatus',
      'orderServiceType',
      'createdAt',
      'docketPrintingStatus',
      'pickupCode',
    ]
    newOrder = await order
      .findAll({
        where: [{
          isDeleted: 'No',
          paymentStatus: 'received',
          barID: barID,
          isCanceled: 'No',
          [Op.or]: [
            {
              orderStatus: 'New'
            },
            {
              orderStatus: 'Preparing'
            }
          ],
          [Op.not]: [
            {
              orderStatus: 'Intoxicated'
            },
          ]
        }],
        attributes: orderAttributes,
        include: [
          {
            required: true,
            // attributes: itemAttributes,
            where: [
              {
                [Op.or]: [
                  {
                    orderStatus: 'New'
                  },
                  {
                    orderStatus: 'Preparing'
                  }
                ],
                isCanceled: 'No'
              },
            ],
            model: orderItems,
            include: [
              {
                where: [...whereClauseProduct],
                // attributes: productAttributes,
                model: product,
              }
            ]
          },
          {
            required: true,
            model: user,
            attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
          }
        ],
        order: [['createdAt', 'ASC']],
        distinct: true,
        duplicating: false,
      });

    pickupOrder = await order
      .findAll({
        where: [{
          orderStatus: {
            [Op.notIn]: ['Intoxicated', 'Pickedup']
          },
          isDeleted: 'No',
          barID: barID,
          isCanceled: 'No',
          paymentStatus: 'received',
        }],
        // having: havingClauseOrder,
        attributes: [...orderAttributes],
        include: [
          {
            required: true,
            // attributes: itemAttributes,
            model: orderItems,
            where: {
              orderStatus: 'Pickup',
              isCanceled: 'No'
            },
            include: [
              {
                where: [...whereClauseProduct],
                // attributes: productAttributes,
                model: product
              }
            ]
          },
          {
            required: true,
            model: user,
            attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
          },
          {
            model: orderDiscount,
            attributes: [
              'id',
              'orderID',
              'discountID',
              'discountCode',
              'discountType',
              'type',
              'discountValue',
              'discountAmount',
              'createdAt',
            ]
          },
        ],
        order: [['updatedAt', 'ASC']],
        distinct: true,
        duplicating: false
      });

    if (barAndAccess.dataValues.posStatus && barAndAccess.dataValues.posStatus === '1') {
      var whereClause = {
        categoryID: '-1',
        isDeleted: 'No'
      }
    } else {
      var whereClause = {
        [Op.not]: [{ categoryID: '-1' }],
        isDeleted: 'No'
      }
    }
    const subCategoryList = await subCategory.findAll({
      attributes: [
        [
          sequelize.literal(`
            (
              SELECT COALESCE(
                (SELECT IF(
                  BSH.isClosed = 0 
                  AND CAST('${currentTime}' AS TIME) BETWEEN CAST(BSH.openingHours AS TIME) 
                  AND CAST(BSH.closingHours AS TIME), 1, 0
                ) 
                FROM bar_sub_category_opening_hours_utc AS BSH 
                WHERE sub_category.id = BSH.subCategoryID 
                AND BSH.barID = ${barID} 
                AND BSH.weekDay = ${currentDay} 
                AND CAST('${currentTime}' AS TIME) BETWEEN CAST(BSH.openingHours AS TIME) AND CAST(BSH.closingHours AS TIME)
                ORDER BY BSH.id ASC 
                ), 0
              )
            )
          `),
          'operatingFlag',
        ],
      ],
      where: whereClause
    });

    let barCategoryIsOpen = 0;

    subCategoryList.map(cat1 => {
      if (cat1.dataValues.operatingFlag == 1) {
        barCategoryIsOpen = 1;
      }
    })
    let barIsOpen = barAndAccess.dataValues.operatingFlag;
    let pauseStartTime = null;
    let pauseEndTime = null;
    let pauseOrderStatus = 0;
    if (barAndAccess.dataValues.pauseOrderStartTime) {
      let endTime = moment.utc(barAndAccess.dataValues.pauseOrderStartTime).add(barAndAccess.dataValues.pauseOrderLimit, 'minutes');
      if (endTime.isAfter(moment.utc())) {
        pauseStartTime = barAndAccess.dataValues.pauseOrderStartTime;
        pauseEndTime = endTime;
        pauseOrderStatus = 1;
      }
    }
    res.status(200).send({
      success: 1,
      message: 'ORDER LIST COUNT RETRIEVED SUCCESSFULLY!',
      data: { 'newOrderCount': newOrder.length, 'newOrder': newOrder, 'pickupOrderCount': pickupOrder.length, 'pickupOrder': pickupOrder, 'readPopup': barAndAccess.readPopup, 'docketStatus': barAndAccess.docketStatus, 'pauseOrderStatus': pauseOrderStatus, 'pauseStartTime': pauseStartTime, 'pauseEndTime': pauseEndTime, 'barIsOpen': barIsOpen, 'barCategoryIsOpen': barCategoryIsOpen }
    })
  } catch (error) {
    console.log(error)
    res.status(500).send({
      success: 0,
      message: error
    })
  }
}

exports.create = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {

    if (req.body.transactionFee == 0) {
      return res.status(200).json({
        success: 2,
        message: "We do apologise, to complete your order you will need to update your MyTab app to the newest version. Thank You!",
      })
    }

    const intoxicatedOrderData = await order.findOne({
      attributes: ['id', 'intoxicatedDate'],
      where: {
        userID: userID,
        barID: req.body.barID,
        orderStatus: 'Intoxicated'
      },
      order: [['id', 'DESC']],
    })

    if (intoxicatedOrderData) {
      let intoxicatedDate = moment(new Date(intoxicatedOrderData.intoxicatedDate)).add(1, 'days').format('YYYY-MM-DD 04:00:00')
      let today = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      if (today < intoxicatedDate) {
        res.status(200).json({
          success: 0,
          message: "Your safety is our priority, this venue has listed you as too intoxicated and is refusing you service for remainder of tonight.",
        })
        return false;
      }
    }

    if (req.body.isConfirm && req.body.isConfirm == 'false') {
      const prevOrderData = await order
        .findOne({
          where: {
            isDeleted: 'No',
            isCanceled: 'No',
            paymentStatus: 'received',
            userID: userID,
            barID: {
              $ne: req.body.barID
            },
            [Op.or]: [
              {
                orderStatus: 'New'
              },
              {
                orderStatus: 'Preparing'
              },
              {
                orderStatus: 'Pickup'
              }
            ]
          },
          attributes: [
            'id',
            'orderNo',
            // [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
            'subTotal',
            'transactionFee',
            'tax',
            'total',
            'userID',
            'barID',
          ],
          // include: [
          //   {
          //     required: true,
          //     model: bar,
          //     attributes: ['id', 'restaurantName']
          //   }
          // ],
          order: [['createdAt', 'DESC'], ['orderStatus', 'DESC']],
          distinct: true,
          duplicating: false
        })
      if (prevOrderData) {
        res.status(200).json({
          success: 2,
          message: "Your orders preparing at another venue, Please confirm you want to continue this order.",
        })
        return false;
      }
    }

    const barData = await bar.findOne({
      attributes: [
        'id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile', 'stripeID', 'venueId', 'posStatus', 'attachedPosConfig', 'docketCommission', 'docketStatus', 'posFee', 'serviceType', 'waitTimeServiceType', 'pauseOrderStartTime', 'pauseOrderLimit'
      ],
      where: {
        id: req.body.barID
      },
      // include: [POSconfig]
    })

    if (barData?.dataValues?.pauseOrderStartTime) {
      let endTime = moment.utc(barData.dataValues.pauseOrderStartTime).add(barData.dataValues.pauseOrderLimit, 'minutes');
      if (endTime.isAfter(moment.utc())) {
        return res.status(200).send({
          success: -6,
          message: 'Due to high order volume, new orders are temporarily paused'
        })
      }
    }

    // const isPOSConfigured = barData.attachedPosConfig && barData.attachedPosConfig !== ''
    const userData = await user.findOne({
      attributes: ['id', 'fullName', 'email', 'countryCode', 'mobile', 'stripeID'],
      where: {
        id: userID
      }
    })

    var categoryData = await categoryModel.findOne({
      where: { name: 'kitchen', isDeleted: 'No' },
      attributes: ['id'],
      raw: true
    });
    if (!categoryData) {
      return res.status(200).json({ success: 0, message: 'Category not found.', data: {} });
    }
    if (req.body.paymentType && req.body.paymentType == 1) {
      if (req.body.cardid && req.body.cardid != '') {
        const card = await stripe.customers.retrieveSource(
          userData.stripeID,
          req.body.cardid
        ).catch(err => err);
        if (card.statusCode === 404) {
          return res.status(200).json({
            success: 0,
            message: 'Provided cards detail are not linked with this user.',
            data: {}
          });
        }
      } else {
        await res.status(200).send({
          success: 0,
          message: 'Payment failed! Please make sure you have added a payment account'
        })
      }
    }

    let pickupCode
    let serviceType
    let tableFlag = false;
    let productIDs = []
    const randomNum = await createorderrandomnumber(barData)
    if (req.body.order_service_type && req.body.order_service_type.toLowerCase() === 'table') {
      if (!req.body.table_code || req.body.table_code && req.body.table_code === '')
        return res.status(200).json({
          success: 0,
          message: 'Table number is required on table service venues!',
          data: {}
        });
      else {
        tableFlag = true
        serviceType = 'TABLE'
      }
    } else {
      pickupCode = await createPickupCode(barData)
      serviceType = 'PICKUP'
    }

    const requestOrderItems = JSON.parse(req.body.orderItems);

    for (const requestOrderItem of requestOrderItems) {
      productIDs.push(requestOrderItem?.productID)
      let productDetail = await commonFunction.getProductDetail(requestOrderItem['productID']);
      requestOrderItem.productDetail = productDetail
      if (productDetail.isStockLimit == 'Yes') {
        if (productDetail.stock == 0 || productDetail.stock < requestOrderItem['quantity']) {
          return res.status(200).json({
            success: 0,
            message: 'Your cart has exceeded the available stock availability for one or more menu item(s)',
            data: {}
          });
        }
      }

    }

    let newConvertedDateTime = new Date();
    newConvertedDateTime.setHours(newConvertedDateTime.getHours() + 8);

    const appliedDiscountsList = await appliedDiscounts.findAll({
      where: { userID, barID: req.body.barID },
    });
    let totalDiscount = appliedDiscountsList.reduce(
      (sum, disc) => sum + Number(disc.discountAmount),
      0
    );
    totalDiscount = parseFloat(totalDiscount.toFixed(2));


    order
      .create({
        orderNo: randomNum,
        subTotal: req.body.subTotal,
        cardType: req.body.cardType,
        cardNumber: req.body.cardNumber,
        fundingType: req.body.fundingType,
        transactionFee: req.body.transactionFee,
        totalDiscountedAmount: totalDiscount,
        pickupCode: pickupCode,
        // tableCode: req.body.table_code ? req.body.table_code.toUpperCase() : '',
        orderServiceType: serviceType,
        total: req.body.total,
        orderDate: new Date(),
        convertedOrderDate: newConvertedDateTime,
        convertedOrderDateTime: newConvertedDateTime,
        userID: userID,
        barID: req.body.barID,
        createdAt: new Date(),
        PreparingStartTime: new Date(),
      })
      .then(async orderResponse => {
        let orderID = orderResponse.id
        let taxIDs = JSON.parse(req.body.tax);
        let totalTaxAmount = 0;
        if (taxIDs && taxIDs.length) {
          const taxData = await taxModel.findAll({
            attributes: ['id', 'name', 'percentage', 'status'],
            where: { id: { in: taxIDs } }
          })

          let orderTaxData = [];
          if (taxData.length) {
            taxData.forEach(tax => {
              orderTaxData.push({
                barID: req.body.barID,
                orderID: orderResponse.id,
                name: tax.dataValues.name,
                percentage: tax.dataValues.percentage,
                taxID: tax.dataValues.id,
                amount: (req.body.subTotal * tax.dataValues.percentage / 100).toFixed(2),
              });
              totalTaxAmount = totalTaxAmount + Number((req.body.subTotal * tax.dataValues.percentage / 100).toFixed(2))
            });
            if (orderTaxData.length) {
              orderTax.bulkCreate(orderTaxData, { returning: true });
            }
          }
        }
        // Record each applied discount into order_discount.
        for (let disc of appliedDiscountsList) {
          await orderDiscount.create({
            orderID: orderID,
            discountID: disc.discountID,
            discountCode: disc.discountCode,
            discountType: disc.discountType,
            type: disc.type,
            userID: userID,
            discountValue: disc.discountValue,
            discountAmount: disc.discountAmount,
            createdAt: new Date()
          });
        }
        // Clear applied discounts from the cart.
        await appliedDiscounts.destroy({
          where: { userID, barID: req.body.barID },
        });

        if (productIDs?.length) {
          const uniqueTagIds = await segmentProductTags.findAll({
            where: {
              productID: {
                [Op.in]: productIDs
              }
            },
            attributes: ['tagID'],
            group: ['tagID']
          });

          const tagIds = uniqueTagIds.map(tag => tag.tagID);
          const userBarTagsData = await userBarTags.findAll({
            where: {
              tagID: {
                [Op.in]: tagIds
              },
              barID: req.body.barID,
              userID,
            },
            attributes: ['tagID'],
          });
          const operations = [];
          const existingIds = userBarTagsData.map(elem => elem.tagID);
          const nonExistingIds = uniqueTagIds.filter(elem => !existingIds.includes(elem.tagID));
          const createData = nonExistingIds.map((elem) => ({ userID, orderID, tagID: elem.tagID, barID: req.body.barID }))

          if (existingIds.length) {
            const updateOperation = userBarTags.update(
              {
                orderID,
              },
              {
                where: {
                  tagID: {
                    [Op.in]: existingIds,
                  },
                  barID: req.body.barID,
                  userID,
                },
              }
            );
            operations.push(updateOperation);
          }
          operations.push(userBarTags.bulkCreate(createData))
          await Promise.all(operations);
        }

        if (req.body.userConsent != undefined && req.body.userConsent == 1) {
          await userConsentVenue.create({
            barID: req.body.barID,
            userID: userID,
            createdAt: moment().tz('Australia/Perth').format('YYYY-MM-DD H:m:s')
          })
        }

        //change order Table Number...
        if (tableFlag == true) {
          orderTableNumber.create({
            orderID,
            tableCode: req.body.table_code
          })
          orderResponse.dataValues.tableCode = req.body.table_code
        }
        // configuring POS order details
        // let orderCurrentStateDetails, posConfigured;
        // if (isPOSConfigured) {
        //   const { POS } = require("./POS");
        //   posConfigured = new POS(barData.id)

        //   orderCurrentStateDetails = await posConfigured.handlePosOrder(orderResponse, 'ORDER');
        // }


        // configuring POS order product item details
        for (const requestOrderItem of requestOrderItems) {
          // let productDetail = await commonFunction.getProductDetail(requestOrderItem['productID']);
          const orderItemObj = {
            orderID: orderID,
            productID: requestOrderItem['productID'],
            price: requestOrderItem['price'],
            quantity: requestOrderItem['quantity'],
            specialRequest: requestOrderItem['specialRequest'],
            waitTime: requestOrderItem.productDetail.productWaitTime ? requestOrderItem.productDetail.productWaitTime : '00:10:00',
            createdAt: new Date(),
            PreparingStartTime: new Date(),
          }
          await orderItems
            .create(orderItemObj)
            .then(async orderResponse => {

              if (barData.dataValues.serviceType == 'BOTH' || barData.dataValues.serviceType == serviceType) {
                if (barData.dataValues.waitTimeServiceType == 'BOTH' || barData.dataValues.waitTimeServiceType == serviceType) {
                  // Create wait time complete notifications
                  await orderItemWaitTimeNotifications.create({
                    orderID: orderID,
                    orderItemID: orderResponse.id,
                    userID: userID,
                    barID: requestOrderItem.productDetail.dataValues.barID,
                    pickupLocationID: requestOrderItem.productDetail.dataValues.pickupLocationID,
                    waitTime: requestOrderItem.productDetail.productWaitTime ? requestOrderItem.productDetail.productWaitTime : '00:10:00',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                  });
                }
              }

              let basePrice = Number(orderResponse.price)



              const arrProductItemVariantsItems = []
              const productItemVariantsItems = requestOrderItem['productItemVariants']
              if (Array.isArray(productItemVariantsItems) && productItemVariantsItems.length > 0) {
                for (const productItemVariantsItem of productItemVariantsItems) {
                  const orderVariants = {
                    orderItemID: orderResponse.id,
                    productVariantsID: productItemVariantsItem['productVariantsID'],
                    price: productItemVariantsItem['price'],
                    createdAt: new Date()
                  }
                  arrProductItemVariantsItems.push(orderVariants)
                  basePrice = Number(productItemVariantsItem['price'])
                }
              }

              const arrProductItemExtrasItems = []
              const productItemExtrasItems = requestOrderItem['productItemExtras']
              if (Array.isArray(productItemExtrasItems) && productItemExtrasItems.length > 0) {
                for (const productItemExtrasItem of productItemExtrasItems) {
                  const orderItemExtras = {
                    orderItemID: orderResponse.id,
                    productExtrasID: productItemExtrasItem['productExtrasID'],
                    price: productItemExtrasItem['price'],
                    createdAt: new Date()
                  }
                  arrProductItemExtrasItems.push(orderItemExtras)
                  basePrice += Number(productItemExtrasItem['price'])
                }
              }

              const productVariantTypes = requestOrderItem['productVariantTypes'];
              if (Array.isArray(productVariantTypes) && productVariantTypes.length > 0) {
                for (const productVariantType of productVariantTypes) {
                  orderProductVariantTypes.create({
                    orderItemID: orderResponse.id,
                    productVariantTypeID: productVariantType['productVariantTypeID'],
                    createdAt: new Date(),
                  }).then(async (productVariantTypesData) => {
                    orderProductVariantSubTypes.create({
                      orderProductVariantTypeID: productVariantTypesData.id,
                      productVariantTypeID: productVariantType.productVariantTypeID,
                      productVariantSubTypeID: productVariantType.productVariantSubTypes.productVariantSubTypeID,
                      orderItemID: orderResponse.id,
                      price: productVariantType.productVariantSubTypes.price
                    });
                  });
                  basePrice += Number(productVariantType.productVariantSubTypes.price);
                }
              }

              if (totalDiscount > 0) {
                // Calculate discount per unit proportionally based on this item's unit price.
                const discountPerUnit = parseFloat(
                  ((basePrice / req.body.subTotal) * totalDiscount).toFixed(2)
                );
                const discountedPrice = parseFloat(
                  (basePrice - discountPerUnit).toFixed(2)
                );
                await orderResponse.update({
                  chargeAmount: basePrice,
                  discountedAmount: discountedPrice,
                });
              } else {
                await orderResponse.update({
                  chargeAmount: basePrice,
                  discountedAmount: basePrice,
                });
              }

              if (arrProductItemVariantsItems.length > 0) {
                orderItemVariants.bulkCreate(arrProductItemVariantsItems)
                // if (isPOSConfigured)
                //   orderCurrentStateDetails = await posConfigured.handlePosOrder(arrProductItemVariantsItems, 'VARIANTS', orderCurrentStateDetails)
              }

              if (arrProductItemExtrasItems.length > 0) {
                orderItemExtras.bulkCreate(arrProductItemExtrasItems)
              }
            })
          // if (isPOSConfigured)
          //   orderCurrentStateDetails = await posConfigured.handlePosOrder(requestOrderItem, 'ITEMS', orderCurrentStateDetails)
        }
        if (req.body.paymentType != '1') {
          var posOrderFee = 0
          var isPosOrder = '0'
          if (barData.dataValues.posStatus == 1) {
            posOrderFee = barData.dataValues.posFee
            isPosOrder = '1'
          }
          var docketOrderFee = 0
          var isDocketOrder = '0'
          if (barData.dataValues.docketStatus == 1) {
            docketOrderFee = barData.dataValues.docketCommission
            isDocketOrder = '1'
          }
          await orderResponse.update({
            paymentType: req.body.paymentType,
            paymentStatus: 'notReceived',
            posOrderFee: posOrderFee,
            isPosOrder: isPosOrder,
            docketOrderFee: docketOrderFee,
            isDocketOrder: isDocketOrder
          })
          cartItems.destroy({
            where: {
              userID: userID
            }
          })
          return res.status(200).send({
            success: 1,
            message: 'Order placed successfully!',
            data: orderResponse
          })
        } else {
          try {
            const source = await stripe.sources.create({
              customer: userData.stripeID,
              original_source: req.body.cardid,
            }, {
              stripeAccount: barData.stripeID,
            });
            let myTabFee = req.body.transactionFee
            let totalFee = Number(myTabFee)

            var docketOrderFee = 0
            var isDocketOrder = '0'
            if (barData.dataValues.docketStatus == 1) {
              let commission = barData.dataValues.docketCommission > 0 ? barData.dataValues.docketCommission : 0;
              let docketCommission = Number(((commission / 100) * (parseFloat(req.body.subTotal) + parseFloat(totalTaxAmount))).toFixed(2))
              totalFee = Number(totalFee) + Number(docketCommission)
              docketOrderFee = commission
              isDocketOrder = '1'
            }


            var posOrderFee = 0
            var isPosOrder = '0'
            if (barData.dataValues.posStatus == 1) {
              let commission = parseFloat(barData.dataValues.posFee) > 0 ? barData.dataValues.posFee : 0;
              let posFee = Number(((commission / 100) * (parseFloat(req.body.subTotal) + parseFloat(totalTaxAmount))).toFixed(2))
              totalFee = Number(totalFee) + Number(posFee)
              posOrderFee = commission
              isPosOrder = '1'
            }
            totalFee = Number(totalFee.toFixed(2))
            let payAmount = Math.round(req.body.total * 100)
            await stripe.charges.create(
              {
                amount: payAmount,
                currency: 'aud',
                source: source.id,
                description: `ORDER ID #${orderResponse.orderNo}`,
                transfer_group: orderID,
                application_fee_amount: Math.round((totalFee * 100)),
              },
              {
                stripeAccount: barData.stripeID
              },
              async function (err, charge) {
                if (err == null) {
                  for (const requestOrderItem of requestOrderItems) {
                    if (requestOrderItem.productDetail && requestOrderItem.productDetail.isStockLimit == "Yes") {
                      product.update({
                        stock: Number(requestOrderItem.productDetail.stock) - requestOrderItem['quantity'],
                      }, {
                        where: {
                          id: requestOrderItem['productID']
                        },
                      })
                    }
                  }
                  const chargeData = await stripe.charges.retrieve(charge.id, {
                    expand: ['balance_transaction']
                  }, {
                    stripeAccount: barData.stripeID,
                  });
                  var stripeFee = 0;
                  if (chargeData.balance_transaction.fee_details) {
                    chargeData.balance_transaction.fee_details.map((fee) => {
                      if (fee.type == 'stripe_fee' || fee.type == 'tax') {
                        stripeFee = stripeFee + (fee.amount / 100)
                      }
                    });
                  }
                  stripeFee = parseFloat(stripeFee.toFixed(2))
                  await orderResponse.update(
                    {
                      paymentStatus: 'received',
                      transactionID: charge.id,
                      transferID: charge.transfer,
                      cardType: charge.payment_method_details.card.brand,
                      cardNumber: charge.payment_method_details.card.last4,
                      posOrderFee: posOrderFee,
                      isPosOrder: isPosOrder,
                      docketOrderFee: docketOrderFee,
                      isDocketOrder: isDocketOrder,
                      stripeFee: stripeFee
                    }
                  )
                  transLogsModel.create({
                    orderID: orderID,
                    amout: req.body.total,
                    transaction_type: 'new_order',
                    transactionID: charge.id,
                    transferID: charge.transfer,
                    log: JSON.stringify(charge),
                    userID: userID,
                    barID: orderResponse.barID,
                    createdAt: new Date()
                  });
                  // if (isPOSConfigured) {
                  //   const subTotal = parseFloat(orderResponse.subTotal)
                  //   let transactionObj = {
                  //     reference: charge.transfer,
                  //     invoice: orderResponse.orderNo,
                  //     method: orderResponse.cardType.toLowerCase(),
                  //     stripeAmount: parseFloat(orderResponse.total).toFixed(2),
                  //     subTotal,
                  //     promocodeAmount: orderResponse.promocode_amount ? orderResponse.promocode_amount : 0,
                  //   };
                  //   orderCurrentStateDetails = await posConfigured.handlePosOrder(transactionObj, 'TRANSACTION', orderCurrentStateDetails)
                  //   await posConfigured.submitPosOrder(orderCurrentStateDetails, orderID)
                  // }

                  let message
                  if (tableFlag)
                    message = `You have received a new order from table number # ${req.body.table_code}`
                  else
                    message = `You have received a new order. Pickup Code is # ${pickupCode}`
                  barNotification.create({
                    barID: orderResponse.barID,
                    notification_type: 'newOrder',
                    userID: userID,
                    dataID: orderID,
                    message: message,
                    createdAt: new Date()
                  })
                  commonFunction.newOrderNotificationToBarCategoryWise(userID, orderResponse.barID, orderID, 'newOrder', message)
                  let msg = "Print The Docket"
                  let contentAvailable = 1
                  docketNotification.create({
                    barID: orderResponse.barID,
                    notification_type: 'docketPrint',
                    userID: userID,
                    orderID: orderID,
                    message: msg,
                    contentAvailable: contentAvailable,
                    createdAt: new Date()
                  })
                  commonFunction.docketPrintNotification(userID, orderResponse.barID, orderID, 'docketPrint', msg, contentAvailable)
                  cartItems.destroy({ where: { userID: userID } })
                  if (barData.dataValues.posStatus == '1') {
                    if (req.body.order_service_type && req.body.order_service_type.toLowerCase() === 'table') {
                      commonFunction.createOrderinDoshii(barData.dataValues.venueId, orderID, 'table');
                    } else {
                      commonFunction.createOrderinDoshii(barData.dataValues.venueId, orderID, 'pickup');
                    }
                  }
                  return res.status(200).send({
                    success: 1,
                    message: 'Order placed successfully!',
                    data: orderResponse
                  })
                } else {
                  transErrorLogsModel.create({
                    orderID: orderID,
                    amout: req.body.total,
                    transaction_type: 'new_order_err',
                    log: JSON.stringify(err),
                    userID: userID,
                    barID: orderResponse.barID,
                    createdAt: new Date()
                  })

                  order.update(
                    {
                      paymentStatus: 'failed'
                    },
                    {
                      where: {
                        id: orderID
                      }
                    }
                  )
                  if (err.message == 'Your card was declined.') {
                    return res.status(200).send({
                      success: -1,
                      message: `There is an issue with your payment ${err ? ' because ' + err.message : ' Please correct the issue to place your order.'}`
                    })
                  } else {
                    return res.status(200).send({
                      success: -1,
                      message: `Order fail ${err ? ' because ' + err.message : ''}`
                    })
                  }
                }
              }
            )
          } catch (error) {
            console.log("error", error);
            order.update(
              {
                paymentStatus: 'failed'
              },
              {
                where: {
                  id: orderID
                }
              }
            )
            return res.status(200).send({
              success: 0,
              message: 'payment fail!'
            })
          }
        }
      }).error(function (err) {
        return res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    console.log(error)
    return res.status(200).send({
      success: 0,
      message: error.message
    })
  }
}

exports.orderDetail = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    var whereClauseOrder = []
    whereClauseOrder.push({
      isDeleted: 'No'
    })
    whereClauseOrder.push({
      userID: userID
    })
    whereClauseOrder.push({
      id: req.body.id
    })

    order
      .findAll({
        where: whereClauseOrder,
        attributes: [
          'id',
          'orderNo',
          'pickupCode',
          [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
          'subTotal',
          'transactionFee',
          'refundTransactionFee',
          'tax',
          'total',
          'orderDate',
          'orderStatus',
          'orderServiceType',
          'paymentType',
          'refundStatus',
          'promocode_id',
          'promocode_amount',
          'promocode_discount',
          'cardType',
          'cardNumber',
          'fundingType',
          'userID',
          'barID',
          'createdAt',
          'refundedDate',
          "totalDiscountedAmount",
        ],
        include: [
          {
            attributes: [
              'id',
              'orderID',
              'productID',
              'price',
              'quantity',
              'specialRequest',
              'isCanceled',
              'refundAmount',
              'refundedQuantity',
              "newRefundAmount",
              'discountedAmount',
              'waitTime',
              'orderStatus',
              'PreparingStartTime',
              'ReadyTime',
              'PickedupTime',
              [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
              [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
              [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
            ],
            model: orderItems,
            include: [
              {
                attributes: [
                  'id',
                  'name',
                  'description',
                  'avatar'
                ],
                model: product,
                include: [
                  {
                    attributes: [
                      'id',
                      'description',
                      'address'
                    ],
                    model: pickupLocation,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productExtrasID',
                  'price'
                ],
                model: orderItemExtras,
                include: [
                  {
                    attributes: [
                      'id',
                      'extraItem'
                    ],
                    model: productExtras,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productVariantsID',
                  'price'
                ],
                model: orderItemVariants,
                include: [
                  {
                    attributes: [
                      'id',
                      'variantType'
                    ],
                    model: productVariants,
                  }
                ]
              },
              {
                attributes: [
                  ['id', "orderProductVariantTypeID"],
                  'orderItemID',
                ],
                where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                model: orderProductVariantTypes,
                required: false,
                include: [
                  {
                    attributes: [
                      ['id', "productVariantTypeID"],
                      'label',
                    ],
                    model: productVariantTypes,
                    required: true,
                    include: [
                      {
                        attributes: [
                          ['id', "orderProductVariantSubTypeID"],
                          'orderItemID',
                        ],
                        where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                        model: orderProductVariantSubTypes,
                        include: [
                          {
                            attributes: [
                              ['id', "productVariantSubTypeID"],
                              ['variantType', "extraItem"],
                              'price',
                            ],
                            model: productVariantSubTypes,
                          }
                        ]
                      }
                    ]
                  }
                ],
              }
            ]
          },
          {
            required: true,
            model: bar,
            attributes: ['id', 'restaurantName', 'email', 'businessRegisterId', 'address', 'avatar']
          },
          {
            model: orderDiscount,
            attributes: [
              'id',
              'orderID',
              'discountID',
              'discountCode',
              'discountType',
              'type',
              'discountValue',
              'discountAmount',
              'createdAt',
            ]
          },
          {
            attributes: [
              'id', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ]
      }).then(order => {
        if (order.length) {
          let newOrderItems = groupByOrderItems(order[0].order_items, order[0].orderServiceType, order[0].refundStatus); // Group By order items
          // delete order[0].dataValues.order_items; // Delete old key
          order[0].dataValues['order_items_group'] = newOrderItems; // Add new key to object
          res.status(200).send({
            success: 1,
            message: 'order detail retrive successfully!',
            data: order[0]
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: {}
          })
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}


exports.barOrderHistoryNewUpdated = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID;
    // let getUserCategory = await barAccessToken.findOne({
    //   where: {
    //     barID: barID,
    //     accessToken: req.headers.accesstoken,
    //   }
    // })
    // getUserCategory = getUserCategory?.subCategoryIDs  !== null ? getUserCategory?.subCategoryIDs?.split(',') : [];

    const whereClauseOrder = [];
    const whereClauseProduct = [];
    const whereClauseOrderItems = [];
    const whereClauseRefundedItem = []

    const orderAttributes = [
      'id',
      'orderNo',
      'subTotal',
      'transactionFee',
      'refundTransactionFee',
      'tax',
      'total',
      'orderDate',
      'orderStatus',
      'refundStatus',
      'paymentType',
      'promocode_id',
      'promocode_amount',
      'promocode_discount',
      'userID',
      'barID',
      'orderServiceType',
      "totalDiscountedAmount",
      [
        Sequelize.literal(
          '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
        ),
        'totalCancelItem'
      ],
      [
        Sequelize.literal(
          '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id)'
        ),
        'totalOrderItems'
      ],
      [
        Sequelize.literal(
          '(select sum(order_tax.amount) from order_tax WHERE order_tax.orderID = `orders`.id)'
        ),
        'totalOrderTax'
      ],
      [
        Sequelize.literal(
          '(select sum(order_refund_tax.amount) from order_refund_tax WHERE order_refund_tax.orderID = `orders`.id)'
        ),
        'totalOrderRefundTax'
      ],
      'createdAt'
    ];

    whereClauseOrder.push({
      isDeleted: 'No',
      barID: barID
    })

    // if(getUserCategory.length > 0) {
    //   whereClauseProduct.push({
    //     subCategoryID : getUserCategory
    //   });
    // }

    if (req.body.startDate) {
      whereClauseOrder.push({
        convertedOrderDate: {
          [Op.gte]: req.body.startDate
        }
      });
    }

    if (req.body.endDate) {
      whereClauseOrder.push({
        convertedOrderDate: {
          [Op.lte]: req.body.endDate
        }
      });
    }

    if (req.body.search) {
      whereClauseOrder.push({
        orderNo: {
          [Op.like]: '%' + req.body.search + '%'
        }
      });
    }

    if (req.body.orderType && req.body.orderType != '') {
      if (req.body.orderType == 'current') {
        whereClauseOrder.push({
          [Op.or]: [
            {
              orderStatus: 'New'
            },
            {
              orderStatus: 'Preparing'
            },
          ],
          isCanceled: 'No',
          paymentStatus: 'received'
        })
        whereClauseOrderItems.push({
          [Op.or]: [
            {
              orderStatus: 'New'
            },
            {
              orderStatus: 'Preparing'
            },
            //   orderStatus: 'Pickup'
            // }
          ],
          isCanceled: 'No',
        })
      } else if (req.body.orderType == 'past') {
        whereClauseOrderItems.push({
          orderStatus: {
            [Op.notIn]: ['New', 'Preparing']
          }
        })
        whereClauseOrder.push({
          //isCanceled: 'No',
          paymentStatus: 'received'
        })
      } else if (req.body.orderType == 'refund') {
        whereClauseOrder.push({
          [Op.or]: [
            {
              refundStatus: 'Refunded'
            },
            {
              refundStatus: 'PartialRefunded'
            },
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated'),
          ],
          paymentStatus: 'received'
        })
        // whereClauseRefundedItem.push({
        //   [Op.or]: [
        //     {
        //       isCanceled: 'Yes'
        //     },
        //     {
        //       refundedQuantity: {
        //         [Op.gt]: 0
        //       }
        //     },
        //     sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated')
        //   ]
        // })
      } else if (req.body.orderType == 'cancel') {
        whereClauseOrderItems.push({
          [Op.and]: [
            {
              isCanceled: 'Yes',
            }
          ]
        })
        whereClauseOrder.push({
          [Op.and]: [
            {
              refundStatus: 'No'
            }
          ]
        })
      } else if (req.body.orderType == 'Intoxicated') {
        whereClauseOrderItems.push({
          [Op.and]: [
            {
              isCanceled: 'No',
            }
          ]
        })
        whereClauseOrder.push({
          [Op.and]: [
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated'),
            {
              refundStatus: 'No'
            }
          ]
        })
      } else if (req.body.orderType == 'promo_code') {
        whereClauseOrder.push({
          [Op.or]: [
            {
              [Op.and]: [
                { promocode_id: { [Op.gt]: 0 } },
                { promocode_amount: { [Op.gt]: 0 } }
              ]
            },
            { totalDiscountedAmount: { [Op.gt]: 0 } },

          ]
        })
      }
    } else {
      whereClauseOrder.push({
        // [Op.or]: [
        //   {
        //     orderStatus: 'New'
        //   },
        //   {
        //     orderStatus: 'Preparing'
        //   },
        //   {
        //     orderStatus: 'Pickup'
        //   }
        // ],
        paymentStatus: 'received'
      })
      whereClauseOrderItems.push({
        [Op.or]: [
          {
            orderStatus: 'New'
          },
          {
            orderStatus: 'Preparing'
          },
          {
            orderStatus: 'Pickup'
          }
        ],
      })
    }

    let page = req.body.page ? req.body.page : 1;
    const offset = (page - 1) * 10;
    const limit = 10;

    const orderHistoryList = await order
      .findAndCountAll({
        where: [
          ...whereClauseOrder,
        ],
        attributes: [...orderAttributes, 'pickupCode'],
        include: [
          {
            required: true,
            attributes: [
              'id',
              'orderID',
              'productID',
              'price',
              'quantity',
              'specialRequest',
              'isCanceled',
              'refundAmount',
              'refundedQuantity',
              "newRefundAmount",
              "discountedAmount",
              'waitTime',
              'orderStatus',
              'PreparingStartTime',
              'ReadyTime',
              'PickedupTime',
              [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
              [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
              [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
            ],
            model: orderItems,
            where: [...whereClauseRefundedItem, ...whereClauseOrderItems],
            include: [
              {
                // where: [ {categoryID: [1,2] } ],
                // where: [ {categoryID: [1,2] }, ...whereClauseProduct ],
                attributes: [
                  'id',
                  'name',
                  'categoryID',
                  'subCategoryID',
                  'description',
                  'avatar',
                  'posID',
                ],
                required: false,
                model: product,
                include: [
                  {
                    attributes: [
                      'id',
                      'description',
                      'address'
                    ],
                    model: pickupLocation,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productExtrasID',
                  'price'
                ],
                model: orderItemExtras,
                include: [
                  {
                    attributes: [
                      'id',
                      'extraItem',
                      'posID',
                    ],
                    model: productExtras,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productVariantsID',
                  'price'
                ],
                model: orderItemVariants,
                include: [
                  {
                    attributes: [
                      'id',
                      'variantType'
                    ],
                    model: productVariants,
                  }
                ]
              },
              {
                attributes: [
                  ['id', "orderProductVariantTypeID"],
                  'orderItemID',
                ],
                where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                model: orderProductVariantTypes,
                required: false,
                include: [
                  {
                    attributes: [
                      ['id', "productVariantTypeID"],
                      'label',
                    ],
                    model: productVariantTypes,
                    required: true,
                  }
                ],
              }
            ]
          },
          {
            // required: true,
            model: user,
            attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
          },
          {
            model: coupons,
            attributes: ['id', 'code', 'name', 'description']
          },
          {
            model: orderDiscount,
            attributes: [
              'id',
              'orderID',
              'discountID',
              'discountCode',
              'discountType',
              'type',
              'discountValue',
              'discountAmount',
              'createdAt',
            ]
          },
          {
            model: orderTableNumber,
            attributes: ['tableCode']
          },
          {
            attributes: [
              'id', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ],
        order: [['createdAt', 'DESC']],
        distinct: true,
        duplicating: false,
        offset: offset,
        limit: limit,
      });

    if (orderHistoryList.count > 0) {
      for (let i = 0; i < orderHistoryList.rows.length; i++) {
        const order = orderHistoryList.rows[i];
        for (let j = 0; j < order.order_items.length; j++) {
          const item = orderHistoryList.rows[i].order_items[j];
          for (let k = 0; k < item.order_product_variant_types.length; k++) {
            const variant = orderHistoryList.rows[i].order_items[j].order_product_variant_types[k];
            let getProductSubVariant = await orderProductVariantSubTypes.find({
              attributes: [
                ['id', "orderProductVariantSubTypeID"],
                "orderItemID",
                [
                  Sequelize.literal(
                    '(select variantType from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                  ),
                  'extraItem'
                ],
                [
                  Sequelize.literal(
                    '(select price from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                  ),
                  'price'
                ],
                [
                  Sequelize.literal(
                    '(select id from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                  ),
                  'productVariantSubTypeID'
                ],
              ],
              where: {
                orderItemID: variant.orderItemID,
                productVariantTypeID: variant.product_variant_type.dataValues.productVariantTypeID,
              }
            });

            orderHistoryList.rows[i].order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type = {
              product_variant_sub_type: {
                productVariantSubTypeID: getProductSubVariant.dataValues.productVariantSubTypeID,
                extraItem: getProductSubVariant.dataValues.extraItem,
                price: getProductSubVariant.dataValues.price,
              },
            };



          }
        }
      }
      orderHistoryList.rows = orderHistoryList && orderHistoryList.rows.map((order) => {
        let newOrderItems = groupByOrderItems(order.order_items, order.orderServiceType, order.refundStatus); // Group By order items
        // delete order.dataValues.order_items; // Delete old key
        order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
        return order
      })

      res.status(200).send({
        success: 1,
        message: 'order list retrieve successfully',
        data: orderHistoryList
      })
    } else {
      res.status(200).json({
        success: 0,
        message: 'No Results Found',
        data: []
      })
    }
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}
exports.advertiserList = async (req, res) => {
  try {
    console.log('advertiserList...');
    const { page = 1, limit = 100, search } = req.body;
    const offset = (page - 1) * limit;
    const whereClause = '';
    if (search && search.trim()) {
      whereClause[Op.or] = [{ name: { [Op.like]: `%${search.trim()}%` } }];
    }
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID;
    console.log('userID...', userID);

    const adsData = await adsModel.findAndCountAll({
      attributes: [
        'id',
        'campaign_id',
        'bar_id',
        'ad_title',
        'ad_description',
        'media_url',
        'call_to_action',
        'start_date',
        'end_date',
        'ad_status',
        'pause_status',
        'payment_status',
        'created_at',
        'created_by_type',
      ],
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
    });
    res.status(200).send({
      success: 1,
      message: 'advertiser list retrieve successfully',
      data: adsData
    })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

// New function for user-facing filtered ads based on segment membership
exports.getUserAds = async (req, res) => {
  try {
    console.log('getUserAds...');
    const { page = 1, limit = 10, barID } = req.body;
    const offset = (page - 1) * limit;

    // Get user ID from session
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID;
    console.log('userID...', userID, 'barID...', barID);

    if (!barID) {
      return res.status(200).send({
        success: 0,
        message: 'barID is required'
      });
    }

    // Get current date and time for filtering active ads
    const currentDate = new Date();

    // Base where clause for ads
    const adsWhereClause = {
      bar_id: barID,
      ad_status: 'Approved',
      pause_status: false,
      payment_status: 'paid',
      start_date: { [Op.lte]: currentDate },
      end_date: { [Op.gte]: currentDate }
    };

    // Get user's segments for this venue
    const userSegments = await segmentUserVenueModel.findAll({
      where: {
        userID: userID,
        barID: barID
      },
      attributes: ['segmentID']
    });

    const userSegmentIds = userSegments.map(segment => segment.segmentID);
    console.log('User segments:', userSegmentIds);

    let adsData;

    if (userSegmentIds.length > 0) {
      // User has segments - get ads that either:
      // 1. Have no segment restrictions (eligibility_type = 'all_mytab_customers')
      // 2. Are targeted to user's segments

      // First get ads for all customers
      const allCustomerAds = await adsModel.findAll({
        attributes: [
          'id',
          'campaign_id',
          'bar_id',
          'ad_title',
          'ad_description',
          'media_url',
          'call_to_action',
          'call_to_action_url',
          'start_date',
          'end_date',
          'eligibility_type',
          'created_at'
        ],
        where: {
          ...adsWhereClause,
          eligibility_type: 'all_mytab_customers'
        }
      });

      // Then get ads targeted to user's segments
      const segmentTargetedAds = await adsModel.findAll({
        attributes: [
          'id',
          'campaign_id',
          'bar_id',
          'ad_title',
          'ad_description',
          'media_url',
          'call_to_action',
          'call_to_action_url',
          'start_date',
          'end_date',
          'eligibility_type',
          'created_at'
        ],
        include: [{
          model: adsSegmentModel,
          as: 'segments',
          where: {
            segmentID: { [Op.in]: userSegmentIds },
            barID: barID
          },
          attributes: []
        }],
        where: {
          ...adsWhereClause,
          eligibility_type: 'mytab_customer_segments'
        }
      });

      // Combine and deduplicate ads
      const allAds = [...allCustomerAds, ...segmentTargetedAds];
      const uniqueAds = allAds.filter((ad, index, self) =>
        index === self.findIndex(a => a.id === ad.id)
      );

      // Sort by created_at DESC
      uniqueAds.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      // Apply pagination
      const paginatedAds = uniqueAds.slice(offset, offset + parseInt(limit));

      adsData = {
        count: uniqueAds.length,
        rows: paginatedAds
      };
    } else {
      // User has no segments - only show ads for all customers
      adsData = await adsModel.findAndCountAll({
        attributes: [
          'id',
          'campaign_id',
          'bar_id',
          'ad_title',
          'ad_description',
          'media_url',
          'call_to_action',
          'call_to_action_url',
          'start_date',
          'end_date',
          'eligibility_type',
          'created_at'
        ],
        where: {
          ...adsWhereClause,
          eligibility_type: 'all_mytab_customers'
        },
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset
      });
    }

    res.status(200).send({
      success: 1,
      message: 'User ads retrieved successfully',
      data: adsData,
      userSegments: userSegmentIds
    });

  } catch (error) {
    console.log('Error in getUserAds:', error);
    res.status(200).send({
      success: 0,
      message: 'Error retrieving ads'
    });
  }
}

// Alternative efficient implementation using raw SQL for better performance
exports.getUserAdsOptimized = async (req, res) => {
  try {
    console.log('getUserAdsOptimized...');
    const { page = 1, limit = 10, barID } = req.body;
    const offset = (page - 1) * limit;

    // Get user ID from session
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID;
    console.log('userID...', userID, 'barID...', barID);

    if (!barID) {
      return res.status(200).send({
        success: 0,
        message: 'barID is required'
      });
    }

    // Get current date for filtering active ads
    const currentDate = new Date().toISOString().slice(0, 19).replace('T', ' ');

    // Raw SQL query to get filtered ads based on user segments
    const query = `
      SELECT DISTINCT
        a.id,
        a.campaign_id,
        a.bar_id,
        a.ad_title,
        a.ad_description,
        a.media_url,
        a.call_to_action,
        a.call_to_action_url,
        a.start_date,
        a.end_date,
        a.eligibility_type,
        a.created_at
      FROM ads a
      WHERE a.bar_id = :barID
        AND a.ad_status = 'Approved'
        AND a.pause_status = 0
        AND a.payment_status = 'paid'
        AND a.start_date <= :currentDate
        AND a.end_date >= :currentDate
        AND (
          a.eligibility_type = 'all_mytab_customers'
          OR (
            a.eligibility_type = 'mytab_customer_segments'
            AND a.id IN (
              SELECT DISTINCT ads_seg.adsID
              FROM ads_segment ads_seg
              INNER JOIN segment_user_venue suv ON ads_seg.segmentID = suv.segmentID
              WHERE ads_seg.barID = :barID
                AND suv.userID = :userID
                AND suv.barID = :barID
            )
          )
        )
      ORDER BY a.created_at DESC
      LIMIT :limit OFFSET :offset
    `;

    const countQuery = `
      SELECT COUNT(DISTINCT a.id) as total
      FROM ads a
      WHERE a.bar_id = :barID
        AND a.ad_status = 'Approved'
        AND a.pause_status = 0
        AND a.payment_status = 'paid'
        AND a.start_date <= :currentDate
        AND a.end_date >= :currentDate
        AND (
          a.eligibility_type = 'all_mytab_customers'
          OR (
            a.eligibility_type = 'mytab_customer_segments'
            AND a.id IN (
              SELECT DISTINCT ads_seg.adsID
              FROM ads_segment ads_seg
              INNER JOIN segment_user_venue suv ON ads_seg.segmentID = suv.segmentID
              WHERE ads_seg.barID = :barID
                AND suv.userID = :userID
                AND suv.barID = :barID
            )
          )
        )
    `;

    const replacements = {
      barID: barID,
      userID: userID,
      currentDate: currentDate,
      limit: parseInt(limit),
      offset: offset
    };

    // Execute both queries using the global sequelize instance
    const [adsResult, countResult] = await Promise.all([
      sequelize.query(query, {
        replacements,
        type: Sequelize.QueryTypes.SELECT
      }),
      sequelize.query(countQuery, {
        replacements,
        type: Sequelize.QueryTypes.SELECT
      })
    ]);

    // Format media URLs
    const formattedAds = adsResult.map(ad => ({
      ...ad,
      media_url: ad.media_url ? `${env.awsPrivateBucketCloudFrontURL}${env.awsAdsFolder}${ad.media_url}` : ''
    }));

    res.status(200).send({
      success: 1,
      message: 'User ads retrieved successfully',
      data: {
        count: countResult[0].total,
        rows: formattedAds
      }
    });

  } catch (error) {
    console.log('Error in getUserAdsOptimized:', error);
    res.status(200).send({
      success: 0,
      message: 'Error retrieving ads'
    });
  }
}