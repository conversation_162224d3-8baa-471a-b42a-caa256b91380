var Sequelize = require('sequelize')
const pickupLocationSubCategory = require('./pickupLocationSubCategory')

var sub_category = sequelize.define(
  'sub_category',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    categoryID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "category",
        key: "id"
      }
    },
    name: Sequelize.TEXT,
    posID: Sequelize.INTEGER,
    fromPosId: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "pos_conf",
        key: "id"
      }
    },
    status: Sequelize.ENUM('Active', 'Inactive'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

// Associations
sub_category.hasOne(pickupLocationSubCategory, { foreignKey: 'subCategoryID' })

module.exports = sub_category