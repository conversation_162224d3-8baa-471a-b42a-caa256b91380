const Sequelize = require('sequelize');

const userBarTags = sequelize.define(
	'user_bar_tags',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		userID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'user',
				key: 'id'
			}
		},
		orderID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'orders',
				key: 'id'
			}
		},
		tagID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'segment_tags',
				key: 'id'
			}
		},
    barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'bar',
				key: 'id'
			}
		},
		createdAt: { type: Sequelize.DATE, allowNull: false },
		updatedAt: { type: Sequelize.DATE },
	},
	{
		freezeTableName: true,
		timestamps: true,
		underscored: false,
	}
)

module.exports = userBarTags