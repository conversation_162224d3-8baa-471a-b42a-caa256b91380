var Sequelize = require('sequelize')

var pickupLocation = sequelize.define(
  'pickup_location',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    description: Sequelize.TEXT,
    address: Sequelize.TEXT,
    latitude: Sequelize.TEXT,
    longitude: Sequelize.TEXT,
    status: Sequelize.ENUM('Active', 'Inactive'),
    isDefault: {
      type: Sequelize.ENUM('0', '1'),
      defaultValue: '0'
    },
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = pickupLocation