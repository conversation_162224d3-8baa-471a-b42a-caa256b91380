var Sequelize = require('sequelize')
var bar = require('./bar')

var role = sequelize.define(
  'role',
  {
    id:{
      type:Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    email: Sequelize.TEXT,
    secondaryEmail: {
      type: Sequelize.TEXT,
      allowNull: true,
    } 
  },
  {
    freezeTableName: true,
    timestamps: false
  },
)

role.belongsTo(bar, { foreignKey: "barID"})

module.exports = role