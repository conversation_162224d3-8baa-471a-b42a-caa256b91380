const http = require('http')
const express = require('express')
const app = express()
const morgan = require('morgan')
const bodyParser = require('body-parser')
const dotenv = require('dotenv')
const querystring = require('querystring')
dotenv.config()
var jwt = require('jsonwebtoken')
var env = require('./config/environment')
const bootstrap = require('./bootstrap');

async function startServer() {
  try {
    await bootstrap();

    const PORT = env.port;
    const actuator = require('express-actuator');
    app.use(morgan('dev'))
    app.use(bodyParser.json({ limit: "50mb" }))
    app.use(bodyParser.urlencoded({ limit: "50mb", extended: true, parameterLimit: 50000 }))
    app.use(express.json());
    app.use(actuator({ infoGitMode: 'full' }));

    app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*')
      res.header(
        'Access-Control-Allow-Headers',
        'Origin, X-Requested-With, Content-Type, Accept, Authorization, xapikey, accesstoken'
      )
      if (req.method === 'OPTIONS') {
        res.header('Access-Control-Allow-Methods', 'PUT, POST, PATCH, DELETE, GET')
        return res.status(200).json({})
      }
      next()
    })

    const server = http.createServer(app)
    require("./config/cronJob")
    const indexRoutes = require('./routes/index')
    app.use('/api/', indexRoutes)

    app.use('/login', (req, res, next) => {
      var dataResponse = req.body;
      var decoded = jwt.decode(dataResponse.id_token);
      if (req.body.user) {
        const userData = JSON.parse(req.body.user);
        decoded.name = userData.name.firstName + ' ' + userData.name.lastName;
      }
      const query = querystring.stringify(decoded)
      return res.redirect('/loginSuccess?' + query);
    })

    app.use('/loginSuccess', (req, res, next) => {
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.write('<html><body><p>MyTab</p></body></html>');
      res.end();
    })

    app.use((req, res, next) => {
      const error = new Error('Not found')
      error.status = 404
      next(error)
    })

    app.use((error, req, res, next) => {
      res.status(error.status || 500)
      res.json({
        success: 0,
        message: error.message,
        error: error
      })
    })

    server.listen(PORT, () => {
      console.log('Express listening on port:', PORT)
    })
    // module.exports = app
  }

  catch (error) {
    console.error('Error starting server:', error);
    process.exit(1);
  }
}

startServer();

