var Sequelize = require('sequelize')
var cartItems = require('./cartItems')
var productExtras = require('./productExtras')

var cart_item_extras = sequelize.define(
  'cart_item_extras',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    cartItemID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "cartItems",
        key: "id"
      }
    },
    productExtrasID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "productExtras",
        key: "id"
      }
    },
    price: Sequelize.FLOAT,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

cart_item_extras.belongsTo(productExtras, { foreignKey: 'productExtrasID' })

cartItems.hasMany(cart_item_extras, { foreignKey: 'cartItemID' })
cart_item_extras.belongsTo(cartItems, { foreignKey: 'cartItemID' })

module.exports = cart_item_extras
