var Sequelize = require('sequelize')
var role = require('./role')

var csvAndReport = sequelize.define(
  'csvAndReport',
  {
    id:{
      type:Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    roleID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "role",
        key: "id"
      }
    },
    frequency: Sequelize.ENUM('daily', 'weekly', 'fortnightly', 'monthly'),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    freezeTableName: true,
    timestamps: false
  },
)

csvAndReport.belongsTo(role, { foreignKey: "roleID"})
role.hasMany(csvAndReport, { foreignKey: "roleID"})

module.exports = csvAndReport