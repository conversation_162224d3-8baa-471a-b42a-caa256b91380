const express = require('express')
var multer = require('multer')
const router = express()

const checkAuth = require('../middleware/barCheckAuth')
const checkUserAuth = require('../middleware/checkAuth')
const bar = require('../controller/bar')
const POS = require("../controller/POS");

var upload = multer({})

router.post('/register', bar.register)
router.post('/checkEmail', upload.array(), bar.checkEmail)
router.post('/login', upload.array(), bar.login)
router.get('/getProfile', upload.array(), checkAuth, bar.getProfile)
router.post('/editProfile', checkAuth, bar.editProfile)
router.post('/updateDeviceToken', checkAuth, bar.updateDeviceToken)
router.get('/readPopupMessage', checkAuth, bar.readPopupMessage)
router.post('/updateMobile', upload.array(), checkAuth, bar.updateMobile)
router.post('/changePassword', upload.array(), checkAuth, bar.changePassword)
router.post('/resetPassword', upload.array(), bar.resetPassword)
router.post('/verifyResetPasswordCode', upload.array(), bar.verifyResetPasswordCode)
router.post('/updatePassword', upload.array(), bar.updatePassword)
router.post('/logout', upload.array(), checkAuth, bar.logout)
router.post('/delete', upload.array(), checkAuth, bar.delete)
router.post('/changePasscode', upload.array(), checkAuth, bar.changePasscode)
router.post('/updatePasscodeStatus', upload.array(), checkAuth, bar.updatePasscodeStatus)
router.post('/updatePasscode', upload.array(), checkAuth, bar.updatePasscode)
router.post('/verifyPasscode', upload.array(), checkAuth, bar.verifyPasscode)
router.post('/contactUs', upload.array(), bar.contactUs)
router.post('/updateServiceTypeFlag', upload.array(), checkAuth, bar.isVenueUpdatableController, bar.updateServiceTypeFlag)
router.post('/getVenueOperatingHours', upload.array(), checkAuth, bar.getVenueOperatingHours)
router.post('/updateVenueOperatingHours', upload.array(), checkAuth, bar.updateVenueOperatingHours)
router.post('/enableSubCategoryIds', upload.array(), checkAuth, bar.enableSubCategoryIds)
router.post('/updateWaitTimeServiceType', upload.array(), checkAuth, bar.updateWaitTimeServiceType)
// router.get('/addWaitTimeForExistingActiveHours', upload.array(), checkAuth, bar.addWaitTimeForExistingActiveHours)

/*User API*/
router.post('/getBars', upload.array(), checkUserAuth, bar.getBars)

// validate doshii venue id
router.post('/validateVenueId', upload.array(), bar.validateVenueId);
router.post('/syncMenuItems', upload.array(), checkAuth, bar.syncMenuItems);
router.post('/updateVenueId', upload.array(), checkAuth, bar.updateVenueId);
// router.post('/updateVenueServeAlchohol', upload.array(), checkAuth, bar.updateVenueServeAlchohol);

// Wait time handling
router.post('/updateWaitTime', upload.array(), checkAuth, bar.updateWaitTime);
router.post('/getWaitTime', upload.none(), checkAuth, bar.getWaitTime);

// POS Handling
router.post('/posSetup', upload.array(), checkAuth, bar.isVenueUpdatableController, bar.posSetup);
router.post('/posSettingDetails', upload.array(), checkAuth, bar.posSettingDetails);
router.post('/manualNotificationsToAllUsers', upload.array(), bar.manualNotificationsToAllUsers)

// Active Hours for Specific Product
// router.post('/addItemActiveHours', upload.array(), checkAuth,bar.isOperatingHoursValid, bar.addItemActiveHours);
// router.post('/updateItemActiveHours', upload.array(), checkAuth,bar.isOperatingHoursValid, bar.updateItemActiveHours);
router.post('/deleteItemActiveHours', upload.array(), checkAuth, bar.deleteItemActiveHours);
router.post('/getItemActiveHours', upload.none(), checkAuth, bar.getItemActiveHours)
router.post('/addUpdateItemActiveHours', upload.array(), checkAuth, bar.isOperatingHoursValid, bar.addUpdateItemActiveHours)

// Wait time for specific hour and sub-heading
router.post('/getSubHeadingWaitTime', upload.array(), checkAuth, bar.getSubHeadingWaitTime);
router.post('/updateSubHeadingWaitTime', upload.array(), checkAuth, bar.updateSubHeadingWaitTime);
router.post('/updateSubHeadingWaitTimeForOldRecords', upload.array(), checkAuth, bar.updateSubHeadingWaitTimeForOldRecords);

// Promo-Code Coupons
router.post('/listPromoCode', upload.none(), checkAuth, bar.listPromoCode)
router.post('/addNewPromoCode',upload.array(), checkAuth, bar.addNewPromoCode);
router.post('/editPromoCode',upload.array(), checkAuth, bar.editPromoCode);
router.post('/deletePromoCode', upload.array(), checkAuth, bar.deletePromoCode);

// Printer 
router.post('/listPrinters', upload.none(), checkAuth, bar.listPrinters)
router.post('/addNewPrinter',upload.array(), checkAuth, bar.addNewPrinter);
router.post('/editPrinter',upload.array(), checkAuth, bar.editPrinter);
router.post('/deletePrinter', upload.array(), checkAuth, bar.deletePrinter);

// Export Excel Sheet
router.post('/addUpdateRole', upload.array(), checkAuth, bar.addUpdateRole)
router.post('/getRole', upload.none(), checkAuth, bar.getRole)

// 1. & 2. New Taxes Page under Settings....  Starts
// Taxes
router.post('/listTax', upload.none(), checkAuth, bar.listTax)
router.post('/addTax',upload.array(), checkAuth, bar.addTax);
router.post('/editTax',upload.array(), checkAuth, bar.editTax);
router.post('/deleteTax', upload.array(), checkAuth, bar.deleteTax);
 // 1. & 2. New Taxes Page under Settings....  Ends

 router.get('/setServiceType', upload.array(), checkAuth, bar.setServiceType)

router.post('/pauseOrder', upload.none(), checkAuth, bar.pauseOrder);
router.post('/pauseOrderStatus', upload.none(), checkUserAuth, bar.pauseOrderStatus); //For user
module.exports = router