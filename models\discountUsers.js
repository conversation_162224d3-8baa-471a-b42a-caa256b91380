const Sequelize = require('sequelize');
const discount = require('./discount');
const user = require('./user');
const bar = require('./bar');

const discountUsers = sequelize.define(
	'discount_users',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		userID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'user',
				key: 'id'
			}
		},
		discountID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'discount',
				key: 'id'
			}
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'bar',
				key: 'id'
			}
		}
	},
	{
		timestamps: false,
		freezeTableName: true,
		underscored: false,
	}
)

discount.hasMany(discountUsers, {
	foreignKey: 'discountID'
});
discountUsers.belongsTo(discount, {
	foreignKey: 'discountID'
});

user.hasMany(discountUsers, {
	foreignKey: 'userID'
});
discountUsers.belongsTo(user, {
	foreignKey: 'userID'
});

bar.hasMany(discountUsers, {
	foreignKey: 'barID'
});
discountUsers.belongsTo(bar, {
	foreignKey: 'barID'
});


module.exports = discountUsers