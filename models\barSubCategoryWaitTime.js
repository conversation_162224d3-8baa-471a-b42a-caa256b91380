var Sequelize = require('sequelize')
var sub_category = require('./subCategory')
var bar = require('./bar')

var barSubCategoryWaitTime = sequelize.define(
  'bar_sub_category_wait_time',
  {
    id:{
      type:Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    subCategoryID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references:{
        model: "sub_category",
        key: "id"
      }
    },
    barSubCategoryOpeningHoursID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'bar_sub_category_opening_hours',
        key: 'id'
      }
    },
    waitTimeType: Sequelize.ENUM('1', '2'),
    weekDay: Sequelize.SMALLINT,
    startTime: Sequelize.TIME,
		endTime: Sequelize.TIME,
		waitTime: Sequelize.TIME,
  },
  {
    freezeTableName: true,
    timestamps: false
  },
)

barSubCategoryWaitTime.belongsTo(sub_category, { foreignKey: "subCategoryID" })
sub_category.hasMany(barSubCategoryWaitTime, {  foreignKey: "subCategoryID", as: 'barSubCategoryWaitTime' })

barSubCategoryWaitTime.belongsTo(bar, { foreignKey: "barID"})
bar.hasMany(barSubCategoryWaitTime, { foreignKey: "barID"})

module.exports = barSubCategoryWaitTime