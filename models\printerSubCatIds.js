const Sequelize = require('sequelize');
const bar = require('./bar');
const subCategory = require('./subCategory');
const printerConnect = require('./printerConnect');

const printerSubCatIds = sequelize.define(
	'printerSubCatIds',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		printerConnectID: {
			type: Sequelize.INTEGER,
			allowNull: true,
			references: {
				model: "printerConnect",
				key: "id"
			},
		},
		subCategoryID: {
			type: Sequelize.INTEGER,
			allowNull: true,
			references: {
				model: "sub_category",
				key: "id"
			},
		},
		// createdAt: Sequelize.DATE,
		// updatedAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false,
	},
);

printerSubCatIds.belongsTo(printerConnect, { foreignKey: "printerConnectID" })
printerConnect.hasMany(printerSubCatIds, { foreignKey: "printerConnectID" })

printerSubCatIds.belongsTo(subCategory, { foreignKey: "subCategoryID" })
subCategory.hasMany(printerSubCatIds, { foreignKey: "subCategoryID" })

module.exports = printerSubCatIds
