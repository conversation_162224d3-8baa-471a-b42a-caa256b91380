ALTER TABLE `product` ADD `stock` VARCHAR(50) NULL DEFAULT NULL AFTER `posID`, ADD `isStockLimit` ENUM('Yes', 'No') NOT NULL DEFAULT 'No' AFTER `stock`;
ALTER TABLE `product` ADD `dailyStockRenewal` VARCHAR(50) NULL DEFAULT NULL AFTER `stock`;
ALTER TABLE `tax` ADD `deletedAt` TIMESTAMP NULL DEFAULT NULL AFTER `updatedAt`;
ALTER TABLE `orders` ADD `isDocketOrder` ENUM('0','1') NOT NULL DEFAULT '0' COMMENT '0-No , 1-Yes' AFTER `posTransactionVersion`, ADD `docketOrderFee` VARCHAR(50) NOT NULL DEFAULT '0' AFTER `isDocketOrder`;
