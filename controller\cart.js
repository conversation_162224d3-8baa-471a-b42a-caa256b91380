var Sequelize = require('sequelize');

const Op = Sequelize.Op;

const commonFunction = require('../common/commonFunction')

const cartItems = require('../models/cartItems');
const cartItemExtras = require('../models/cartItemExtras');
const cartItemVariants = require('../models/cartItemVariants');
const cartProductVariantTypes = require('../models/cartProductVariantTypes');
const cartProductVariantSubTypes = require('../models/cartProductVariantSubTypes');
const productVariantTypes = require('../models/productVariantTypes');
const productVariantSubTypes = require('../models/productVariantSubTypes');
const itemActiveHours = require('../models/itemActiveHours');
const taxModel = require('../models/tax')  // 1. & 2. New Taxes Page under Settings.... 
const operatingBarTax = require('../models/operatingBarTax')  // 1. & 2. New Taxes Page under Settings.... 
const user = require('../models/user');
const bar = require('../models/bar');
const category = require('../models/category');
const product = require('../models/product');
const productVariants = require('../models/productVariants');
const productExtras = require('../models/productExtras');
const settings = require('../models/settings');
const barSubCategoryWaitTimeUTC = require('../models/barSubCategoryWaitTimeUTC');

var env = require('../config/environment');
var jwt = require('jsonwebtoken');
const moment = require('moment');
const barSubCategoryOpeningHoursUTC = require('../models/barSubCategoryOpeningHoursUTC');
var stripe = require('stripe')(env.stripe_secret_key);

exports.add = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID;
  let totalQty;
  try {
    const productData = await product.findOne({ where: { id: req.body.productID } });
    if (productData) {
      const cartItemCount = await cartItems.findOne({
        attributes: [
          'id',
          [Sequelize.fn('sum', Sequelize.col('quantity')), 'totalQuantity'],
        ],
        where: {userID: userID},
        group: ['userID'],
        include: [{
          required: true,
          attributes: [
            'id',
            'name',
            'price',
            'description',
            'avatar'
          ],
          model: product,
          where: {id: req.body.productID},
          include: [
            {
              required: true,
              where: {
                id: productData.categoryID
              },
              attributes: [
                'id',
                'name',
              ],
              model: category,
            }
          ]
        }]
      })

      if (cartItemCount) {
        totalQty = parseInt(cartItemCount.dataValues.totalQuantity) + parseInt(req.body.quantity)
        if (totalQty > 10) {
          var msg = "Your cart is full. Please Place your order!"
          if (productData.categoryID == 1) {
            msg = "You have reached your maximum drink cart limit(10)."
          } else {
            msg = "You have reached your maximum food cart limit(10)."
          }

          return res.status(200).json({
            data: 1,
            success: 0,
            message: msg,
          })
        }
      }

      cartItems
        .findOne({
          where: {
            userID: userID,
            barID: {
              $ne: req.body.barID
            }
          },
          include: [
            {
              model: bar,
              attributes: [
                'id',
                'restaurantName'
              ]
            }
          ]
        })
        .then(async cartDataResponse => {
          if (cartDataResponse) {
            res.status(200).json({
              success: 1,
              message: 'You currently have item/s in your cart from ' + cartDataResponse.bar.restaurantName + '.' + '\nWould you like to empty your cart?',
              data: 0
            });
          } else {

            let productDetail = await commonFunction.getProductDetail(req.body.productID);
            if(productDetail.isStockLimit == 'Yes'){
              const cartItemCount = await cartItems.findOne({
                attributes: [
                  'id',
                  [Sequelize.fn('sum', Sequelize.col('quantity')), 'totalQuantity'],
                ],
                where: { userID: userID },
                group: ['userID'],
                include: [{
                  required: true,
                  attributes: [
                    'id',
                    'name',
                    'price',
                    'description',
                    'avatar',
                  ],
                  model: product,
                  where: {
                    id: req.body.productID
                  }
                }]
              });
              if(cartItemCount){
                if(productDetail.stock == 0 || productDetail.stock < (parseInt(cartItemCount.dataValues.totalQuantity) + parseInt(req.body.quantity))){
                  return res.status(200).json({
                    success: 0,
                    message: 'Your cart has exceeded the available stock availability for one or more menu item(s)',
                    data: {}
                  });
                }
              }else{
                if(productDetail.stock == 0 || productDetail.stock < req.body.quantity){
                  return res.status(200).json({
                    success: 0,
                    message: 'Your cart has exceeded the available stock availability for one or more menu item(s)',
                    data: {}
                  });
                }
              }
            }
            
            const cartItemServiceType = await cartItems.findOne({ where: { userID: userID } });
            if(cartItemServiceType){
              if(cartItemServiceType.dataValues.cartServiceType != req.body.cartServiceType) {
                return res.status(200).send({
                  success: -5,
                  data: {},
                  // message: 'Some menu items in your cart are only available for ' + cartItemServiceType.dataValues.cartServiceType + ' service. Please clear your cart to create a new order, or return to the original service type selected to proceed with your existing order.'
                  message: 'You currently have items in your cart under a different service type. Please clear your cart to create a new order, or return to the original service type selected to proceed with your existing order.'
                }); 
              }
            }

            const barData = await bar.findOne({ where: { id: req.body.barID } });

            if(barData.dataValues.serviceType != "BOTH" && barData.dataValues.serviceType != req.body.cartServiceType) {
              return res.status(200).send({
                success: -4,
                data: {},
                message: 'Service for this item is currently not available!'
              }); 
            }
            
            if(productData.dataValues.serviceType != "BOTH" && productData.dataValues.serviceType != req.body.cartServiceType) {
              return res.status(200).send({
                success: -4,
                data: {},
                message: 'Service for this item is currently not available!'
              });
            }
            
            let reqProductVariantTypes = JSON.parse(req.body.productVariantTypes);
            for (const item of reqProductVariantTypes) {
              const productVariantTypeData = await productVariantTypes.findOne({ where: { id: item.id } });
              if(productVariantTypeData.dataValues.serviceType != "BOTH" && productVariantTypeData.dataValues.serviceType != req.body.cartServiceType) {
                return res.status(200).send({
                  success: -4,
                  data: {},
                  message: 'Service for this item is currently not available!'
                });
              }
            }

            cartItems
              .create({
                // where: {
                //   barID: req.body.barID,
                //   userID: userID,
                //   productID: req.body.productID,
                //   subCategoryID: req.body.subCategoryID,
                // },
                // defaults: {
                barID: req.body.barID,
                userID: userID,
                productID: req.body.productID,
                subCategoryID: req.body.subCategoryID,
                quantity: req.body.quantity,
                specialRequest: req.body.specialRequest,
                cartServiceType: req.body.cartServiceType,
                createdAt: new Date()
                // }
              })
              .then(async (cartResponse, isCreated) => {
                if (isCreated) {
                  cartResponse.update({
                    quantity: totalQty,
                    specialRequest: req.body.specialRequest,
                    cartServiceType: req.body.cartServiceType,
                  });
                }
                let cartItemID = cartResponse.id;
                try {
                  var arrProductItemExtrasItems = [];
                  var productItemExtrasItems = JSON.parse(req.body.productItemExtras);
                  for (const item of productItemExtrasItems) {
                    arrProductItemExtrasItems.push({
                      cartItemID: cartItemID,
                      productExtrasID: item['productExtrasID'],
                      createdAt: new Date()
                    });
                  }
                  if (arrProductItemExtrasItems.length > 0) {
                    cartItemExtras.bulkCreate(arrProductItemExtrasItems);
                  }

                  var arrProductItemVariantsItems = [];
                  var productItemVariantsItems = JSON.parse(req.body.productItemVariants);
                  for (const item of productItemVariantsItems) {
                    arrProductItemVariantsItems.push({
                      cartItemID: cartItemID,
                      productVariantsID: item['productVariantsID'],
                      createdAt: new Date()
                    });
                  }

                  if (arrProductItemVariantsItems.length > 0) {
                    cartItemVariants.bulkCreate(arrProductItemVariantsItems);
                  }

                  var productVariantTypes = JSON.parse(req.body.productVariantTypes);
                  for (const item of productVariantTypes) {
                    cartProductVariantTypes.create({
                      cartItemID: cartItemID,
                      productVariantTypeID: item.id,
                      createdAt: new Date()
                    }).then(async (productVariantTypesData) => {
                      let productVariantSubTypesArr = [];
                      for (const subType of item.productVariantSubTypes) {
                        productVariantSubTypesArr.push({
                          cartProductVariantTypeID: productVariantTypesData.id,
                          productVariantTypeID: item.id,
                          productVariantSubTypeID: subType,
                          cartItemID: cartItemID,
                        });
                      }
                      cartProductVariantSubTypes.bulkCreate(productVariantSubTypesArr);
                    });
                  }

                  await res.status(200).send({
                    success: 1,
                    message: 'Item added to cart successfully!',
                    data: 1
                  });
                } catch (error) {
                  console.log("error", error);
                  await cartItems.destroy({ where: { id: cartItemID } });
                  await cartItemExtras.destroy({ where: { cartItemID: cartItemID } });
                  await cartItemVariants.destroy({ where: { cartItemID: cartItemID } });
                  await cartProductVariantTypes.destroy({ where: { cartItemID: cartItemID } });
                  await cartProductVariantSubTypes.destroy({ where: { cartItemID: cartItemID } });
                  await res.status(200).send({
                    success: 0,
                    message: 'Item not added. Please try again!'
                  });
                }
              }).error(function (err) {
                res.status(200).json({
                  success: 0,
                  message: err.message,
                });
              });
          }
        }).error(function (err) {
          res.status(200).json({
            success: 0,
            message: err.message,
          });
        });
    } else {
      res.status(200).send({
        success: 0,
        message: 'Item not added. Please try again!'
      });
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: error.message
    });
  }
};

exports.deleteItem = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID;
  var cartItemID = req.body.id;
  try {
    cartItems
      .destroy({ where: { id: cartItemID } })
      .then(async function () {
        if (cartItemID) {
          cartItemExtras.destroy({ where: { cartItemID: cartItemID } });
          cartProductVariantTypes.destroy({ where: { cartItemID: cartItemID } });
          cartProductVariantSubTypes.destroy({ where: { cartItemID: cartItemID } });
        }
        res.json({
          success: 1,
          message: 'success!'
        });
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        });
      });
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    });
  }
};

exports.clearCart = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID;
  try {
    const cartID = await cartItems.findOne({ where: { userID: userID } });
    cartItems
      .destroy({ where: { userID: userID } })
      .then(async function (cartRes) {
        if (cartID) {
          cartItemExtras.destroy({ where: { cartItemID: cartID.id } });
          cartProductVariantTypes.destroy({ where: { cartItemID: cartID.id } });
          cartProductVariantSubTypes.destroy({ where: { cartItemID: cartID.id } });
        }
        res.json({
          success: 1,
          message: 'success!'
        });
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        });
      });
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    });
  }
};

exports.updateItem = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID;
  try {
    const productData = await product.findOne({ where: { id: req.body.productID } });
    var cartItemID = req.body.id;

    if (productData) {

      const barData = await bar.findOne({ where: { id: req.body.barID } });
      
      if(barData.dataValues.serviceType != "BOTH" && barData.dataValues.serviceType != req.body.cartServiceType) {
        cartItems.destroy({
          where:{
            barID: req.body.barID,
          },
        });
        return res.status(200).send({
          success: -4,
          data: {},
          message: 'Service for this item is currently not available!'
        }); 
      }
      
      let removeCartDataFlag = false;
      if(productData.dataValues.serviceType != "BOTH" && productData.dataValues.serviceType != req.body.cartServiceType) {
        removeCartDataFlag = true;
      }
      
      let reqProductVariantTypes = JSON.parse(req.body.productVariantTypes);
      for (const item of reqProductVariantTypes) {
        const productVariantTypeData = await productVariantTypes.findOne({ where: { id: item.id } });
        if(productVariantTypeData.dataValues.serviceType != "BOTH" && productVariantTypeData.dataValues.serviceType != req.body.cartServiceType) {
          removeCartDataFlag = true;
        }
      }

      if(removeCartDataFlag) {
        await cartItems.destroy({ where: { id: cartItemID } });
        await cartProductVariantTypes.destroy({ where: { cartItemID: cartItemID } });
        await cartProductVariantSubTypes.destroy({ where: { cartItemID: cartItemID } });
        return res.status(200).send({
          success: -4,
          data: {},
          message: 'Service for this item is currently not available!'
        });
      }

      let productDetail = await commonFunction.getProductDetail(req.body.productID);
      if(productDetail.isStockLimit == 'Yes'){
        const cartItemCount = await cartItems.findOne({
          attributes: [
            'id',
            [Sequelize.fn('sum', Sequelize.col('quantity')), 'totalQuantity'],
          ],
          where: { userID: userID, id: { [Op.ne] : cartItemID } },
          group: ['userID'],
          include: [{
            required: true,
            attributes: [
              'id',
              'name',
              'price',
              'description',
              'avatar',
            ],
            model: product,
            where: {
              id: req.body.productID
            }
          }]
        });
        if(cartItemCount){
          if(productDetail.stock == 0 || productDetail.stock < (parseInt(cartItemCount.dataValues.totalQuantity) + parseInt(req.body.quantity))){
            return res.status(200).json({
              success: 0,
              message: 'Your cart has exceeded the available stock availability for one or more menu item(s)',
              data: {}
            });
          }
        }else{
          if(productDetail.stock == 0 || productDetail.stock < req.body.quantity){
            return res.status(200).json({
              success: 0,
              message: 'Your cart has exceeded the available stock availability for one or more menu item(s)',
              data: {}
            });
          }
        }
      }
            
      // 3. Remove Cart Limit....

      cartItems
        .update(
          {
            productID: req.body.productID,
            quantity: req.body.quantity,
            subCategoryID: req.body.subCategoryID,
            specialRequest: req.body.specialRequest,
            updatedAt: new Date()
          },
          {
            returning: true,
            where: {
              id: cartItemID
            }
          }).then(async cartResponse => {
            try {
              await cartItemExtras.destroy({ where: { cartItemID: cartItemID } });
              await cartItemVariants.destroy({ where: { cartItemID: cartItemID } });
              await cartProductVariantTypes.destroy({ where: { cartItemID: cartItemID } });
              await cartProductVariantSubTypes.destroy({ where: { cartItemID: cartItemID } });

              var arrProductItemExtrasItems = [];
              var productItemExtrasItems = JSON.parse(req.body.productItemExtras);
              for (var j = 0; j < productItemExtrasItems.length; j++) {
                arrProductItemExtrasItems.push({
                  cartItemID: cartItemID,
                  productExtrasID: productItemExtrasItems[j]['productExtrasID'],
                  createdAt: new Date()
                });
              }
              if (arrProductItemExtrasItems.length > 0) {
                cartItemExtras.bulkCreate(arrProductItemExtrasItems);
              }

              var arrProductItemVariantsItems = [];
              var productItemVariantsItems = JSON.parse(req.body.productItemVariants);
              for (var k = 0; k < productItemVariantsItems.length; k++) {
                arrProductItemVariantsItems.push({
                  cartItemID: cartItemID,
                  productVariantsID: productItemVariantsItems[k]['productVariantsID'],
                  createdAt: new Date()
                });
              }

              if (arrProductItemVariantsItems.length > 0) {
                cartItemVariants.bulkCreate(arrProductItemVariantsItems);
              }

              var productVariantsTypes = JSON.parse(req.body.productVariantTypes);
              for (const item of productVariantsTypes) {
                cartProductVariantTypes.create({
                  cartItemID: cartItemID,
                  productVariantTypeID: item.id,
                  createdAt: new Date()
                }).then(async (productVariantTypesData) => {
                  let productVariantSubTypesArr = [];
                  for (const subType of item.productVariantSubTypes) {
                    productVariantSubTypesArr.push({
                      cartProductVariantTypeID: productVariantTypesData.id,
                      productVariantTypeID: item.id,
                      productVariantSubTypeID: subType,
                      cartItemID: cartItemID,
                    });
                  }
                  cartProductVariantSubTypes.bulkCreate(productVariantSubTypesArr);
                });
              }

              res.status(200).send({
                success: 1,
                message: 'Item updated successfully!'
              });
            } catch (error) {
              console.log("error", error);
              res.status(200).send({
                success: 0,
                message: 'Item not updated. Please try again!'
              });
            }
          }).error(function (err) {
            console.log("err", err);
            res.status(200).json({
              success: 0,
              message: err.message,
            });
          });
    } else {
      res.status(200).send({
        success: 0,
        message: 'Item not updated. Please try again!'
      });
    }
  } catch (error) {
    console.log("error", error);
    res.status(200).send({
      success: 0,
      message: 'error!'
    });
  }
};

exports.getCartItems = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID;
  try {
    var whereClauseOrder = [];
    whereClauseOrder.push({
      userID: userID
    });

    cartItems
      .findAll({
        where: whereClauseOrder,
        attributes: [
          'id',
          'productID',
          'barID',
          [Sequelize.literal(`(select restaurantName from bar where bar.id = cart_items.barID ) `), 'restaurantName'],
          'userID',
          'quantity',
          'cartServiceType',
          'specialRequest',
          'createdAt',
          'subCategoryID'
        ],
        include: [
          {
            attributes: [
              'id',
              'name',
              'price',
              'description',
              'posID',
              'avatar',
              'stock',
              'isStockLimit',
            ],
            model: product
          },
          {
            attributes: [
              'id',
              'cartItemID',
              'productExtrasID'
            ],
            model: cartItemExtras,
            include: [
              {
                attributes: [
                  'id',
                  'extraItem',
                  'price',
                  'posID'
                ],
                model: productExtras,
              }
            ]
          },
          {
            attributes: [
              'id',
              'cartItemID',
              'productVariantsID'
            ],
            model: cartItemVariants,
            include: [
              {
                attributes: [
                  'id',
                  'variantType',
                  'price'
                ],
                model: productVariants,
              }
            ]
          },
          {
            model: cartProductVariantTypes,
            attributes: [
              ['id', 'cartProductVariantTypeID'],
            ],
            include: [
              {
                model: cartProductVariantSubTypes,
                attributes: [
                  ['id', 'cartProductVariantSubTypeID'],
                ],
                include: [
                  {
                    attributes: [
                      ['id', 'productVariantTypeID'],
                      'label',
                      'posID',
                    ],
                    model: productVariantTypes,
                    required: true,
                  },
                  {
                    attributes: [
                      ['id', "productVariantSubTypeID"],
                      ['variantType', "extraItem"],
                      'price',
                      'posID',
                    ],
                    model: productVariantSubTypes,
                  }
                ],
              }
            ],
          }
        ],
        order: [["cart_product_variant_types", "cart_product_variant_sub_types", "productVariantTypeID", "asc"]]
      }).then(async cartItemData => {
        if (cartItemData && cartItemData.length > 0) {
          const cartData = {};

          let cardData = {};
          const userData = await user
            .findOne({
              attributes: [
                'id',
                'email',
                'fullName',
                'stripeID'
              ],
              where: {
                id: userID
              }
            });

          if (userData) {
            var userStripeID = userData.stripeID;
            if (userStripeID != '') {
              stripeRes = await stripe.customers.listSources(userStripeID, { object: 'card', limit: 1 });
              if (stripeRes.data && stripeRes.data.length > 0) {
                cardData = stripeRes.data[0];
              }
            }
          }

          const feesData = await settings.findAll({ where: { barId: cartItemData[0] ? cartItemData[0].dataValues.barID : 0.99 } });
          // 1. & 2. New Taxes Page under Settings.... Starts
          var taxName
          var taxAmount
          var taxTotalPer
          const taxData = await taxModel.findAll({
            attributes: ['id','name', 'percentage', 'status'],
            where: [{ barID: req.body.barID, status: 'Active' }],
            include: [
              {
                model: operatingBarTax,
                required: false,
              }
            ],
          })
          let today = moment().tz('Australia/Perth').isoWeekday() - 1;

          let taxDataArr = [];
          taxData.forEach(tax => {
            if(tax.operating_bar_taxes.length == 0) {
              taxName = tax.dataValues.name
              taxAmount = tax.dataValues.percentage
              taxTotalPer += taxAmount
              delete tax.dataValues.operating_bar_taxes;
              taxDataArr.push(tax);
            }else if(tax.operating_bar_taxes.length) {
              tax.operating_bar_taxes.forEach(operatingTax => {
                if(operatingTax.weekDay === today && !operatingTax.isClosed) {
                  taxName = tax.dataValues.name
                  taxAmount = tax.dataValues.percentage
                  taxTotalPer += taxAmount
                  delete tax.dataValues.operating_bar_taxes;
                  taxDataArr.push(tax);
                }
              });
            }
          });
          const barDataArr = await bar.findOne({
            attributes: [
              'id',
              'restaurantName',
              'serviceType',
              [Sequelize.literal(`CASE WHEN (select count(*) from user_consent_venue where user_consent_venue.barID = bar.id and user_consent_venue.userID = ${userID}) >= 1 THEN 1 ELSE null END`), 'userConsent']
            ],
            where: {
              id: cartItemData[0].dataValues.barID
            },
          })
          // 1. & 2. New Taxes Page under Settings.... Ends
          cartData.cartItemData = cartItemData;
          cartData.cardData = cardData;
          cartData.feesData = feesData;
          cartData.taxData = taxDataArr; // 1. & 2. New Taxes Page under Settings....
          cartData.barData = barDataArr; 
          res.status(200).send({
            success: 1,
            data: cartData,
            message: 'Cart item retrive successfully!'
          });
        } else {
          res.status(200).send({
            success: 0,
            message: 'Cart item not found!'
          });
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        });
      });
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    });
  }
};

// For Restricting an item to be added in cart when active time of products gets closed...
exports.cartItemsActiveOrInactive = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID;

  try {

    let itemActiveSubids = await itemActiveHours.findAll({
      attributes: [
        'subCategoryID',
      ],
      where: {
        subCategoryID: req.body.subCategoryID,
        status: '1',
        barID: req.body.barID,
        weekDay: Sequelize.fn('WEEKDAY', Sequelize.cast(moment().tz('Australia/Perth').format('YYYY-MM-DD'), 'date'))
      },
      raw: true
    }).then(async results => {
      if (results.length == 0) {
        res.status(200).send({
          success: -3,
          data: [],
          message: 'This Item is not available at the moment!'
        });
      } else {
        next();
      }
    })
      .error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        });
      });
  } catch (e) {
    console.log(e);
  }
};

exports.cartItemsActiveOrInactiveV2 = async (req, res, next) => {
  try {
    const currentDateTimeUTC = req.body.currentDateTimeUTC || moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const [currentDay, currentTime] = [moment(currentDateTimeUTC).isoWeekday() - 1, moment(currentDateTimeUTC).format('HH:mm:ss')];

    const itemActive = await barSubCategoryOpeningHoursUTC.findOne({
      where: { isClosed: '0', barID: req.body.barID, subCategoryID: req.body.subCategoryID, weekDay: currentDay, openingHours: { [Op.lte]: currentTime }, closingHours: { [Op.gte]: currentTime } },
      raw: true
    });

    return itemActive ? next() : res.status(200).json({ success: -3, data: [], message: 'This Item is not available at the moment!' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: 0, message: 'Something went wrong, please try again' });
  }
};