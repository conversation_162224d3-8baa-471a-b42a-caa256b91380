var Sequelize = require('sequelize')
const Op = Sequelize.Op
const moment = require('moment')
const message = require('../config/message')
const common = require('./common')
const bar = require('../models/bar')
const orders = require('../models/orders')
const orderItems = require('../models/orderItems')
const orderItemExtras = require('../models/orderItemExtras')
const orderItemVariants = require('../models/orderItemVariants')
const orderProductVariantTypes = require('../models/orderProductVariantTypes')
const orderProductVariantSubTypes = require('../models/orderProductVariantSubTypes')
const cartItems = require('../models/cartItems')
const cartItemExtras = require('../models/cartItemExtras');
const cartItemVariants = require('../models/cartItemVariants');
const cartProductVariantTypes = require('../models/cartProductVariantTypes');
const cartProductVariantSubTypes = require('../models/cartProductVariantSubTypes');
const product = require('../models/product')
const productVariants = require('../models/productVariants')
const productExtras = require('../models/productExtras')
const barCategorySequence = require('../models/barCategorySequence')
const barProductSequence = require('../models/barProductSequence') // 8. Ability to rearrange menu products under subheadings
const pickupLocation = require('../models/pickupLocation')
const subCategory = require('../models/subCategory')
const productVariantTypes = require('../models/productVariantTypes')
const productVariantSubTypes = require('../models/productVariantSubTypes')
const segmentTagsModel = require('../models/segmentTags')
const segmentProductTagsModel = require('../models/segmentProductTags')
const productFoodOptions = require('../models/productFoodOptions')
const foodOptions = require('../models/foodOptions')
const userAccessToken = require('../models/userAccessToken')
const operatingHours = require('../models/operatingHours')
const SubCategoryWaitTime=require("../models/subCategoryWaitTime")

const commonFunction = require('../common/commonFunction')
const { s3PublicUploadFile, s3UploadDoshiiFile } = require('../middleware/awsS3Operations')
const multerMiddleware = require('../middleware/multer');

var env = require('../config/environment')
var jwt = require('jsonwebtoken')

const productModel = require("../models/product");
const subcategory = require("../models/subCategory");
const {POS} = require("./POS");
const category = require('../models/category')
const sequelize = require('sequelize')
const {capitalize} = require('lodash')
const barOpeningHoursUTC = require('../models/barOpeningHoursUTC')

exports.getSingle = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  var productID = req.body.id
  try {
    var productFetchPromises = []
    productFetchPromises.push(fetchProduct(productID, barID))
    productFetchPromises.push(fetchProductVariants(productID))
    productFetchPromises.push(fetchProductExtras(productID))
    Promise.all(productFetchPromises)
      .then(function (productData) {
        const response = productData[0]['value']
        response.dataValues.productVariants = productData[1]['value']
        response.dataValues.productExtras = productData[2]['value']
        res.status(200).send({
          success: 1,
          data: response,
          message: 'success!'
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getProductList = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    const barDetails = await bar
      .findOne({
        attributes: [
          'id',
          'serviceType',
          'posStatus'
        ],
        where: { id: barID },
    })

    var whereClause = []
    whereClause.push({
      isDeleted: 'No'
    })

    if (barDetails.dataValues.posStatus && barDetails.dataValues.posStatus == '1') {
      whereClause.push({
        categoryID: '-1'
      })
    }else{
      // if (req.body.categoryID) {
        whereClause.push({
          [Op.not]: [{ categoryID: '-1' }]
        })
      // }
    }

    var status = 'All'
    if (req.body.status) {
      status = req.body.status
    }

    // const selectedServiceType = req.body.selectedServiceType
    const currentDateTimeUTC = moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const [currentDay, currentTime] = [moment(currentDateTimeUTC).isoWeekday() - 1, moment(currentDateTimeUTC).format('HH:mm:ss')];

    subCategory
      .findAll({
        attributes: [
          'id',
          'categoryID',
          'name',
          [
            sequelize.literal(`
              (
                SELECT COALESCE(
                  (SELECT IF(
                    BSH.isClosed = 0 
                    AND BSH.weekDay = ${currentDay} 
                    AND CAST('${currentTime}' AS TIME) BETWEEN CAST(BSH.openingHours AS TIME) 
                    AND CAST(BSH.closingHours AS TIME), 1, 0
                  ) 
                  FROM bar_sub_category_opening_hours_utc AS BSH 
                  WHERE sub_category.id = BSH.subCategoryID 
                  AND BSH.barID = ${barID} 
                  AND BSH.weekDay = ${currentDay} 
                  AND CAST('${currentTime}' AS TIME) BETWEEN CAST(BSH.openingHours AS TIME) AND CAST(BSH.closingHours AS TIME)
                  ORDER BY BSH.id ASC 
                  ), 0
                )
              )
            `),
            'operatingFlag',
          ],
        ],
        where: whereClause,
        include: [
          {
            model: barCategorySequence,
            as: "bar_category_sequence",
            required: false,
            attributes: [
              [
                Sequelize.fn(
                  "coalesce",
                  Sequelize.col("subCategorySequence"),
                  1000000000000
                ),
                "subCategorySequence",
              ],
            ],
            where: { barId: barID },
          },
        ],
        order: [
          [Sequelize.literal("`bar_category_sequence.subCategorySequence`")], "id"
        ],
      })
      .then(categories => {
        if (categories.length > 0) {
          var productFetchPromises = []
          // productFetchPromises.push(fetchPopularProductList(barID, status, req.body.categoryID, ''))
          var filterLoop = []
          categories.map(cat1 =>  {
            if (cat1.dataValues.operatingFlag == 1) {
               filterLoop.push(cat1.id)
            }
          })
          if (filterLoop.length == 0) {
            console.log("No Popular")
          }
          else {
            productFetchPromises.push(fetchPopularProductList(barID, "", filterLoop))
          }

          categories.forEach(cat => {
            productFetchPromises.push(fetchProductList(cat, barID, status))
          })

          Promise.all(productFetchPromises)
            .then(function (productData) {
              var arryProcessPromises = []
              productData.map(product => {
                arryProcessPromises.push(product.value)
              })
              res.status(200).send({
                success: 1,
                data: arryProcessPromises,
                message: 'success!'
              })
            })
            .catch(function (err) {
              /* error handling */
              res.status(200).json({
                success: 0,
                message: err.message,
              })
            })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: []
          })
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    console.log(error);
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

// 8. Ability to rearrange menu products under subheadings start
exports.productSequence = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID
    const productsIds = req.body.product_ids ? req.body.product_ids.split(',') : ''
    const categoryId = req.body.category_id
    const subCategoryId = req.body.sub_category_id
    product
      .count({
        where: {isDeleted: 'No', id: productsIds}
      })
      .then(async response => {
        if (response > 0) {
          await barProductSequence.destroy({where: {barId: barID, subCategoryId: subCategoryId}})
            .then(async () =>
              await barProductSequence.bulkCreate(
                productsIds
                  .map((product, index) => {
                    return {
                      barId: barID,
                      productSequence: index + 1,
                      productId: product,
                      categoryId: categoryId,
                      subCategoryId: subCategoryId,
                    }
                  })
                )
              )
          res.status(200).send({
            success: 1,
            message: 'Success!',
            data: response
          })
        } else {
          res.status(200).send({
            success: 0,
            message: 'Incorrect products provided!',
            data: response
          })
        }
      }).error(function (err) {
      res.status(200).json({
        success: 0,
        message: err.message,
      })
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
} // 8. Ability to rearrange menu products under subheadings end

exports.add = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    await multerMiddleware.singleProductPic(req, res, async function (err) {
      try {
        if (req.file != undefined) {
          await s3PublicUploadFile(
            req.file,
            env.awsProductFolder +
            req.file.originalname
          );
        }
        if (req.body.isError) {
          res.status(200).send({
            success: 0,
            message: req.body.message
          })
        } else {
          // const {POS} = require("./POS");
          // const posConfigured = new POS(barID)
          // const venueOptedPosId = await posConfigured.fetchOptedPosConfig()

          var avatar = '';
          if (req.file != undefined) {
            avatar = req.file.originalname
          }
          // 10. Profile photos mandatory & text....
          // if(avatar === '' || avatar === null) {
          //     res.status(200).send({
          //       success: 0,
          //       message: "Please select your product’s profile photo, if you currently do not have one please select your Venue’s profile photo to save this product."
          //     })

          //   } // 10. Profile photos mandatory & text....
          // else {
          let tags = null;
          if (req.body?.tags) { tags = JSON.parse(req.body?.tags); }
          if (tags?.length) {
            const segmentTags = await segmentTagsModel.count({
              where: {
                id: tags
              }
            });

            if (segmentTags !== tags.length) {
              return res.status(200).send({
                success: 0,
                message: "Tag ID not found."
              })
            }
          }

          if (!req.body.product_tax) {
            return res.status(200).send({
              success: 0,
              message: "Product tax required."
            })
          }

          const productData = {
            barID: barID,
            categoryID: req.body.categoryID,
            subCategoryID: req.body.subCategoryID,
            name: req.body.name,//(req.body.name).replace(/(^\w|\s\w)/g, m => m.toUpperCase()),// 10. Profile photos mandatory & text....
            description: req.body.description,
            price: req.body.price,
            pickupLocationID: req.body.pickupLocationID,
            avatar: avatar,
            productServiceType: req.body.tableServiceFlag === '1' ? 'TABLE' : 'PICKUP',
            serviceType: req.body.serviceType,
            stock: req.body.stock,
            dailyStockRenewal: req.body.dailyStockRenewal,
            isDailyStockRenewal: req.body.isDailyStockRenewal,
            isStockLimit: req.body.isStockLimit,
            productTax: req.body.product_tax,
            // fromPosId: venueOptedPosId.attachedPosConfig,
            createdAt: new Date(),
            updatedAt: new Date(),
          }
          product
            .create(productData)
            .then(async productResponse => {
              // add this product to POS too only if there is any POS attached to this venue and has category
              // if (venueOptedPosId.attachedPosConfig) {
              //   productResponse.sub_category = await subCategory.findOne({
              //     attributes: ['posID'],
              //     where: {
              //       id: JSON.parse(productData.subCategoryID),
              //       posID: {[Op.ne]: null},
              //       fromPosId: venueOptedPosId
              //     }
              //   })
              //   posConfigured.createPosProduct(productResponse)
              //     .then(productPosUpdated => {
              //         if (productPosUpdated)
              //           productResponse.update({posID: productPosUpdated})
              //       }
              //     )
              // }
              // else
              //   console.log(`These categories ${productData.categoryID} does not exist in opted POS`)

              var productID = productResponse.id

              var prmVariantsType = JSON.parse(req.body.variantsType)
              var arrVariantsType = []
              if (tags?.length) {
                const tagsData = tags.map((tagID) => ({
                  productID,
                  tagID
                }));
                await segmentProductTagsModel.bulkCreate(tagsData);
              }
              for (var i = 0; i < prmVariantsType.length; i++) {
                arrVariantsType.push({
                  productID: productID,
                  variantType: prmVariantsType[i].variantType,
                  price: prmVariantsType[i].price
                })
              }

              var prmExtrasItem = JSON.parse(req.body.extrasItem)
              var arrExtrasItem = []
              for (var i = 0; i < prmExtrasItem.length; i++) {
                arrExtrasItem.push({
                  productID: productID,
                  extraItem: prmExtrasItem[i].extraItem,
                  price: prmExtrasItem[i].price,
                  extraSequence: i + 1,
                  createdAt: Date.now(),
                  updatedAt: Date.now()
                })
              }
              // product types & its sub types or size ([small, large],[thin,thick])
              var productTypes = JSON.parse(req.body.productVariantTypes);
              if (productTypes.length) {
                for (const type of productTypes) {
                  // store label of product types
                  await productVariantTypes.create({ label: type.label, productID: productID, serviceType: type.serviceType ? type.serviceType : null, isUpdateByUser: 'Yes' }).then(async (productVariantTypesData) => {
                    // store sizes/sub-types of product
                    let productSubTypeArr = [];
                    let extraSequence = 0;
                    for (const size of type.productVariantSubTypes) {
                      productSubTypeArr.push({
                        variantType: size.extraItem,
                        price: size.price,
                        productVariantTypeID: productVariantTypesData.id,
                        extraSequence: extraSequence++
                      });
                    }
                    createProductVariantTypes(productSubTypeArr);
                  });
                }
              }

              const foodOptions = JSON.parse(req.body.foodOptions)
              let foodOptionsArr = []
              for (const option of foodOptions) {
                foodOptionsArr.push({
                  productID: productID,
                  foodOptionID: option,
                  createdAt: Date.now(),
                  updatedAt: Date.now()
                })
              }

              var productPromises = []
              productPromises.push(
                createProductVariants(arrVariantsType)
              )
              productPromises.push(
                createProductExtras(arrExtrasItem)
              )
              productPromises.push(
                createProductFoodOptions(foodOptionsArr)
              )

              Promise.all(productPromises)
                .then(function (data) {
                  var productFetchPromises = []
                  productFetchPromises.push(fetchProduct(productID))
                  productFetchPromises.push(fetchProductVariants(productID))
                  productFetchPromises.push(fetchProductExtras(productID))
                  Promise.all(productFetchPromises)
                    .then(function (productData) {
                      const response = productData[0]['value']
                      response.dataValues.productVariants = productData[1]['value']
                      response.dataValues.productExtras = productData[2]['value']
                      res.status(200).send({
                        success: 1,
                        data: response,
                        message: 'Item added successfully'
                      })
                    })
                    .catch(function (err) {
                      /* error handling */
                      console.log(err)
                    })
                })
                .catch(function (err) {
                  /* error handling */
                  console.log(err)
                })
            }).error(function (err) {
              res.status(200).json({
                success: 0,
                message: err.message,
              })
            })
          // }// 10. Profile photos mandatory & text....
        }
      } catch (error) {
        res.status(200).send({
          success: 0,
          message: 'error!'
        })
      }
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.edit = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    await multerMiddleware.singleProductPic(req, res, async function (err) {
      try {
        if (req.file != undefined) {
          await s3PublicUploadFile(
            req.file,
            env.awsProductFolder +
            req.file.originalname
          );
        }
        if (req.body.isError) {
          res.status(200).send({
            success: 0,
            message: req.body.message
          })
        } else {
          // const {POS} = require("./POS");
          // const posConfigured = new POS(barID)
          // const venueOptedPosId = await posConfigured.fetchOptedPosConfig()
          var productID = req.body.id;
          let tags = null;
          if (req.body?.tags) { tags = JSON.parse(req.body?.tags); }
          
          const productOldData = await product.findOne({
            where: {
              id: productID
            }
          })
  
          if(req.body.serviceType && req.body.serviceType != productOldData.dataValues.serviceType) {
            const currentDateTimeUTC = moment().utc().format('YYYY-MM-DD HH:mm:ss');
            const [currentDay, currentTime] = [moment(currentDateTimeUTC).isoWeekday() - 1, moment(currentDateTimeUTC).format('HH:mm:ss')];
            
            const openingHours = await barOpeningHoursUTC.count({
              attributes: [],
              where: { 
                isClosed: 0,
                weekDay: currentDay,
                openingHours: { [Op.lte]: currentTime },
                closingHours: { [Op.gte]: currentTime },
                barID: barID,
              }
            });

            let isVenueUpdatable = openingHours > 0 ? 1 : 0;
            if (isVenueUpdatable) {
              return res.status(200).json({
                success: 0,
                message: "Your venue is still open, updating your menu service type is only available outside your venue's opening hours.",
                data: {venueOpenFlag: 1},
              })
            }
          }
          
          if (tags) {
            if(tags.length)
            {
              const segmentTags = await segmentTagsModel.count({
                where: {
                  id: tags
                }
              });
              if (segmentTags !== tags.length) {
                return res.status(200).send({
                  success: 0,
                  message: "Tag ID not found."
                })
              }
            }
            await segmentProductTagsModel.destroy({
              where: {
                productID,
              },
              force: true
            });
            if(tags.length)
            {
              const tagsData = tags.map((tagID) => ({
                productID,
                tagID
              }));
              await segmentProductTagsModel.bulkCreate(tagsData);
            }
          }
  
  
          var updateData = {}
          if (req.file != undefined) {
            if (productOldData.avatar != '') {
              common.deletePublicFile(productOldData.dataValues.avatar, '/mytab/product/original')
            }
            if (req.file != undefined)
              updateData['avatar'] = req.file.originalname
          }
  
          updateData['barID'] = barID;
          updateData['categoryID'] = req.body.categoryID;
          updateData['subCategoryID'] = req.body.subCategoryID;
          updateData['name'] = req.body.name
          updateData['description'] = req.body.description;
          updateData['price'] = req.body.price;
          updateData['pickupLocationID'] = req.body.pickupLocationID;
          updateData['serviceType'] = req.body.serviceType;
          updateData['stock'] = req.body.stock;
          updateData['dailyStockRenewal'] = req.body.dailyStockRenewal;
          updateData['isDailyStockRenewal'] = req.body.isDailyStockRenewal;
          updateData['isStockLimit'] = req.body.isStockLimit;
          updateData['productTax']=req.body.product_tax;
  
          if(req.body.serviceType) {
            updateData['isUpdateByUser'] = 'Yes';
          }
          
            product
            .update(updateData,
              {
                returning: true,
                where: {
                  id: productID
                }
              }
            )
            .then(async ([productResponse, isUpdated]) => {
              // to prevent the false update flag
              await product.update({updatedAt: new Date()}, {where: {id: productID}})
              // update this product to POS too only if there is any POS attached to this venue and has category
              // if (venueOptedPosId && isUpdated === 1) {
              //   updateData.sub_category = await subCategory.findOne({
              //     attributes: ['posID'],
              //     where: {
              //       id: updateData.subCategoryID,
              //       posID: {[Op.ne]: null},
              //       fromPosId: venueOptedPosId && venueOptedPosId.attachedPosConfig
              //     }
              //   })
              //   await posConfigured.updatePosProduct({...updateData, posID: req.body.posID})
              // }
              var prmVariantsType = JSON.parse(req.body.variantsType)
              var arrVariantsType = []
              for (var i = 0; i < prmVariantsType.length; i++) {
                if (prmVariantsType[i].id) {
                  productVariants
                    .update(
                      {
                        variantType: prmVariantsType[i].variantType,
                        price: prmVariantsType[i].price,
                        updatedAt: new Date()
                      },
                      {
                        returning: true,
                        where: {
                          id: prmVariantsType[i].id,
                          productID: productID
                        }
                      }
                    )
                } else {
                  arrVariantsType.push({
                    productID: productID,
                    variantType: prmVariantsType[i].variantType,
                    price: prmVariantsType[i].price
                  })
                }
              }
  
              var prmExtrasItem = JSON.parse(req.body.extrasItem)
              var arrExtrasItem = []
              let extraSequence = 0
              for (const {extraItem, id, price, posID, productOptionposID, productOptionName} of prmExtrasItem) {
                if (id) {
                  const extraUpdates = {
                    extraItem: extraItem,
                    price: price,
                    extraSequence: extraSequence++,
                    posID: posID,
                    productOptionposID: productOptionposID,
                    productOptionName: productOptionName,
                  }
                  productExtras.update(extraUpdates, {
                    returning: true, where: {id: id, productID: productID}
                  }).then(([itemExtra, isUpdated]) => {
                    if (isUpdated === 1)
                      // posConfigured.updatePosProductExtras({...extraUpdates, posID: posID})
                    productExtras.update({updatedAt: new Date()}, {where: {id: id, productID: productID}})
                  })
                } else {
                  const additionItems = {
                    productID: productID,
                    extraItem: extraItem,
                    price: price,
                    extraSequence: extraSequence++,
                    posID: posID,
                    productOptionposID: productOptionposID,
                    productOptionName: productOptionName,
                  }
                  arrExtrasItem.push(additionItems)
                  // posConfigured.addPosProductExtras(additionItems)
                }
              }
  
              var productTypes = JSON.parse(req.body.productVariantTypes);
              if (productTypes.length) {
                for (const type of productTypes) {
                  if (type.id) {
                    const productVariantTypeData = await productVariantTypes.findOne({ where: { id: type.id } });
                    if(type.serviceType != productVariantTypeData.dataValues.serviceType) {
                      const currentDateTimeUTC = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                      const [currentDay, currentTime] = [moment(currentDateTimeUTC).isoWeekday() - 1, moment(currentDateTimeUTC).format('HH:mm:ss')];
                      
                      const openingHours = await barOpeningHoursUTC.count({
                        attributes: [],
                        where: { 
                          isClosed: 0,
                          weekDay: currentDay,
                          openingHours: { [Op.lte]: currentTime },
                          closingHours: { [Op.gte]: currentTime },
                          barID: barID,
                        }
                      });

                      let isVenueUpdatable = openingHours > 0 ? 1 : 0;
                      if (isVenueUpdatable) {
                        return res.status(200).json({
                          success: 0,
                          message: "Your venue is still open, updating your menu service type is only available outside your venue's opening hours.",
                          data: {venueOpenFlag: 1},
                        })
                      }
                    }
                    
                    productVariantTypes
                      .update(
                        {
                          label: type.label,
                          serviceType: type.serviceType,
                          isUpdateByUser: "Yes",
                          posID: type.posID,
                          updatedAt: new Date()
                        },
                        {
                          returning: true,
                          where: {
                            id: type.id,
                            productID: productID
                          }
                        }
                      );
                    let productSubTypeArr = [];
                    let extraSequence = 0;
                    for (const size of type.productVariantSubTypes) {
                      if (size.id) {
                        productVariantSubTypes
                          .update(
                            {
                              variantType: size.extraItem,
                              price: size.price,
                              extraSequence: extraSequence++,
                              posID: size.posID,
                              updatedAt: new Date()
                            },
                            {
                              returning: true,
                              where: {
                                id: size.id,
                                productVariantTypeID: type.id
                              }
                            }
                          );
                      } else {
                        productSubTypeArr.push({
                          variantType: size.extraItem,
                          price: size.price,
                          productVariantTypeID: type.id,
                          extraSequence: extraSequence++,
                          posID: size.posID,
                        });
                      }
                    }
                    createProductVariantTypes(productSubTypeArr);
                  } else {
                    await productVariantTypes.create({ label: type.label, productID: productID, serviceType: type.serviceType ? type.serviceType : null, isUpdateByUser: "Yes", posID: type.posID, }).then(async (productVariantTypesData) => {
                      let productSubTypeArr = [];
                      let extraSequence = 0;
                      for (const size of type.productVariantSubTypes) {
                        productSubTypeArr.push({
                          variantType: size.extraItem,
                          price: size.price,
                          productVariantTypeID: productVariantTypesData.id,
                          posID: size.posID,
                          extraSequence: extraSequence++,
                        });
                      }
                      createProductVariantTypes(productSubTypeArr);
                    });
                  }
                }
              }
  
              const foodOptions = JSON.parse(req.body.foodOptions)
              for (const option of foodOptions) {
                productFoodOptions.findOrCreate({
                  where: {
                    productID: productID,
                    foodOptionID: option,
                  },
                  defaults: {
                    productID: productID,
                    foodOptionID: option,
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                  }
                });
              }
  
              var productPromises = []
              productPromises.push(
                createProductVariants(arrVariantsType)
              )
              productPromises.push(
                createProductExtras(arrExtrasItem)
              )
  
              var deletedVariantsType = req.body.deletedVariantsType
              if (deletedVariantsType) {
                productPromises.push(
                  deletedProductVariants(deletedVariantsType, productID)
                )
              }
  
              var deletedExtrasItem = req.body.deletedExtrasItem
              if (deletedExtrasItem) {
                productPromises.push(
                  deletedProductExtras(deletedExtrasItem, productID)
                )
              }
  
              var deletedProductVariantTypesArray = req.body.deletedProductVariantTypes;
              if (deletedProductVariantTypesArray) {
                productPromises.push(
                  deletedProductVariantTypes(deletedProductVariantTypesArray, productID)
                );
              }
  
              var deletedProductVariantSubTypesArray = req.body.deletedProductVariantSubTypes;
              if (deletedProductVariantSubTypesArray) {
                productPromises.push(
                  deletedProductVariantSubTypes(deletedProductVariantSubTypesArray)
                );
              }
  
              var deletedFoodOptionIds = req.body.deletedFoodOptions
              if (deletedFoodOptionIds) {
                productPromises.push(
                  deletedFoodOptions(deletedFoodOptionIds, productID)
                )
              }
  
              Promise.all(productPromises)
                .then(function (data) {
                  var productFetchPromises = []
                  productFetchPromises.push(fetchProduct(productID))
                  productFetchPromises.push(fetchProductVariants(productID))
                  productFetchPromises.push(fetchProductExtras(productID))
                  Promise.all(productFetchPromises)
                    .then(function (productData) {
                      const response = productData[0]['value']
                      response.dataValues.productVariants = productData[1]['value']
                      response.dataValues.productExtras = productData[2]['value']
                      res.status(200).send({
                        success: 1,
                        data: response,
                        message: 'Item details updated successfully'
                      })
                    })
                    .catch(function (err) {
                      /* error handling */
                      console.log(err)
                    })
                })
                .catch(function (err) {
                  /* error handling */
                  console.log(err)
                })
            }).error(function (err) {
              console.log(err)
              res.status(200).json({
                success: 0,
                message: err.message,
              })
            })
          
        }
      } catch (error) {
        res.status(200).send({
          success: 0,
          message: 'error!'
        })
      }
    })
  } catch (error) {
    console.log("error", error);
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.delete = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  const subCategory = req.body.sub_category_id
  try {
    product
      .update(
        {
          isDeleted: 'Yes',
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: req.body.id,
            barID: barID
          }
        }
      )
      .then( async function (removalDetails) {
        const existingItem = await product.count({where: {subCategoryID: subCategory, isDeleted: 'No', barID: barID}})
        if (removalDetails[1] === 1 && existingItem === 0) {
          barCategorySequence.destroy({where: {subCategoryId: subCategory, barId: barID}})
        }
        await segmentProductTagsModel.destroy({
          where: {
            productID: req.body.id
          }
        });
        res.json({
          success: 1,
          message: 'success!'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.deleteProductVariant = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var productID = req.body.productID
  try {
    productVariants
      .update(
        {
          isDeleted: 'Yes',
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: req.body.id,
            productID: productID
          }
        }
      )
      .then(function () {
        res.json({
          success: 1,
          message: 'success!'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.deleteProductExtras = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var productID = req.body.productID
  try {
    productExtras
      .update(
        {
          isDeleted: 'Yes',
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: req.body.id,
            productID: productID
          }
        }
      )
      .then(function () {
        res.json({
          success: 1,
          message: 'success!'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateStatus = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    product
      .update(
        {
          status: req.body.status,
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: req.body.id,
            barID: barID
          }
        }
      )
      .then(function () {
        res.json({
          success: 1,
          message: 'success!'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

function fetchProduct(productID, barID = null, serviceType=null) {
  return new Promise(function (resolve, reject) {
    var productWhereClause = {
      isDeleted: 'No',
      id: productID
    }

    if (barID != null) {
      productWhereClause['barID'] = barID
    }

    let productVariantTypeWhereClause = [];
    
    productVariantTypeWhereClause.push({ 
      isDeleted: "No"
    });
    
    if (serviceType && serviceType.toLowerCase() !== 'both') {
      //productWhereClause['serviceType'] = serviceType;
      productVariantTypeWhereClause.push({
        [Op.or]: [
          {serviceType: serviceType},
          {serviceType: "BOTH"}
        ],
      })
    }
    
    let currentDay = moment().tz('Australia/Perth').isoWeekday()
    product
      .findOne({
        attributes: [
          'id',
          'barID',
          'categoryID',
          'subCategoryID',
          'name',
          'description',
          'avatar',
          'serviceType',
          // [
          //   Sequelize.literal(`(CASE WHEN product.posID IS NULL THEN CONCAT('${env.awsServerURL}',"product/original/", avatar ) ELSE avatar END)`),
          //   'avatar'
          // ],
          'price',
          'posID',
          'pickupLocationID',
          'stock',
          'isStockLimit',
          'dailyStockRenewal',
          'isDailyStockRenewal',
          'status',
          "productTax",
          [
            Sequelize.literal(`
              (select waitTime from sub_category_wait_time 
                where 
                  barID = product.barID and
                  subCategoryID = sub_category.id and
                  weekDay = '${currentDay - 1}' and
                  startTime <= '${moment().tz('Australia/Perth').format('HH:mm:ss')}' and
                  endTime >= '${moment().tz('Australia/Perth').format('HH:mm:ss')}'
              )`
            ), 'waitTime'
          ]
        ],
        where: productWhereClause,
        include: [
          {
            model: pickupLocation,
            attributes: [
              'id',
              'description',
              'address',
              'latitude',
              'longitude',
            ]
          },
          {
            model: subCategory,
            attributes: [
              'id',
              'name'
            ]
          },
          {
            model: productVariantTypes,
            as: 'productVariantTypes',
            required: false,
            attributes: ["id", "label", "productID", "serviceType","posID"],
            where: productVariantTypeWhereClause,
            include: {
              model: productVariantSubTypes,
              as: 'productVariantSubTypes',
              required: false,
              attributes: ["id", ["variantType", "extraItem"], "price", "extraSequence","posID"],
              where: { isDeleted: "No" },
            }
          },
          {
            model: productFoodOptions,
            as: "productFoodOptions",
            attributes: ["id", "productID", "foodOptionID",
              [Sequelize.literal(`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "name"],
              [Sequelize.literal(`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "initials"],
            ],
            include: {
              model: foodOptions,
              as: "foodOptions",
              attributes: [],
              // order:[[sequelize.fn('length', sequelize.col('initials')), 'ASC'], ['initials', 'ASC']]
            }
          },
          {
						model: segmentProductTagsModel,
						attributes: ['id'],
						include: {
							model: segmentTagsModel,
							attributes: ['id', 'name']
						}
					}
        ],
        order: [["productVariantTypes", "id", "asc"], ["productVariantTypes", "productVariantSubTypes", "extraSequence", "asc"], ["productFoodOptions",'id', 'ASC']]
      })
      .then(productFetchResponse => {
        resolve({
          key: 'fetchProduct',
          value: productFetchResponse
        })
      })
  })
}

function fetchProductVariants(productID) {
  return new Promise(function (resolve, reject) {
    var whereClause = {
      isDeleted: 'No',
      productID: productID
    }

    productVariants
      .findAll({
        attributes: [
          'id',
          'productID',
          'variantType',
          'price'
        ],
        where: whereClause
      })
      .then(response => {
        resolve({
          key: 'fetchProductVariants',
          value: response
        })
      })
  })
}

function fetchProductExtras(productID) {
  return new Promise(function (resolve, reject) {
    productExtras
      .findAll({
        attributes: [
          'id',
          'productID',
          'extraItem',
          'price',
          'posID',
          'productOptionposID',
          'productOptionName',
        ],
        where: {
          isDeleted: 'No',
          productID: productID
        },
        order: [['extraSequence', 'asc']],
      })
      .then(response => {
        resolve({
          key: 'fetchProductExtras',
          value: response
        })
      })
  })
}

function createProductVariants(arrVariantsType) {
  try {
    return new Promise(function (resolve, reject) {
      productVariants
        .bulkCreate(arrVariantsType)
        .then(variantsTypeResponse => {
          resolve({
            key: 'productVariants',
            value: {
              status: 1
            }
          })
        })
    })
  } catch (error) {
    console.log('createProductVariants:' + error)
  }
}

function createProductExtras(arrExtrasItem) {
  try {
    return new Promise(function (resolve, reject) {
      productExtras
        .bulkCreate(arrExtrasItem)
        .then(extrasItemResponse => {
          resolve({
            key: 'productExtras',
            value: {
              status: 1
            }
          })
        })
    })
  } catch (error) {
    console.log('createProductExtras:' + error)
  }
}
function createProductVariantTypes(arrExtrasItem) {
  try {
    return new Promise(function (resolve, reject) {
      productVariantSubTypes.bulkCreate(arrExtrasItem).then(extrasItemResponse => {
        resolve({
          key: 'productVariantSubTypes',
          value: {
            status: 1
          }
        });
      });
    });
  } catch (error) {
    console.log('createProductVariantTypes:' + error);
  }
}

function deletedProductExtras(deletedExtrasItem, productID) {
  try {
    return new Promise(function (resolve, reject) {
      const deletedExtrasItemIDs = deletedExtrasItem.split(',')

      productExtras.update(
        {
          isDeleted: 'Yes',
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: {
              [Op.in]: deletedExtrasItemIDs
            },
            productID: productID
          }
        }
      ).then(deletedExtrasItemResponse => {
        resolve({
          key: 'deletedExtrasItem',
          value: {
            status: 1
          }
        })
      })
    })
  } catch (error) {
    console.log('deletedExtrasItem:' + error)
  }
}

function deletedProductVariants(deletedVariantsType, productID) {
  try {
    return new Promise(function (resolve, reject) {
      const deletedVariantsTypeIDs = deletedVariantsType.split(',')
      productVariants.update(
        {
          isDeleted: 'Yes',
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: {
              [Op.in]: deletedVariantsTypeIDs
            },
            productID: productID
          }
        }
      ).then(deletedVariantsTypeResponse => {
        resolve({
          key: 'deletedVariantsType',
          value: {
            status: 1
          }
        })
      })
    })
  } catch (error) {
    console.log('deletedVariantsType:' + error)
  }
}

/*User API*/
exports.getOldProducts = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {

    var whereClause = []

    whereClause.push({
      barID: req.body.barID
    })

    whereClause.push({
      isDeleted: 'No'
    })

    whereClause.push({
      status: 'Active'
    })

    if (req.body.subCategoryID) {
      whereClause.push({
        subCategoryID: req.body.subCategoryID
      })
    }

    if (req.body.search) {
      whereClause.push({
        name: {
          [Op.like]: '%' + req.body.search + '%'
        }
      })
    }

    var page = 1
    if (req.body.page) {
      page = (parseInt(req.body.page) - 1) * 10
    }

    product
      .findAll({
        attributes: [
          'id',
          'barID',
          'categoryID',
          'subCategoryID',
          'name',
          'posID',
          'description',
          'price',
          'pickupLocationID',
          'avatar',
          // [
          //   Sequelize.literal(`(CASE WHEN product.posID IS NULL THEN CONCAT('${env.awsServerURL}',"product/original/", avatar ) ELSE avatar END)`),
          //   'avatar'
          // ],
          [
            Sequelize.literal(
              '(select IFNULL(min(product_variants.price), 0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'minPrice'
          ],
          [
            Sequelize.literal(
              '(select IFNULL(max(product_variants.price),0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'maxPrice'
          ],
        ],
        where: whereClause,
        distinct: true,
        offset: page,
        limit: 10
      })
      .then(response => {
        res.status(200).send({
          success: 1,
          message: 'Success!',
          data: response
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getItemProfile = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  var productID = req.body.id
  const serviceType = req.body.serviceType

  try {
    var productFetchPromises = []
    productFetchPromises.push(fetchProduct(productID, null, serviceType))
    productFetchPromises.push(fetchProductVariants(productID))
    productFetchPromises.push(fetchProductExtras(productID))
    Promise.all(productFetchPromises)
      .then(function (productData) {
        const response = productData[0]['value']
        response.dataValues.productVariants = productData[1]['value']
        response.dataValues.productExtras = productData[2]['value']
        res.status(200).send({
          success: 1,
          data: response,
          message: 'success!'
        })
      })
      .catch(function (err) {
        /* error handling */
        console.log(err)
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getProducts = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  var barID = req.body.barID

  try {
    var barID = req.body.barID
    let serviceType = req.body.serviceType ? req.body.serviceType : null;
    //let currentTime = req.body.currentTime ? moment(req.body.currentTime).format('HH:mm:ss') : moment().tz('Australia/Perth').format('HH:mm:ss') // fix opening kitchen hours issue
    //let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone; // fix opening kitchen hours issue
    
    const barData = await bar.findOne({
      attributes: [
        'id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile',
        'stripeID', 'address', 'venueId', 'attachedPosConfig', 'docketCommission', 'posStatus', 'serviceType', 'avatar'
      ],
      include: [
        {
          model: operatingHours,
          attributes: [
            'id',
            'weekDay',
            'openingHours',
            'closingHours',
            'isClosed'
          ],
          required: false,
          where: { isClosed: 0 }
        }
      ],
      where: {
        id: barID
      },
    })
    if(serviceType && barData.dataValues.serviceType != "BOTH" && serviceType != barData.dataValues.serviceType) {
      return res.status(200).json({
        success: 0,
        message: capitalize(serviceType) +' service is currently not active for this Venue',
        data: []
      });
    }

    if(barData.dataValues.posStatus && barData.dataValues.posStatus === '1'){
      var whereClause = {
        categoryID: '-1',
        isDeleted: 'No'
      }
    }else{
      var whereClause = {
        [Op.not]: [{ categoryID: '-1' }],
        isDeleted: 'No'
      }
    }

    let currentDay = moment().tz('Australia/Perth').isoWeekday()
    currentDay = currentDay - 1;
    let currentTime = moment().tz("Australia/Perth").format("HH:mm:ss");
    
    subCategory
      .findAll({
        attributes: [
          'id',
          'categoryID',
          'name',
          //[sequelize.literal(`(SELECT IF(IA.status = '1' and CAST('${currentTime}' AS TIME) between CAST(IA.activeHours AS TIME) and CAST(IA.inActiveHours AS TIME), 1, 0)isActive FROM itemActiveHours as IA WHERE sub_category.id = IA.subCategoryID AND IA.barID = ${barID} AND IA.weekDay = WEEKDAY(CAST(NOW() AS DATE))HAVING isActive = 1)`), 'operatingFlag'], // fix opening kitchen hours issue
          [sequelize.literal(`(SELECT IF(IA.status = '1' and CAST('${moment().tz('Australia/Perth').format('HH:mm:ss')}' AS TIME) between CAST(IA.activeHours AS TIME) and CAST(IA.inActiveHours AS TIME), 1, 0)isActive FROM itemActiveHours as IA WHERE sub_category.id = IA.subCategoryID AND IA.barID = ${barID} AND IA.weekDay = ${currentDay} HAVING isActive = 1)`), 'operatingFlag'],
          [
            sequelize.literal("`sub_category_wait_time`.`waitTime`"),
            "waitTime",
          ],         
        ],
        include: [{
          model: barCategorySequence,
          as: 'bar_category_sequence',
          required: false,
          attributes: [[sequelize.fn('coalesce', sequelize.col('subCategorySequence'), 1000000000000), 'subCategorySequence']],
          where: {barId: barID}
          },
          {
            model: SubCategoryWaitTime,
            as: "sub_category_wait_time",
            required: false,
            attributes: [],
            where: {
              barID: barID,
              weekDay: currentDay,
              startTime: { [Op.lte]: currentTime },
              endTime: { [Op.gte]: currentTime },
            },           
          },
        ],
        order: [
          [sequelize.literal('`operatingFlag`'),'DESC'],
          [Sequelize.literal("`bar_category_sequence.subCategorySequence`")],
          "id"
        ],
        where: {
          ...whereClause,
          [Op.and]: [
            sequelize.literal(`(
            SELECT COUNT(product.id) 
            FROM product 
            WHERE product.subCategoryID = sub_category.id 
              AND product.isDeleted = "No" 
              AND product.status = "Active" 
              AND product.barID = ${barID}
          ) > 0`),
          ],
        },
        
      })
      .then(async (categories) => {
        if (categories.length > 0) {
          var productFetchPromises = []
          var filterLoop = []
          categories.map(cat1 =>  {
            if (cat1.dataValues.operatingFlag == 1) {
              filterLoop.push(cat1.id)
            }
          })

          const orderData = await orders.findOne({
            where: {
              userID: userID,
              barID: barID,
              paymentStatus: 'received',
              refundStatus: 'No',
              orderStatus: 'Pickedup',
            },
            attributes: [
              'id',
              'orderNo',
              'pickupCode',
              [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
              'subTotal',
              'transactionFee',
              'tax',
              'total',
              'orderDate',
              'orderStatus',
              'orderServiceType',
              'paymentType',
              'refundStatus',
              'promocode_id',
              'promocode_amount',
              'promocode_discount',
              'cardType',
              'cardNumber',
              'userID',
              'barID',
              'createdAt',
              'refundedDate'
            ],
            include: [
              {
                required: true,
                attributes: [
                  'id',
                  'orderID',
                  'productID',
                  'price',
                  'quantity',
                  'specialRequest',
                  'isCanceled',
                  'refundAmount',
                  'refundedQuantity',
                  'waitTime',
                  'orderStatus',
                  'PreparingStartTime',
                  'ReadyTime',
                  'PickedupTime',
                  [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
                  [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
                  [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
                ],
                model: orderItems,
                include: [
                  {
                    // where: [ {categoryID: [1,2] } ],
                    // where: [ {categoryID: [1,2] }, ...whereClauseProduct ],
                    attributes: [
                      'id',
                      'name',
                      'categoryID',
                      'subCategoryID',
                      'description',
                      'avatar',
                      'posID',
                      'stock',
                      'isStockLimit',
                    ],
                    required:false,
                    model: product,
                    where: {
                      status: 'Active',
                      isDeleted: 'No',
                      subCategoryID: {
                        [Op.in] : filterLoop
                      }
                    },
                    include: [
                      {
                        attributes: [
                          'id',
                          'description',
                          'address'
                        ],
                        model: pickupLocation,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productExtrasID',
                      'price'
                    ],
                    model: orderItemExtras,
                    include: [
                      {
                        attributes: [
                          'id',
                          'extraItem',
                          'posID',
                        ],
                        model: productExtras,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productVariantsID',
                      'price'
                    ],
                    model: orderItemVariants,
                    include: [
                      {
                        attributes: [
                          'id',
                          'variantType'
                        ],
                        model: productVariants,
                      }
                    ]
                  },
                  {
                    attributes: [
                      ['id', "orderProductVariantTypeID"],
                      'orderItemID',
                    ],
                    where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                    model: orderProductVariantTypes,
                    required: false,
                    include: [
                      {
                        attributes: [
                          ['id', "productVariantTypeID"],
                          'label',
                        ],
                        model: productVariantTypes,
                        required: true,
                        include: [
                          {
                            attributes: [
                              ['id', "orderProductVariantSubTypeID"],
                              'orderItemID',
                            ],
                            // where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                            model: orderProductVariantSubTypes,
                            include: [
                              {
                                attributes: [
                                  ['id', "productVariantSubTypeID"],
                                  ['variantType', "extraItem"],
                                  'price',
                                ],
                                model: productVariantSubTypes,
                              }
                            ]
                          }
                        ]
                      }
                    ],
                  }
                ]
              },
            ],
            order: [['id', 'DESC']],
          })

          var orderAgainProductIDs = []
          if(orderData && orderData.orderServiceType == serviceType){
            let incrementalID = 0;
            for (let j = orderData.order_items.length; j >= 0; j--) {
              if(incrementalID < 3){
                const item = orderData.order_items[j];
                var tempProductVariantTypes = []
                var tempProductItemExtras = [];
                if(item && item.product){
                  if(item.product.isStockLimit == 'Yes' && item.product.stock <= 0){
                    continue; 
                  }
                  for (let k = 0; k < item.order_product_variant_types.length; k++) {
                    const variant = orderData.order_items[j].order_product_variant_types[k];
                    let tempVariantTypes = {}
                    tempVariantTypes.id = variant.product_variant_type.dataValues.productVariantTypeID
                    let getProductSubVariant = await orderProductVariantSubTypes.find({
                      attributes: [
                        ['id', "orderProductVariantSubTypeID"],
                        "orderItemID",
                        [
                          Sequelize.literal(
                          '(select variantType from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                          ),
                          'extraItem'
                        ],
                        [
                          Sequelize.literal(
                            '(select price from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                          ),
                          'price'
                        ],
                        [
                          Sequelize.literal(
                          '(select id from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                          ),
                          'productVariantSubTypeID'
                        ],
                      ],
                      where: {
                        orderItemID: variant.orderItemID,
                        productVariantTypeID: variant.product_variant_type.dataValues.productVariantTypeID,
                      }
                    });
                    orderData.order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.productVariantSubTypeID = getProductSubVariant.dataValues.productVariantSubTypeID;
                    orderData.order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.extraItem = getProductSubVariant.dataValues.extraItem;
                    orderData.order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.price = getProductSubVariant.dataValues.price;
                    tempVariantTypes.productVariantSubTypes = [{ id : getProductSubVariant.dataValues.productVariantSubTypeID }]
                    tempProductVariantTypes.push(tempVariantTypes)
                  }
                  if(item.order_item_extras.length > 0){
                    item.order_item_extras.map(itemExtra => {
                      tempProductItemExtras.push({ 'id' : itemExtra.productExtrasID })
                    })
                  }
                  var productOrderData = {
                    id: item.productID,
                    productItemExtras: tempProductItemExtras,
                    productVariantTypes: tempProductVariantTypes
                  }
                  orderAgainProductIDs.push(productOrderData)
                  incrementalID++;
                }
              }
            }
            if(orderAgainProductIDs.length > 0){
              productFetchPromises.push(fetchOrderAgainProductList(barID, orderAgainProductIDs, serviceType))
            }
          }

          if (filterLoop.length == 0) {
            console.log("No Popular")
          }
          else {
            productFetchPromises.push(fetchPopularProductList(barID, "", filterLoop, serviceType))
          }
          
          categories.forEach(cat => {
            productFetchPromises.push(fetchProductList(cat, barID, "Active", 1, serviceType))
          })

          let resArray = {}
          
          let cartItemsResponse = await cartItems
          .findAll({
            where: {
              userID: userID
            },
            attributes: [
              'id',
              'barID',
              'productID',
              'quantity',
              'subCategoryID',
              'cartServiceType',
              [Sequelize.literal(`(select price from product where id = cart_items.productID)`), 'productPrice'],
            ]
          })

          Promise.all(productFetchPromises)
          .then(async function (productData) {
              var arryProcessPromises = []
              productData.map(product => {
                if(product.value && product.value.categoryProduct && product.value.categoryProduct.length > 0) {
                  arryProcessPromises.push(product.value)
                }
              })
              resArray.productData = arryProcessPromises
              resArray.cartItemsResponse = cartItemsResponse;
              resArray.barData = barData;

              res.status(200).send({
                success: 1,
                data: resArray,
                message: 'success!'
              })
            })
            .catch(function (err) {
              console.log(err);
              /* error handling */
              res.status(200).json({
                success: 0,
                message: err.message,
              })
            })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: []
          })
        }
      }).error(function (err) {
        console.log(err);
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    console.log(error);
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

function fetchProductList(category, barID, status = 'Active', StockAvailable = 0, selectedServiceType = null) {
  return new Promise(function (resolve, reject) {
    var productWhereClause = []
    productWhereClause.push({
      isDeleted: 'No',
      subCategoryID: category.id,
      barID: barID,
      // serviceType: {[Op.eq]: selectedServiceType}
    })

    if (status == 'Active') {
      productWhereClause.push({
        status: 'Active'
      })
    }

    if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
      productWhereClause.push({
        [Op.or]: [
          {serviceType: selectedServiceType},
          {serviceType: "BOTH"}
        ],
      })
    }

    // if (req.body.search) {
    //   whereClause.push({
    //     name: {
    //       [Op.like]: '%' + req.body.search + '%'
    //     }
    //   })
    // }

    product
      .findAll({
        attributes: [
          'id',
          'barID',
          'categoryID',
          'subCategoryID',
          'name',
          'description',
          'price',
          'pickupLocationID',
          'status',
          'avatar',
          'serviceType',
          'stock',
          'isStockLimit',
          'dailyStockRenewal',
          'isDailyStockRenewal',
          [
            Sequelize.literal(`(CASE WHEN product.isStockLimit = 'Yes' THEN (CASE WHEN product.stock > 0 THEN '1' ELSE '0' END) ELSE '1' END)`),
            'productIsValid'
          ],
          [
            Sequelize.literal(
              '(select IFNULL(min(product_variants.price), 0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'minPrice'
          ],
          [
            Sequelize.literal(
              '(select IFNULL(max(product_variants.price),0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'maxPrice'
          ],
        ],
        include: [{
          model: productFoodOptions,
          as: "productFoodOptions",
          attributes: ["id", "productID", "foodOptionID",
            [Sequelize.literal(`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "name"],
            [Sequelize.literal(`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "initials"],
          ],
          include: {
            model: foodOptions,
            as: "foodOptions",
            attributes: [],
          },
        },
        // 8. Ability to rearrange menu products under subheadings start
        {
          model: barProductSequence,
          as: 'bar_product_sequence',
          required: false,
          attributes: [[sequelize.fn('coalesce', sequelize.col('productSequence'),100000000000), 'productSequence']],
        }, // 8. Ability to rearrange menu products under subheadings end
        ],
        where: productWhereClause,
        distinct: true,
        order: [[Sequelize.literal(`bar_product_sequence.productSequence`), "DESC"], ["productFoodOptions", "foodOptions", "listingOrder", "ASC"], "id"], // 8. Ability to rearrange menu products under subheadings add
        // order: [['id', 'DESC']],
      })
      .then(productFetchResponse => {
        var resData = {
          categoryID: category.id,
          mainCategoryID: category.categoryID,
          categoryName: category.name,
          categoryWaitTime: category.dataValues.waitTime,
          categoryProduct: StockAvailable == 1 ? productFetchResponse.filter(product => product.dataValues.productIsValid == '1') : productFetchResponse,
          categoryActive: category.dataValues.operatingFlag ? category.dataValues.operatingFlag : 0
        }
        resolve({
          key: 'categoryWiseProduct',
          value: resData
        })
      })
  })
}

function fetchPopularProductList(barID, categoryID = null, subCategoryIDs = null, serviceType = null) {
  return new Promise(function (resolve, reject) {
    var productWhereClause = []
    if ( subCategoryIDs.length == 0) {
      productWhereClause.push({
        isDeleted: 'No',
        barID: barID
      })
    } else {
      productWhereClause.push({
        isDeleted: 'No',
        barID: barID,
        subCategoryID: subCategoryIDs,
        status: 'Active'
      })
    }

    // if (status == 'Active') {
    //   productWhereClause.push({
    //     status: 'Active'
    //   })
    // }

    if (categoryID) {
      productWhereClause.push({
        categoryID: categoryID
      })
    }
    
    if (serviceType && serviceType.toLowerCase() !== 'both') {
      productWhereClause.push({
        [Op.or]: [
          {serviceType: serviceType},
          {serviceType: "BOTH"}
        ],
      })
    }

    product
      .findAll({
        attributes: [
          'id',
          'barID',
          'categoryID',
          'subCategoryID',
          'name',
          'description',
          'price',
          'pickupLocationID',
          'status',
          'avatar',
          'serviceType',
          'stock',
          'isStockLimit',
          'dailyStockRenewal',
          'isDailyStockRenewal',
          // [
          //   Sequelize.literal(`(CASE WHEN product.posID IS NULL THEN CONCAT('${env.awsServerURL}',"product/original/", avatar ) ELSE avatar END)`),
          //   'avatar'
          // ],
          [
            Sequelize.literal(`(CASE WHEN product.isStockLimit = 'Yes' THEN (CASE WHEN product.stock > 0 THEN '1' ELSE '0' END) ELSE '1' END)`),
            'productIsValid'
          ],
          [
            Sequelize.literal(
              '(select IFNULL(min(product_variants.price), 0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'minPrice'
          ],
          [
            Sequelize.literal(
              '(select IFNULL(max(product_variants.price),0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'maxPrice'
          ],
          [
            Sequelize.literal(
              '(select SUM(quantity) from order_items WHERE order_items.productID = `product`.id)'
            ),
            'soldQuantity'
          ],
        ],
        include: {
          model: productFoodOptions,
          as: "productFoodOptions",
          attributes: ["id", "productID", "foodOptionID",
            [Sequelize.literal(`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "name"],
            [Sequelize.literal(`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "initials"],
          ],
          include: {
            model: foodOptions,
            as: "foodOptions",
            attributes: [],
            
          }
        },
        where: productWhereClause,
        order: [sequelize.literal('soldQuantity DESC'), ['id', 'DESC'],["productFoodOptions", "foodOptions", "listingOrder", "ASC"]],
        distinct: true,
      })
      .then(productFetchResponse => {
        let productResponse = [];
        for (let index = 0; index < 5; index++) {
          let product = productFetchResponse[index]
          if(product && product.dataValues.productIsValid == '1' && product.dataValues.soldQuantity > '0'){
            productResponse.push(product)
          }
        }
        var resData = {
          categoryID: 0,
          maincategoryID: 0,
          categoryName: 'Popular',
          categoryProduct: productResponse,
          categoryActive: 1
        }
        resolve({
          key: 'categoryWiseProduct',
          value: resData
        })
      })
  })
}

function fetchOrderAgainProductList(barID, productsData = [], serviceType = null) {
  return new Promise(function (resolve, reject) {
    const productDataResponse = [];
    productsData.map(async(productData) => {
      const productPromise = new Promise((resolve, reject) => {
        let productResponse = product
        .findOne({
          attributes: [
            'id',
            'barID',
            'categoryID',
            'subCategoryID',
            'name',
            'description',
            'price',
            'pickupLocationID',
            'status',
            'avatar',
            'serviceType',
            'stock',
            'isStockLimit',
            'dailyStockRenewal',
            'isDailyStockRenewal',
            // [
            //   Sequelize.literal(`(CASE WHEN product.posID IS NULL THEN CONCAT('${env.awsServerURL}',"product/original/", avatar ) ELSE avatar END)`),
            //   'avatar'
            // ],
            [
              Sequelize.literal(
                '(select IFNULL(min(product_variants.price), 0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
              ),
              'minPrice'
            ],
            [
              Sequelize.literal(
                '(select IFNULL(max(product_variants.price),0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
              ),
              'maxPrice'
            ],
          ],
          include: {
            model: productFoodOptions,
            as: "productFoodOptions",
            attributes: ["id", "productID", "foodOptionID",
              [Sequelize.literal(`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "name"],
              [Sequelize.literal(`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "initials"],
            ],
            include: {
              model: foodOptions,
              as: "foodOptions",
              attributes: [],
            }
          },
          where: { 
            id : productData.id,
          },
          order: [['id', 'DESC'],["productFoodOptions", "foodOptions", "listingOrder", "ASC"]]
        })
        if(productResponse){
          resolve(productResponse)
        }
      })
      productPromise.then((data) => {
        data.dataValues['productExtras'] = productData.productItemExtras ? productData.productItemExtras : []
        data.dataValues['productVariantTypes'] = productData.productVariantTypes ? productData.productVariantTypes : []
        return data
      })
      productDataResponse.push(productPromise)
    });
    Promise.all(productDataResponse)
    .then(async function (finalProductDataList) {
      var resData = {
        categoryID: 0,
        maincategoryID: -2,
        categoryName: 'Order Again',
        categoryProduct: finalProductDataList,
        categoryActive: 1
      }
      resolve({
        key: 'categoryWiseProduct',
        value: resData
      })
    })
  })
}

exports.productPosHandling = class productPosHandling {
  constructor(barID) {
    this.barID = barID
    this.posConfigured = null;
  }

  upsertKountaProducts = async (products) => {
    let checkedForNonRemoval = [];
    const isSingleRecord = !Array.isArray(products)
    let productsIterable = isSingleRecord ? [products] : products;
    for (let product of productsIterable) {
      let {id: posID, name, description, show_online} = product

      // get product kounta details
      const productDetail = await this.posConfigured.fetchApi(this.posConfigured.URL_CONVERSION.KOUNTA_VIEW_PRODUCT_URL(posID));

      // get product details
      const productDatabaseDetail = await productModel.findOne({where: {posID: posID}})

      let {created_at, updated_at, is_modifier, unit_price, categories, image} = productDetail

      // only accepts first category as primary for product
      const subCategoryId = await subCategory.findOne({where: {posID: categories[0]}, attributes: ['id','categoryID']})

      if (!categories[0] || !subCategoryId || is_modifier) {
        if (!(productDatabaseDetail && productDatabaseDetail.subCategoryID)) continue
        continue
      }

      checkedForNonRemoval.push(posID)
      let productResponse = {
        barID: this.barID,
        name: name,
        description: description ? description : '',
        categoryID: subCategoryId ? subCategoryId.categoryID : -1, // static POS category is 0
        fromPosId: 1,  // static kounta subcategory is 1
        subCategoryID: subCategoryId && subCategoryId.id, // so the products having local category dont get removed
        // status: show_online ? 'Active' : 'Inactive',
        posID: posID,
        avatar: image ? image : '',
        price: unit_price,
        createdAt: created_at,
        updatedAt: updated_at
      };

      try {
        // create or update product
        productModel.findOrCreate({
          where: {
            barID: this.barID,
            isDeleted: 'No',
            [Op.or]: [{
              posID: posID
            }, {
              name: name
            }]
          },
          defaults: productResponse
        }).then(([productData, isCreated]) => {
          if (!isCreated) {
            if (!productResponse.subCategoryID) {
              // Preserving local category and sub category
              delete productResponse.subCategoryID
              delete productResponse.categoryID
            }
            productData.update({...productResponse, updatedAt: new Date()});
          }
        })

      } catch (e) {
        console.log("From category sync: ", e)
      }
    }

    // remove any redundant product
    if (!isSingleRecord)
      product.findAll({
        where: {
          posID: {[Op.notIn]: checkedForNonRemoval},
          barID: this.barID
        },
      }).then(removableExtras => {
        removableExtras.map(removingRecords => {
          // removingRecords.destroy()
          removingRecords.update({isDeleted: 'Yes'})
        })
      })
  }

  upsertKountaProductExtras = async (productExtrasReceived, singleProductUpdate) => {
    let checkedForNonRemovalPosId = [];
    let checkedForNonRemovalProductId = [];
    // When product is updated from we know the updating product id
    // and hence we will remove all the extras not linked with the system
    const isSingleProduct = singleProductUpdate && singleProductUpdate.singularUpdate

    if (isSingleProduct)
      await productExtras.findAll({
        where: {
          productOptionposID: {[Op.notIn]: singleProductUpdate.existingExtras},
          '$product.barID$': this.barID,
          '$product.posID$': singleProductUpdate.productPosId,
          isDeleted: 'No'
        },
        include: {
          model: product,
          attributes: [],
          required: true,
        }
      }).then(async productExtrasRemovable => {
        // productExtrasRemovable.map(removingRecords => removingRecords.destroy())
        const removingRecordsHan = []
        productExtrasRemovable.map(removingRecords => removingRecordsHan.push(removingRecords.id))
        console.log(`Removing extras: ${removingRecordsHan}`)
        await productExtras.update({isDeleted: 'Yes'}, {where: {id: removingRecordsHan}})
      });

    for (let productExtra of productExtrasReceived) {
      let {id: optionSetId, num_options, name: optionSetName} = productExtra

      // get product extra details to add into sub category only
      // if there are options(IS TREATED AS OUR PRODUCT EXTRAS)
      // in option sets(THIS IS OF NO USE as our system has no PRODUCT EXTRAS heading)
      if (num_options > 0) {
        console.time("FetchAPI");
        const productExtraDetail = await this.posConfigured.fetchApi(this.posConfigured.URL_CONVERSION.KOUNTA_VIEW_PRODUCT_EXTRA_URL(optionSetId))
        console.timeEnd("FetchAPI");
        const {products_assigned, options, created_time, modified_time} = productExtraDetail

        const {count, rows: productIds} = await product.findAndCountAll({
          attributes: ['id'],
          where: {posID: products_assigned}
        })

        // This condition is here to have only those extras which are assigned to a product, for future whenever any isolated extra is linked with product
        // that time backend will have a webhook to update a product there will do a entry of the isolated extra
        if (count === 0) {
          continue
        }

        for (const option of options) {
          let {name, show_online, product_id: posId,} = option

          // Because you won't exact price details with option set API
          const productExtraDetail = await this.posConfigured.fetchApi(this.posConfigured.URL_CONVERSION.KOUNTA_VIEW_PRODUCT_URL(posId))
          let unit_price = productExtraDetail.unit_price

          checkedForNonRemovalPosId.push(posId)

          for (const productId of productIds) {

            let existingExtras = await productExtras.findOne({
              where: {isDeleted: 'No', extraSequence: {[Op.ne]: null}, productID: productId.id},
              order: [['extraSequence', 'DESC']]
            })
            let lastSequence = existingExtras ? existingExtras.extraSequence : 0

            let extras = {
              extraItem: name,
              price: parseFloat(unit_price),
              posID: posId,
              productOptionposID: optionSetId,
              productOptionName: optionSetName,
              // status: show_online ? 'Active' : 'Inactive',
              createdAt: created_time,
              extraSequence: ++lastSequence,
              updatedAt: modified_time,
              productID: productId.id,
            };
            checkedForNonRemovalProductId.push(productId.id)

            // create or update product extras
            productExtras.findOrCreate({
              where: {
                [Op.or]: [
                  {posID: posId},
                  {extraItem: name}
                ],
                productID: productId.id,
                isDeleted: 'No'
              },
              defaults: extras
            }).then(([productData, isCreated]) => {
              if (!isCreated) {
                productData.update({...extras, updatedAt: new Date()});
              }
            })
          }
        }
        if (!isSingleProduct) {
          productExtras.findAll({
            attributes: ['id'],
            where: {
              [Op.and]: {
                [Op.or]: [
                  {posID: {[Op.notIn]: checkedForNonRemovalPosId}},
                  {posID: {[Op.ne]: null}},
                ],
                productID: {[Op.notIn]: checkedForNonRemovalProductId},
                isDeleted: 'No'
              }
            },
            include: {
              model: product,
              attributes: [],
              where: {barID: this.barID},
            }

            //  HERE IT WILL LEAVE ALL THOSE PRODUCT EXTRA WHOSE PRODUCT NOW DON'T EXISTS, but for now it will be
            //  handled from webhook when products gets removed from POS it will remove all the attached extras
          }).then(async removableExtras => {
            // removableExtras.map(removingRecords => removingRecords.destroy())
            const removableExtrasDel = []
            removableExtras.map(removableExtras => removableExtrasDel.push(removableExtras.id))
            await productExtras.update({isDeleted: 'Yes'}, {where: {id: removableExtrasDel}})
          })
        }
      }
    }
  }

  fetchUnSyncedProducts = async (attachedPosConfig, barID) => {
    const unSyncedProducts = await productModel.findAll({
      where: {
        barID: barID,
        posID: {[Op.eq]: null},
        // so not from any other older POS
        fromPosId: {[Op.eq]: null},
      },
      include: [subcategory]
    })
    const productRemoval = unSyncedProducts.filter(unSyncedProduct => unSyncedProduct.id)
    await productModel.update({isDeleted: 'Yes'}, {where: {id: productRemoval}})
    const ExistingSyncedProducts = await productModel.findAll({
      where: {
        barID: barID,
        posID: {[Op.ne]: null},
        // so only current POS products gets synced
        fromPosId: {[Op.eq]: attachedPosConfig},
      },
      include: [subcategory]
    })
    return {unSyncedProducts, ExistingSyncedProducts}
  }
}

function deletedProductVariantTypes(deletedProductVariantTypes, productID) {
  try {
    return new Promise(function (resolve, reject) {
      const deletedProductVariantTypesIDs = deletedProductVariantTypes.split(',');
      productVariantTypes.update(
        {
          isDeleted: 'Yes',
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: {
              [Op.in]: deletedProductVariantTypesIDs
            },
            productID: productID
          }
        }
      ).then(async (deletedProductVariantTypesResponse) => {
        await productVariantSubTypes.update(
          {
            isDeleted: 'Yes',
            updatedAt: new Date()
          },
          {
            returning: true,
            where: {
              productVariantTypeID: {
                [Op.in]: deletedProductVariantTypesIDs
              },
            }
          }
        );
        resolve({
          key: 'deletedProductVariantTypes',
          value: {
            status: 1
          }
        });
      }).catch(() => reject());
    });
  } catch (error) {
    console.log('deletedProductVariantTypes:' + error);
    return;
  }
}

function deletedProductVariantSubTypes(deletedProductVariantSubTypes) {
  try {
    return new Promise(function (resolve, reject) {
      const deletedProductVariantSubTypesIDs = deletedProductVariantSubTypes.split(',');
      productVariantSubTypes.update(
        {
          isDeleted: 'Yes',
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: {
              [Op.in]: deletedProductVariantSubTypesIDs
            },
          }
        }
      ).then(deletedProductVariantSubTypesResponse => {
        resolve({
          key: 'deletedProductVariantSubTypes',
          value: {
            status: 1
          }
        });
      }).catch(() => reject());
    });
  } catch (error) {
    console.log('deletedProductVariantSubTypes:' + error);
    return;
  }
}

function createProductFoodOptions(arrFoodOptions) {
  try {
    return new Promise(function (resolve, reject) {
      productFoodOptions
        .bulkCreate(arrFoodOptions)
        .then(() => {
          resolve({
            key: 'productFoodOptions',
            value: {
              status: 1
            }
          })
        })
    })
  } catch (error) {
    console.log('createProductFoodOptions:' + error)
  }
}

function deletedFoodOptions(deletedFoodOptions, productID) {
  try {
    return new Promise(function (resolve, reject) {
      const deletedFoodOptionIDs = deletedFoodOptions.split(',')
      productFoodOptions.destroy(
        {
          where: {
            id: {
              [Op.in]: deletedFoodOptionIDs
            },
            productID: productID
          }
        }
      ).then(deletedFoodOptions => {
        resolve({
          key: 'deletedFoodOptions',
          value: {
            status: 1
          }
        })
      })
    })
  } catch (error) {
    console.log('deletedFoodOptions:' + error)
  }
}
exports.getProductOptionList = async (req, res) => {
  foodOptions.findAll({
    attributes: [['id', "foodOptionID"], 'name', "initials"],
    order:[['listingOrder', 'ASC']]
    // order:[[sequelize.fn('length', sequelize.col('initials')), 'ASC'], ['initials', 'ASC']]
  }).then((foodOptionList) => {
    res.status(200).json({
      success: 1,
      message: 'Food Option List',
      data: foodOptionList
    });
  }).catch((error) => {
    console.log(error);
    res.status(500).json({
      success: 0,
      message: 'Error while fetching options list!',
      data: []
    });
  });
}

exports.updateProductImagePath = async (req, res, next) => {
  try {
    product
      .findAll({
        where: {
          avatar : {
            [Op.ne] : '',
            [Op.ne] : null,
          }
        }
      })
      .then(async productResponse => {
        let updatedProductIds = [];
        for (var i = 0; i < productResponse.length; i++) {
          if(productResponse[i].dataValues.avatar.search('doshii.io') > 0){
            let imageName = productResponse[i].dataValues.avatar.split('/')[productResponse[i].dataValues.avatar.split('/').length - 1]
            await s3UploadDoshiiFile(
              productResponse[i].dataValues.avatar,
              env.awsProductFolder +
              imageName
            );
            await product.update({
              avatar : imageName,
            },
            {
              where: {
                id: productResponse[i].id
              }
            })
            updatedProductIds.push(productResponse[i].id)
          }else{
            let imageName = productResponse[i].dataValues.avatar.split('/')[productResponse[i].dataValues.avatar.split('/').length - 1]
            await product.update({
              avatar : imageName,
            },
            {
              where: {
                id: productResponse[i].id
              }
            })
            updatedProductIds.push(productResponse[i].id)
          }
        }
        return res.status(200).send({
          success: 1,
          data: updatedProductIds,
          message: 'Updated image id'
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}
