const Sequelize = require('sequelize');
const bar = require('./bar');
const user = require('./user');
const segment = require('./segment');

const segmentUserVenue = sequelize.define(
  'segment_user_venue',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    userID: {
      type: Sequelize.INTEGER(12),
      allowNull: false,
      references: {
        model: 'user',
        key: 'id'
      }
    },
    barID: {
      type: Sequelize.INTEGER(12),
      allowNull: false,
      references: {
        model: 'bar',
        key: 'id'
      }
    },
    segmentID: {
      type: Sequelize.INTEGER(12),
      allowNull: false,
      references: {
        model: 'segment',
        key: 'id'
      }
    },
    convertedDateTime: Sequelize.DATE,
    createdAt: { type: Sequelize.DATE, allowNull: false },
    updatedAt: { type: Sequelize.DATE, allowNull: true }
  },
  {
    timestamps: true,
    freezeTableName: true,
    underscored: false,
  }
)

segmentUserVenue.belongsTo(bar, { foreignKey: 'barID' });
segmentUserVenue.belongsTo(user, { foreignKey: 'userID' });
segmentUserVenue.belongsTo(segment, { foreignKey: 'segmentID' });
segment.hasMany(segmentUserVenue, { foreignKey: 'segmentID' });

module.exports = segmentUserVenue