var Sequelize = require('sequelize')
var sub_category = require('./subCategory')
var bar = require('./bar')

var sub_category_wait_time = sequelize.define(
  'sub_category_wait_time',
  {
    id:{
      type:Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    subCategoryID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references:{
        model: "sub_category",
        key: "id"
      }
    },
    itemActiveHoursID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references:{
        model: "itemActiveHours",
        key: "id"
      }
    },
    waitTimeType: Sequelize.ENUM('1', '2'),
    weekDay: Sequelize.SMALLINT,
    startTime: Sequelize.TIME,
		endTime: Sequelize.TIME,
		waitTime: Sequelize.TIME,
  },
  {
    freezeTableName: true,
    timestamps: false
  },
)

sub_category_wait_time.belongsTo(sub_category, { foreignKey: "subCategoryID" })
sub_category.hasMany(sub_category_wait_time, {  foreignKey: "subCategoryID", as: 'sub_category_wait_time' })

sub_category_wait_time.belongsTo(bar, { foreignKey: "barID"})
bar.hasMany(sub_category_wait_time, { foreignKey: "barID"})

module.exports = sub_category_wait_time