const express = require('express')
var multer = require('multer')
const router = express()

const checkAuth = require('../middleware/checkAuth')
const user = require('../controller/user')

var upload = multer({})

router.post('/register', user.userRegister)
router.post('/checkEmail', upload.array(), user.checkEmail)
router.post('/checkMobile', upload.array(), user.checkMobile)
router.post('/sendEmailOTP', upload.array(), user.sendEmailOTP)
router.post('/verifyEmailOTP', upload.array(), user.verifyEmailOTP)
router.post('/login', upload.array(), user.login)
router.post('/checkUniqueUser', upload.array(), checkAuth, user.checkUniqueUser)
router.get('/getProfile', checkAuth, user.getProfile)
router.post('/editProfile', checkAuth, user.editProfile)
router.post('/updateDeviceToken', checkAuth, upload.array(), user.updateDeviceToken)
router.get('/readPopupMessage', checkAuth, user.readPopupMessage)
router.post('/verifyMobileNumber', checkAuth, user.verifyMobileNumber)
router.post('/changePassword', upload.array(), checkAuth, user.changePassword)
router.post('/resetPassword', upload.array(), user.resetPassword)
router.post('/verifyResetPasswordCode', upload.array(), user.verifyResetPasswordCode)
router.post('/updatePassword', upload.array(), user.updatePassword)
router.post('/logout', upload.array(), checkAuth, user.logout)
router.post('/contactUs', upload.array(), user.contactUs)
router.post('/suggestVenue', upload.array(), user.suggestVenue)
router.post('/delete', upload.array(), checkAuth, user.delete)
router.post('/fav-venue', checkAuth, user.favVenue)
router.post('/fav-venue-list', upload.array(), checkAuth, user.favVenueList);
router.post('/add-location', upload.array(), checkAuth, user.addLocation);

module.exports = router