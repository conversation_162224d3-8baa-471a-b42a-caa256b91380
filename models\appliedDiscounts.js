var Sequelize = require('sequelize')
const discount = require('./discount');
var appliedDiscounts = sequelize.define(
  'applied_discounts',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true,
    },
    userID: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    discountID: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    discountType: {
          type: Sequelize.ENUM('percentage', 'fixed'),
          allowNull: false
        },
    discountCode: {
      type: Sequelize.STRING(255),
      allowNull: false,
    },
    type: {
      type: Sequelize.ENUM('manual','automatic'),
      allowNull: false,
    },
    discountValue: {
      type: Sequelize.DECIMAL(10,2),
      allowNull: false,
    },
    discountAmount: {
      type: Sequelize.DECIMAL(10,2),
      allowNull: false,
    },
    createdAt: { type: Sequelize.DATE, allowNull: false },
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

appliedDiscounts.belongsTo(discount, {
	foreignKey: 'discountID'
});

module.exports = appliedDiscounts







