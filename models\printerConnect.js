const Sequelize = require('sequelize');
const bar = require('./bar');
const subCategory = require('./subCategory');

const printer = sequelize.define(
	'printerConnect',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: true,
			references: {
				model: "bar",
				key: "id"
			}
		},
		printerName: Sequelize.STRING,
		printerModel: Sequelize.STRING,
		networkAddress: Sequelize.STRING,
		status: Sequelize.ENUM(1, 0),
		createdAt: Sequelize.DATE,
		updatedAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false,
	},
);

// bar.belongsTo(pos_conf, {foreignKey: 'attachedPosConfig'})
// subCategory.belongsTo(pos_conf, {foreignKey: 'fromPosId'})
printer.belongsTo(subCategory, { foreignKey: "subCategoryID" })
subCategory.hasMany(printer, { foreignKey: "subCategoryID" })

printer.belongsTo(bar, { foreignKey: "barID" })
bar.hasMany(printer, { foreignKey: "barID" })

module.exports = printer
