const Sequelize = require('sequelize');
const bar = require('./bar');

const discount = sequelize.define(
	'discount',
	{
		id: {
			type: Sequelize.BIGINT,
			primaryKey: true,
			autoIncrement: true
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'bar',
				key: 'id'
			}
		},
		code: {
			type: Sequelize.STRING(255),
			allowNull: false
		},
		type: {
			type: Sequelize.ENUM('manual', 'automatic'),
			allowNull: false
		},
		discountType: {
			type: Sequelize.ENUM('percentage', 'fixed'),
			allowNull: false
		},
		discountValue: {
			type: Sequelize.DECIMAL(10, 2),
			allowNull: false
		},
		startDate: {
			type: Sequelize.DATE,
			allowNull: false
		},
		endDate: {
			type: Sequelize.DATE,
			allowNull: true
		},
		isActive: {
			type: Sequelize.ENUM('0', '1'),
			defaultValue: '1'
		},
		is_combined_discount: {
			type: Sequelize.ENUM('0', '1'),
			defaultValue: '0'
		},
		totalUsageLimit: {
			type: Sequelize.INTEGER,
			allowNull: true
		},
		perUserLimit: {
			type: Sequelize.INTEGER,
			allowNull: true
		},
		eligibilityType: {
			type: Sequelize.ENUM('all_users', 'segment_group', 'individual_users'),
			allowNull: false
		},
		combinedEligibility: {
			type: Sequelize.ENUM('0', '1'),
			allowNull: true,
			defaultValue: '0'
		},
		createdAt: { type: Sequelize.DATE, allowNull: false },
		updatedAt: { type: Sequelize.DATE },
		deletedAt: { type: Sequelize.DATE }
	},
	{
		timestamps: true,
		paranoid: true,
		freezeTableName: true,
		underscored: false,
	}
)

bar.hasMany(discount, {
	foreignKey: 'barID'
});
discount.belongsTo(bar, {
	foreignKey: 'barID'
});


module.exports = discount