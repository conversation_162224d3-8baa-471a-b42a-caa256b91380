const express = require('express')
const router = express()
const multer = require('multer')

const checkUserAuth = require('../middleware/checkAuth')
const checkBarAuth = require('../middleware/barCheckAuth')
const order = require('../controller/order')
const bar = require("../controller/bar");

var upload = multer({})

/*User API*/
router.post('/generateClientSecret', upload.array(), checkUserAuth, order.generateClientSecret)
// router.post('/create', upload.array(), checkUserAuth, bar.isVenueClosedController, order.cartGetItems, order.create)
router.post('/create', upload.array(), checkUserAuth, bar.isVenueClosedControllerV2, order.cartGetItemsV2, order.create)
router.post('/stripe/webhook', upload.array(), order.stripeWebhook)
router.post('/updateOrderPaymentStatus', upload.array(), checkUserAuth, order.updateOrderPaymentStatus)
router.post('/getFees', upload.array(), checkUserAuth, order.getFees)
router.post('/checkPromocode', upload.array(), checkUserAuth, order.checkPromoCode)
router.post('/getPromocode', upload.array(), checkUserAuth, order.getPromocode)
router.post('/orderHistory', upload.array(), checkUserAuth, order.orderHistory)
router.post('/newOrder', upload.array(), checkUserAuth, order.newRunningOrder)
router.post('/singleCurrentOrder', upload.array(), checkUserAuth, order.singleCurrentOrder)
router.post('/orderDetail', upload.array(), checkUserAuth, order.orderDetail)
router.post('/send-invoice', checkUserAuth, order.sendInvoice)
router.post('/orderItemPickupUser', upload.array(), checkUserAuth, order.orderItemPickupUser)
router.post('/addExtraTimeForPickupOrder', upload.array(), checkUserAuth, order.addExtraTimeForPickupOrder)
// router.get('/testAPI', upload.array(), order.testAPI)

/*Bar API*/
router.post('/barOrderList', upload.array(), checkBarAuth, order.barList)
router.post('/barOrderListNew', upload.array(), checkBarAuth, order.barOrderListNew)
router.post('/barOrderListCount', upload.array(), checkBarAuth, order.barOrderListCount)
router.post('/updateStatus', upload.array(), checkBarAuth, order.updateStatus)
router.post('/updateItemStatus', upload.array(), checkBarAuth, order.updateItemStatus)
router.post('/barOrderHistory', upload.array(), checkBarAuth, order.barOrderHistory)
router.post('/barOrderHistoryNew', upload.array(), checkBarAuth, order.barOrderHistoryNew)
router.post('/barOrderHistoryNewUpdated', upload.array(), checkBarAuth, order.barOrderHistoryNewUpdated)
router.post('/barOrderDetail', upload.array(), checkBarAuth, order.barOrderDetail)
router.post('/updateOrderItemWaitTime', upload.array(), checkBarAuth, order.updateOrderItemWaitTime)
router.post('/cancel', upload.array(), checkBarAuth, order.orderCancel)
router.post('/intoxicated', upload.array(), checkBarAuth, order.intoxicated)
router.post('/readyForPickupAlert', upload.array(), checkBarAuth, order.readyForPickupAlert)
router.post('/changeTableNumber', upload.array(), checkUserAuth, order.changeTableNumberController)
router.post('/printDocketOrderResponse', upload.array(), checkBarAuth, order.printDocketOrderResponse)
router.post('/update-docket-printer-status', upload.array(), checkBarAuth, order.updateDocketPrinterStatus)

/* Doshii Webhooks */
router.post('/doshii/webhook', upload.array(), order.verifyDoshiiOrderWebhook);
router.post('/doshiiMenuItemWebhook', upload.array(), order.doshiiMenuItemWebhook);

module.exports = router