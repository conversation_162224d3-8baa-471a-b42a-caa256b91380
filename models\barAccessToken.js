var Sequelize = require('sequelize')
const bar = require("./bar");

var barAccessToken = sequelize.define('bar_accesstoken', {
    id: {
        type: Sequelize.BIGINT,
        autoIncrement: true,
        primaryKey: true
    },
    barID: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
            model: "bar",
            key: "id"
        }
    },
    subCategoryIDs: Sequelize.TEXT,
    activePrintersIDs: Sequelize.TEXT,
    accessToken: Sequelize.TEXT,
    deviceType: Sequelize.ENUM('ios', 'android'),
    deviceToken: Sequelize.TEXT,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
}, {
    freezeTableName: true,
    timestamps: false
})
barAccessToken.belongsTo(bar, { foreignKey: 'barID' })
bar.hasMany(barAccessToken, { foreignKey: 'barID' })

module.exports = barAccessToken