var Sequelize = require('sequelize')
const pickupLocation = require('./pickupLocation')

var pickupLocationSubCategory = sequelize.define(
  'pickup_location_sub_category',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    pickupLocationID: { // <-- Must match exactly (case-sensitive)
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "pickup_location", // Name of the table
        key: "id"
      }
    },
    subCategoryID : Sequelize.INTEGER,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)
pickupLocation.hasMany(pickupLocationSubCategory, { foreignKey: 'pickupLocationID' });
pickupLocationSubCategory.belongsTo(pickupLocation, { foreignKey: 'pickupLocationID' });

module.exports = pickupLocationSubCategory