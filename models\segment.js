const Sequelize = require('sequelize');


const segment = sequelize.define(
  'segment',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    name: Sequelize.STRING(255),
    isParent: {
      type: Sequelize.ENUM('0', '1'),
      defaultValue: '0'
    },
    isActive: {
      type: Sequelize.ENUM('0', '1'),
      defaultValue: '1'
    },
    createdAt: { type: Sequelize.DATE, allowNull: false },
    updatedAt: { type: Sequelize.DATE, allowNull: false }
  },
  {
    timestamps: true,
    freezeTableName: true,
    underscored: false,
  }
)



module.exports = segment