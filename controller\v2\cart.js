var Sequelize = require('sequelize');

const Op = Sequelize.Op;

const cartItems = require('../../models/cartItems');
const cartItemExtras = require('../../models/cartItemExtras');
const cartItemVariants = require('../../models/cartItemVariants');
const cartProductVariantTypes = require('../../models/cartProductVariantTypes');
const cartProductVariantSubTypes = require('../../models/cartProductVariantSubTypes');
const productVariantTypes = require('../../models/productVariantTypes');
const productVariantSubTypes = require('../../models/productVariantSubTypes');
const taxModel = require('../../models/tax')  // 1. & 2. New Taxes Page under Settings.... 
const operatingBarTax = require('../../models/operatingBarTax')  // 1. & 2. New Taxes Page under Settings.... 
const user = require('../../models/user');
const bar = require('../../models/bar');
const product = require('../../models/product');
const productVariants = require('../../models/productVariants');
const productExtras = require('../../models/productExtras');
const settings = require('../../models/settings');
const appliedDiscounts = require('../../models/appliedDiscounts');
const discount = require('../../models/discount');
const discountSegments = require('../../models/discountSegments');
const segmentUserVenue = require('../../models/segmentUserVenue');
const discountUsers = require('../../models/discountUsers');

var env = require('../../config/environment');
var jwt = require('jsonwebtoken');
const moment = require('moment');
const orderDiscount = require('../../models/orderDiscount');
var stripe = require('stripe')(env.stripe_secret_key);


async function checkUsageLimits(discountRecord, userID) {
  const totalUsage = await orderDiscount.count({ where: { discountID: discountRecord.id } });
  if (discountRecord.totalUsageLimit && totalUsage >= discountRecord.totalUsageLimit) return false;
  const userUsage = await orderDiscount.count({ where: { discountID: discountRecord.id, userID } });
  if (discountRecord.perUserLimit && userUsage > 0) return false;
  return true;
}

async function checkSegmentEligibility(userID, discountID, isCombined, barID) {
  const segmentData = await discountSegments.findAll({
    where: {
      discountID,
      barID,
    }
  })
  const segmentIds = segmentData.map((elem) => elem.dataValues.segmentID)
  const segmentUserCount = await segmentUserVenue.count({
    where: {
      barID,
      segmentID: segmentIds,
      userID

    }
  })
  if (segmentUserCount > 0) {
    if (isCombined == "1") {
      return segmentIds.length === segmentUserCount
    }
    else {
      return true
    }
  }
  else {
    return false
  }
}


exports.getCartItems = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID;
  try {
    var whereClauseOrder = [];
    whereClauseOrder.push({
      userID: userID
    });

    cartItems
      .findAll({
        where: whereClauseOrder,
        attributes: [
          'id',
          'productID',
          'barID',
          [Sequelize.literal(`(select restaurantName from bar where bar.id = cart_items.barID ) `), 'restaurantName'],
          'userID',
          'quantity',
          'cartServiceType',
          'specialRequest',
          'createdAt',
          'subCategoryID'
        ],
        include: [
          {
            attributes: [
              'id',
              'name',
              'price',
              'description',
              'posID',
              'avatar',
              'stock',
              'isStockLimit',
            ],
            model: product
          },
          {
            attributes: [
              'id',
              'cartItemID',
              'productExtrasID'
            ],
            model: cartItemExtras,
            include: [
              {
                attributes: [
                  'id',
                  'extraItem',
                  'price',
                  'posID'
                ],
                model: productExtras,
              }
            ]
          },
          {
            attributes: [
              'id',
              'cartItemID',
              'productVariantsID'
            ],
            model: cartItemVariants,
            include: [
              {
                attributes: [
                  'id',
                  'variantType',
                  'price'
                ],
                model: productVariants,
              }
            ]
          },
          {
            model: cartProductVariantTypes,
            attributes: [
              ['id', 'cartProductVariantTypeID'],
            ],
            include: [
              {
                model: cartProductVariantSubTypes,
                attributes: [
                  ['id', 'cartProductVariantSubTypeID'],
                ],
                include: [
                  {
                    attributes: [
                      ['id', 'productVariantTypeID'],
                      'label',
                      'posID',
                    ],
                    model: productVariantTypes,
                    required: true,
                  },
                  {
                    attributes: [
                      ['id', "productVariantSubTypeID"],
                      ['variantType', "extraItem"],
                      'price',
                      'posID',
                    ],
                    model: productVariantSubTypes,
                  }
                ],
              }
            ],
          }
        ],
        order: [["cart_product_variant_types", "cart_product_variant_sub_types", "productVariantTypeID", "asc"]]
      }).then(async cartItemData => {
        if (cartItemData && cartItemData.length > 0) {
          const cartData = {};
          const barData = await bar.findOne({ where: { id: cartItemData[0].dataValues.barID } });
          let cardData = {};
          const userData = await user
            .findOne({
              attributes: [
                'id',
                'email',
                'fullName',
                'stripeID'
              ],
              where: {
                id: userID
              }
            });

          if (userData) {
            var userStripeID = userData.stripeID;
            if (userStripeID != '') {
              let stripeRes = await stripe.customers.listSources(userStripeID, { object: 'card', limit: 1 });
              if (stripeRes.data && stripeRes.data.length > 0) {
                cardData = stripeRes.data[0];
              }
            }
          }

          let cartTotal = cartItemData.reduce((sum, item) => {
            // Base product price
            let basePrice = parseFloat(Number(item.product.price).toFixed(2));
            // Calculate total extras price for the cart item.
            let extrasPrice = 0;
            if (item.cart_item_extras && Array.isArray(item.cart_item_extras)) {
              item.cart_item_extras.forEach(extra => {
                if (extra.product_extra && extra.product_extra.price) {
                  extrasPrice += parseFloat(Number(extra.product_extra.price).toFixed(2));
                }
              });
            }
            // Calculate total variant price for the cart item.
            let variantPrice = 0;
            if (item.cart_product_variant_types && Array.isArray(item.cart_product_variant_types)) {
              item.cart_product_variant_types.forEach(variantType => {
                if (variantType.cart_product_variant_sub_types && Array.isArray(variantType.cart_product_variant_sub_types)) {
                  variantType.cart_product_variant_sub_types.forEach(subType => {
                    if (subType.product_variant_sub_type && subType.product_variant_sub_type.price) {
                      variantPrice += parseFloat(Number(subType.product_variant_sub_type.price).toFixed(2));
                    }
                  });
                }
              });
            }
            // Total price for this cart item = (base + extras + variants) * quantity.
            let totalPricePerItem = (basePrice + extrasPrice + variantPrice) * item.quantity;
            let newSum = sum + totalPricePerItem;
            return parseFloat(newSum.toFixed(2));
          }, 0);

          let runningTotal = cartTotal;
          let cumulativeDiscount = 0;

          const savingsLiteral = Sequelize.literal(`
            ROUND(
              CASE
                WHEN discountType = 'fixed'
                  THEN discountValue
                ELSE (discountValue * CAST(${cartTotal} AS DECIMAL(10,2)) / 100)
              END
            , 2)
          `);

          const bestDiscount = await discount.findOne({
            attributes: {
              include: [
                [ savingsLiteral, 'savings' ]
              ]
            },
            where: {
              barID: barData.id,
              type: 'automatic',
              isActive: '1',
              startDate: { [Op.lte]: new Date() },
              [Op.or]: [
                { endDate: null },
                { endDate: { [Op.gte]: new Date() } }
              ],
            },
            order: [
              [ Sequelize.literal("savings"), 'DESC' ],
              [ Sequelize.col('createdAt'), 'DESC' ]
            ],
            limit: 1
          });
          await appliedDiscounts.destroy({
            where: {
              userID,
              barID: barData.id,
              type:"automatic"
            }
          });
          // let autoDiscountTotal = 0;
          if (bestDiscount) {
            let eligible = true;
            if (bestDiscount.eligibilityType === "segment_group") {
              eligible = await checkSegmentEligibility(
                userID,
                bestDiscount.id,
                bestDiscount.combinedEligibility,
                barData.id
              );
            } else if (bestDiscount.eligibilityType === "individual_users") {
              let userEligible = await discountUsers.findOne({
                where: { discountID: bestDiscount.id, userID },
              });
              eligible = !!userEligible;
            }
            if (eligible) {

              let usageOk = await checkUsageLimits(
                bestDiscount,
                userID
              );
              if (usageOk) {
                let discountAmount = parseFloat(bestDiscount.get("savings"));
                if (runningTotal - discountAmount < 0) {
                  discountAmount = runningTotal;
                }
                runningTotal = parseFloat(
                  (runningTotal - discountAmount).toFixed(2)
                );
                cumulativeDiscount += discountAmount;
                cumulativeDiscount = parseFloat(cumulativeDiscount.toFixed(2));

                var [appliedAutoDiscount, created] = await appliedDiscounts.findOrCreate({
                  where: {
                    userID,
                    barID: barData.id,
                    discountID: bestDiscount.id,
                  },
                  defaults: {
                    discountCode: bestDiscount.code,
                    discountType: bestDiscount.discountType,
                    type: bestDiscount.type,
                    discountValue: parseFloat(Number(bestDiscount.discountValue).toFixed(2)),
                    discountAmount,
                    createdAt: new Date()
                  }
                });
              }
            }
          }
         
          // Process manual discount (if exists)
          let manualDiscountRecord = await appliedDiscounts.findOne({
            where: { userID, barID: barData.id, type: "manual" },
            include:{
              model:discount,
              where:{
                barID: barData.id,
                type: "manual",
                isActive: "1",
                startDate: { [Op.lte]: new Date() },
                [Op.or]: [
                  { endDate: null },
                  { endDate: { [Op.gte]: new Date() } },
                ],
              },
              required:false
            }
          });
          
          if (manualDiscountRecord) {
            if (manualDiscountRecord?.discount && appliedAutoDiscount.is_combined_discount!=="0"||manualDiscountRecord?.discount?.is_combined_discount!=="0") {
              let manualDiscountAmount = 0;
              if (manualDiscountRecord) {
                manualDiscountAmount = parseFloat(
                  Number(manualDiscountRecord.discountAmount).toFixed(2)
                );
                if (runningTotal - manualDiscountAmount < 0) {
                  manualDiscountAmount = runningTotal;
                }
                runningTotal = parseFloat(  
                  (runningTotal - manualDiscountAmount).toFixed(2)
                );
                cumulativeDiscount += manualDiscountAmount;
                cumulativeDiscount = parseFloat(cumulativeDiscount.toFixed(2));
                await appliedDiscounts.update(
                  { discountAmount: manualDiscountAmount },
                  { where: { id: manualDiscountRecord.id } }
                );
              }
            }
            else {
              await appliedDiscounts.destroy({
                where: {
                  id: manualDiscountRecord?.id
                }
              });
            }
          }

          // Ensure the final amount never goes below zero.
          let updatedTotal = Math.max(0, runningTotal);
        
          const feesData = await settings.findAll({ where: { barId: cartItemData[0] ? cartItemData[0].dataValues.barID : 0.99 } });
          // 1. & 2. New Taxes Page under Settings.... Starts
          var taxName
          var taxAmount
          var taxTotalPer
          const taxData = await taxModel.findAll({
            attributes: ['id', 'name', 'percentage', 'status'],
            where: [{ barID: req.body.barID, status: 'Active' }],
            include: [
              {
                model: operatingBarTax,
                required: false,
              }
            ],
          })
          let today = moment().tz('Australia/Perth').isoWeekday() - 1;

          let taxDataArr = [];
          taxData.forEach(tax => {
            if (tax.operating_bar_taxes.length == 0) {
              taxName = tax.dataValues.name
              taxAmount = tax.dataValues.percentage
              taxTotalPer += taxAmount
              delete tax.dataValues.operating_bar_taxes;
              taxDataArr.push(tax);
            } else if (tax.operating_bar_taxes.length) {
              tax.operating_bar_taxes.forEach(operatingTax => {
                if (operatingTax.weekDay === today && !operatingTax.isClosed) {
                  taxName = tax.dataValues.name
                  taxAmount = tax.dataValues.percentage
                  taxTotalPer += taxAmount
                  delete tax.dataValues.operating_bar_taxes;
                  taxDataArr.push(tax);
                }
              });
            }
          });

          const barDataArr = await bar.findOne({
            attributes: [
              'id',
              'restaurantName',
              'serviceType',
              [Sequelize.literal(`CASE WHEN (select count(*) from user_consent_venue where user_consent_venue.barID = bar.id and user_consent_venue.userID = ${userID}) >= 1 THEN 1 ELSE null END`), 'userConsent']
            ],
            where: {
              id: cartItemData[0].dataValues.barID
            },
          })

          const finalAppliedDiscounts=await appliedDiscounts.findAll({
            where: { userID, barID: barData.id },
          });

          let arrTotal = [];
          let taxAmountTotal = 0;
          let transactionFees=0;

          if(taxDataArr.length)
          {
            taxDataArr.forEach((tax) => {
              const percentage = parseFloat(tax.percentage) || 0;
              const amount = (cartTotal * percentage) / 100;
              taxAmountTotal += parseFloat(amount.toFixed(2));
              arrTotal.push({ id:tax.id, name: tax.name, amount: parseFloat(amount.toFixed(2)) });
            });
            }

          if (feesData.length) {
            const transactionFeesObj = feesData.find((elem) => elem.paramKey === "Transaction fee");
            if(transactionFeesObj)
            {
              
              const subtotalWithTaxes = cartTotal + taxAmountTotal;
              if (subtotalWithTaxes > 10) {
                const val = subtotalWithTaxes * transactionFeesObj.paramVar;
                transactionFees = val / 100;
              } else {
                transactionFees = transactionFeesObj.paramFix;
              }
              transactionFees=parseFloat(transactionFees.toFixed(2));
            }
          }

          if (updatedTotal <= 0 && cardData?.country) {
            const account = await stripe.accounts.retrieve();
            const businessCountry = account.country;
            if (businessCountry === cardData.country) {
              transactionFees = (transactionFees + parseFloat(env.stripeStaticCharge)) / (1 - parseFloat(env.stripeDomesticCardCharge))
            }
            else {
              transactionFees = (transactionFees + parseFloat(env.stripeStaticCharge)) / (1 - parseFloat(env.stripeInternationalCardCharge))
            }
          }

          transactionFees=parseFloat(transactionFees.toFixed(2));
          
          cartData.cartItemData = cartItemData;
          cartData.cardData = cardData;
          cartData.feesData = feesData;
          // cartData.taxData1 = taxDataArr;
          cartData.taxData = arrTotal;
          cartData.barData = barDataArr;
          cartData.transactionFees=transactionFees;
          cartData.subTotal = cartTotal;
          cartData.totalDiscount = cumulativeDiscount;
          cartData.totalAmount = updatedTotal;
          cartData.appliedDiscounts = finalAppliedDiscounts
          res.status(200).send({
            success: 1,
            data: cartData,
            message: 'Cart item retrieve successfully!'
          });
        } else {
          res.status(200).send({
            success: 0,
            message: 'Cart item not found!'
          });
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        });
      });
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    });
  }
};

exports.applyManualDiscount = async (req, res) => {
  try {
    const { discountCode, barID, cartTotal } = req.body;
    const sessionData = await jwt.verify(
      req.headers.accesstoken,
      env.ACCESS_TOKEN_SECRET
    );
    const userID = sessionData.userID;

    const discountRecord = await discount.findOne({
      where: {
        code: discountCode,
        barID,
        type: "manual",
        isActive: "1",
        startDate: { [Op.lte]: new Date() },
        [Op.or]: [{ endDate: null }, { endDate: { [Op.gte]: new Date() } }],
      },
    });

    if (!discountRecord) {
      return res
        .status(400)
        .json({ success: 0, message: "Invalid or expired discount code." });
    }

    if (discountRecord.eligibilityType === "segment_group") {
      const isEligible = await checkSegmentEligibility(
        userID,
        discountRecord.id,
        discountRecord.combinedEligibility,
        barID
      );
      if (!isEligible) {
        return res.status(400).json({
          success: 0,
          message: "User is not eligible for this discount.",
        });
      }
    } else if (discountRecord.eligibilityType === "individual_users") {
      const eligibleUser = await discountUsers.findOne({
        where: { discountID: discountRecord.id, userID },
      });
      if (!eligibleUser) {
        return res.status(400).json({
          success: 0,
          message: "User is not eligible for this discount.",
        });
      }
    }

    const usageOk = await checkUsageLimits(discountRecord, userID);
    if (!usageOk) {
      return res
      .status(400)
      .json({ success: 0, message: "Discount usage limit reached." });
    }
    
    // Fetch automatic discounts and calculate their total first
    const autoDiscounts = await appliedDiscounts.findOne({
      where: { userID, barID, type: "automatic" },
      include: {
        model: discount,
        where: {
          barID: barID,
          type: "automatic",
          isActive: "1",
          startDate: { [Op.lte]: new Date() },
          [Op.or]: [
            { endDate: null },
            { endDate: { [Op.gte]: new Date() } },
          ],
        },
        required: false
      }
    });
    
    const totalAutoDiscount = autoDiscounts?.discountAmount || 0;
    if (autoDiscounts) {
      if (autoDiscounts?.discount) {
        if (autoDiscounts?.discount?.is_combined_discount==="0"||discountRecord.is_combined_discount==="0") {
          return res
            .status(400)
            .json({ success: 0, message: "Discount is not combinable." });
        }
      }
      else {
        await appliedDiscounts.destroy({
          where: {
            id: autoDiscounts?.id
          }
        });
      }
    }
    // Calculate initial discount value
    let manualDiscountAmount = 0;
    if (discountRecord.discountType === "percentage") {
      manualDiscountAmount = parseFloat(Math.floor(((cartTotal * discountRecord.discountValue) / 100) * 100) / 100);
    } else {
      manualDiscountAmount = parseFloat(Math.floor(Number(discountRecord.discountValue) * 100) / 100);
    }

    // Adjust for cap to ensure final total doesn't go negative
    const remainingAfterAuto = cartTotal - totalAutoDiscount;
    if (manualDiscountAmount > remainingAfterAuto) {
      manualDiscountAmount = parseFloat(remainingAfterAuto.toFixed(2));
    }

    // Only one manual discount allowed
    await appliedDiscounts.destroy({
      where: { userID, barID, type: "manual" },
    });

    await appliedDiscounts.create({
      userID,
      barID,
      discountID: discountRecord.id,
      discountCode: discountRecord.code,
      discountType: discountRecord.discountType,
      type: discountRecord.type,
      discountValue: parseFloat(
        Number(discountRecord.discountValue).toFixed(2)
      ),
      discountAmount: manualDiscountAmount,
      createdAt: new Date(),
    });


    return res.status(200).json({
      success: 1,
      message: "Manual discount applied successfully.",
    });
  } catch (error) {
    console.log("applyManualDiscount error:", error);
    return res
      .status(500)
      .json({ success: 0, message: "Internal server error." });
  }
};


exports.removeDiscount = async (req, res) => {
  try {
    const { discountID, barID } = req.body; 
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const userID = sessionData.userID;
    const appliedDiscountRecord = await appliedDiscounts.findOne({
      where: { discountID, userID, barID,type:"manual" }
    });

    if (!appliedDiscountRecord) {
      return res.status(400).json({ success: 0, message: 'Discount record not found.' });
    }


    await appliedDiscounts.destroy({ where: { discountID, userID, barID } });
    
   
    
    return res.status(200).json({
       success: 1,
       message: 'Discount removed successfully.',
      
    });
  } catch (error) {
    console.log('removeDiscount error:', error);
    return res.status(500).json({ success: 0, message: 'Internal server error.' });
  }
};

