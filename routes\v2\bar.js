const express = require('express')

const router = express()
var multer = require('multer')
const checkAuth = require('../../middleware/barCheckAuth')
const checkUserAuth = require('../../middleware/checkAuth')
const bar = require('../../controller/v2/bar')

var upload = multer({})

router.post('/getAccountDeleteForm', checkAuth, bar.getAccountDeleteForm)

router.get('/getProductTax', checkAuth, bar.getProductTax)

router.post('/editProfileEmailOTP', checkAuth, bar.editProfileEmailOTP)
router.post('/editProfileVerifyOTP', checkAuth, bar.editProfileVerifyOTP)

router.post('/getSubHeadingWaitTime', checkAuth, bar.getSubHeadingWaitTime);
router.post('/updateSubHeadingWaitTime', checkAuth, bar.updateSubHeadingWaitTime);

/*User API*/
router.post('/getBars', upload.array(), checkUserAuth, bar.getBars)

module.exports = router