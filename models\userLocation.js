var Sequelize = require('sequelize')
var user = require('./user')

var userLocation = sequelize.define('user_locations', {
    id: {
        type: Sequelize.BIGINT,
        autoIncrement: true,
        primaryKey: true
    },
    userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
            model: "user",
            key: "id"
        }
    },
    latitude: Sequelize.TEXT,
    longitude: Sequelize.TEXT,
    address: Sequelize.TEXT,
    city: Sequelize.TEXT,
    state: Sequelize.TEXT,
    pinCode: Sequelize.TEXT,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
}, {
    freezeTableName: true,
    timestamps: false
})

user.hasMany(userLocation, { foreignKey: 'id' })
userLocation.belongsTo(user, { foreignKey: 'userID' })

module.exports = userLocation