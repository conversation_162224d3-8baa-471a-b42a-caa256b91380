var Sequelize = require('sequelize')
const Op = Sequelize.Op

const message = require('../config/message')
const common = require('./common')
const pickupLocation = require('../models/pickupLocation')

var env = require('../config/environment')
var jwt = require('jsonwebtoken');

exports.list = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    pickupLocation
      .findAll({
        where: {
          barID: barID,
          isDeleted: 'No'
        }
      })
      .then(response => {
        res.status(200).send({
          success: 1,
          message: 'Success!',
          data: response
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.add = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    pickupLocation
      .create({
        barID: barID,
        description: req.body.description,
        address: req.body.address,
        latitude: req.body.latitude,
        longitude: req.body.longitude,
        createdAt: new Date()
      })
      .then(async response => {
        resData = await single(response.id)
        res.status(200).send({
          success: 1,
          data: resData,
          message: 'Pick up location added successfully.'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.edit = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    pickupLocation
      .update(
        {
          barID: barID,
          description: req.body.description,
          address: req.body.address,
          latitude: req.body.latitude,
          longitude: req.body.longitude,
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: req.body.id
          }
        }
      )
      .then(async response => {
        resData = await single(req.body.id)
        res.status(200).send({
          success: 1,
          data: resData,
          message: 'Pick up location updated successfully'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.delete = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    pickupLocation
      .update(
        {
          isDeleted: 'Yes',
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: req.body.id,
            barID: barID
          }
        }
      )
      .then(function () {
        res.json({
          success: 1,
          message: 'Pick up location deleted successfully'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

async function single(id) {
  location = await pickupLocation.findOne({
    where: {
      id: id
    }
  })
  return location
}