const express = require('express')
const router = express()
const multer = require('multer')

const checkUserAuth = require('../../middleware/checkAuth')
const cart = require('../../controller/v2/cart')


var upload = multer({})

router.post('/getCartItems', upload.array(), checkUserAuth, cart.getCartItems)
router.post('/applyDiscount', upload.array(), checkUserAuth, cart.applyManualDiscount)
router.post('/removeDiscount', upload.array(), checkUserAuth, cart.removeDiscount)

module.exports = router