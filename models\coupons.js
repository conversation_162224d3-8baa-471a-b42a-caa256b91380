var Sequelize = require('sequelize')
var bar = require('../models/bar')
const sub_category = require('./subCategory')// 9. Promo codes - Subheading specific....
var coupons = sequelize.define(
  'coupons',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    code: Sequelize.TEXT,
    name: Sequelize.TEXT,
    description: Sequelize.TEXT,
    max_uses: Sequelize.INTEGER,
    max_uses_user: Sequelize.INTEGER,
    discount_amount: Sequelize.FLOAT,
    is_fixed: Sequelize.ENUM('Yes', 'No'),
    startsAt: Sequelize.DATEONLY,
    expiresAt:{
      type: Sequelize.DATEONLY,
      allowNull: true,
    },
    status: Sequelize.ENUM('Active', 'Inactive'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No'),
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

coupons.belongsTo(bar, { foreignKey: 'barID' })
bar.hasMany(coupons, { foreignKey: 'barID' })
coupons.belongsTo(sub_category, { foreignKey: 'subcategoryID' })// 9. Promo codes - Subheading specific....
sub_category.hasMany(coupons, { foreignKey: 'subcategoryID' })// 9. Promo codes - Subheading specific....

module.exports = coupons