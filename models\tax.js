// 1. & 2. New Taxes Page under Settings.... Starts
var Sequelize = require('sequelize')
var bar = require('./bar')
var tax = sequelize.define(
  'tax',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    name: Sequelize.TEXT,
    percentage: Sequelize.FLOAT,
    status: Sequelize.ENUM('Active', 'Inactive'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

tax.belongsTo(bar, { foreignKey: 'barID' })
bar.hasMany(tax, { foreignKey: 'barID' })

module.exports = tax

// 1. & 2. New Taxes Page under Settings....  Ends