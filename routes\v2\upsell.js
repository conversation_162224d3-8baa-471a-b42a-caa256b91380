const express = require('express')
const router = express()
const checkAuth = require('../../middleware/barCheckAuth')
const upsell = require('../../controller/v2/upsell')

router.route('/')
  .post(checkAuth, upsell.addVenueSubCategoryUpsell)
  .put(checkAuth, upsell.updateVenueSubCategoryUpsell)
  .delete(checkAuth, upsell.deleteVenueSubCategoryUpsell);

router.post('/list', checkAuth, upsell.getVenueSubCategoryUpsell);


module.exports = router