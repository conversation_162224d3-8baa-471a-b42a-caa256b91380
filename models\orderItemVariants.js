var Sequelize = require('sequelize')
var orderItems = require('./orderItems')
var productVariants = require('./productVariants')

var order_item_variants = sequelize.define(
  'order_item_variants',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderItemID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orderItems",
        key: "id"
      }
    },
    productVariantsID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "productVariants",
        key: "id"
      }
    },
    price: Sequelize.FLOAT,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

order_item_variants.belongsTo(productVariants, { foreignKey: 'productVariantsID' })

orderItems.hasMany(order_item_variants, { foreignKey: 'orderItemID' })
order_item_variants.belongsTo(orderItems, { foreignKey: 'orderItemID' })

module.exports = order_item_variants