var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')
var orders = require('./orders')

var transactionLogs = sequelize.define(
  'transaction_logs',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    userID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "user",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    orderID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orders",
        key: "id"
      }
    },
    amout: Sequelize.FLOAT,
    transaction_type: Sequelize.TEXT,
    transactionID: Sequelize.TEXT,
    transferID: Sequelize.TEXT,
    refundTransactionID: Sequelize.TEXT,
    reversalsTransactionID: Sequelize.TEXT,
    log: Sequelize.TEXT,
    createdAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

transactionLogs.belongsTo(user, { foreignKey: 'userID' })
transactionLogs.belongsTo(bar, { foreignKey: 'barID' })
transactionLogs.belongsTo(orders, { foreignKey: 'orderID' })

module.exports = transactionLogs