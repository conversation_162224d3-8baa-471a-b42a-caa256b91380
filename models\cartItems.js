var Sequelize = require('sequelize')
var product = require('./product')
var user = require('./user')
var bar = require('./bar')
var sub_category = require('./subCategory')

var cart_items = sequelize.define(
  'cart_items',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    userID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "user",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    productID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product",
        key: "id"
      }
    },
    price: Sequelize.FLOAT,
    quantity: Sequelize.INTEGER,
    specialRequest: Sequelize.TEXT,
    cartServiceType: Sequelize.ENUM('TABLE', 'PICKUP'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

cart_items.belongsTo(product, { foreignKey: 'productID' })

user.hasMany(cart_items, { foreignKey: 'userID' })
cart_items.belongsTo(user, { foreignKey: 'userID' })

cart_items.belongsTo(bar, { foreignKey: 'barID' })
cart_items.belongsTo(sub_category, { foreignKey: 'subCategoryID' })

module.exports = cart_items