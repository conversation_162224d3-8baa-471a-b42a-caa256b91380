const express = require('express')
const router = express()
const checkAuth = require('../../middleware/barCheckAuth')
const openingHours = require('../../controller/v2/openingHours')

router.get('/getVenueOpeningHours', checkAuth, openingHours.getVenueOpeningHours)
router.post('/updateVenueOpeningHours', checkAuth, openingHours.updateVenueOpeningHours)
router.post('/updateVenueCategoryOpeningHours', checkAuth, openingHours.updateVenueCategoryOpeningHours)

module.exports = router