var Sequelize = require('sequelize')
var sub_category = require('./subCategory')
var bar = require('./bar')
var operatingHours = require('./operatingHours')

var itemActiveHours = sequelize.define(
  'itemActiveHours',
  {
    id:{
      type:Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    subCategoryID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references:{
        model: "sub_category",
        key: "id"
      }
    },
    weekDay: Sequelize.SMALLINT,
    activeHours: Sequelize.TIME,
		inActiveHours: Sequelize.TIME,
    status: Sequelize.ENUM('1', '0'),
  },
  {
    freezeTableName: true,
    timestamps: false
  },
)

itemActiveHours.belongsTo(sub_category, { foreignKey: "subCategoryID" })
sub_category.hasMany(itemActiveHours, { foreignKey: "subCategoryID" })

itemActiveHours.belongsTo(bar, { foreignKey: "barID"})
bar.hasMany(itemActiveHours, { foreignKey: "barID"})

module.exports = itemActiveHours