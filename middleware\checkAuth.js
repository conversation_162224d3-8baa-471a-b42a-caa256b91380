const message = require("../config/message")
var env = require('../config/environment')
var jwt = require('jsonwebtoken');
const userAccessToken = require('../models/userAccessToken');

module.exports = async (req, res, next) => {
  const token = req.headers.accesstoken;
  if (token == null || token == '') {
    res.status(401).send({
      status: 0,
      message: message.user.sessionExpired
    })
  } else {
    jwt.verify(token, env.ACCESS_TOKEN_SECRET, async (err, user) => {
      if (err) {
        res.status(401).send({
          status: 0,
          message: message.user.sessionExpired
        })
      } else {
        if (user.userType == 'customer') {
          res.locals.userID = user.userID;
          let getUserAuthDetails = await userAccessToken.findOne({
            where: {
              userID: user.userID,
              accessToken: token,
            },
          });
          
          if(!getUserAuthDetails) {
            return res.status(401).send({
              status: 0,
              message: message.user.sessionExpired
            })
          }    
          next()
          return true;
        } else {
          return res.status(401).send({
            status: 0,
            message: message.user.sessionExpired
          })
        }
      }
    })
  }
}