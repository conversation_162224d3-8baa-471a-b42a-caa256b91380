var Sequelize = require('sequelize');
const Op = Sequelize.Op;

const pickupLocation = require('../../models/pickupLocation');
const pickupLocationSubCategory = require('../../models/pickupLocationSubCategory');
const product = require('../../models/product');

var env = require('../../config/environment');
var jwt = require('jsonwebtoken');

// Ensure default pickup location
async function ensureDefaultPickupLocation(barID) {
  let defaultPickupLocation = await pickupLocation.findOne({
    where: { barID, isDeleted: 'No', isDefault: '1' },
    order: [['id', 'ASC']],
  });

  if (!defaultPickupLocation) {
    defaultPickupLocation = await pickupLocation.create({
      barID,
      address: 'Collect at counter',
      isDefault: '1',
    });
  }

  return defaultPickupLocation;
}

exports.list = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID;
  try {
    const data = await pickupLocation.findAll({
      where: { barID, isDeleted: 'No' },
      attributes: { exclude: ['isDeleted', 'createdAt', 'updatedAt'] },
      include: [
        {
          model: pickupLocationSubCategory,
          attributes: ['subCategoryID'],
          include: [
            {
              model: product,
              attributes: [],
              where: {
                isDeleted: 'No',
                barID: barID
              },
              required: true
            }
          ]
        },
      ],
      order: [['isDefault', 'DESC'], 'id']
    });

    res.status(200).send({
      success: 1,
      message: 'Success!',
      data,
    });
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: error.message,
    });
  }
};

exports.add = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID;

  try {
    const newPickupLocation = await pickupLocation.create({
      barID,
      address: req.body.address,
    });

    const data = await pickupLocation.findOne({ where: { id: newPickupLocation.id } });
    res.status(200).send({
      success: 1,
      data,
      message: 'Pickup location added successfully.',
    });
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: error.message,
    });
  }
};

exports.edit = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID;
  const { id, address, sub_category_ids } = req.body;

  try {
    if (address) {
      await pickupLocation.update(
        { address },
        { where: { id, barID } }
      );
    }

    if (sub_category_ids?.length) {
      const existingLinks = await pickupLocationSubCategory.findAll({
        where: { barID, subCategoryID: { [Op.in]: sub_category_ids } },
      });

      const linkedSubCategories = existingLinks.reduce((acc, link) => {
        acc[link.subCategoryID] = link.pickupLocationID;
        return acc;
      }, {});

      for (const subCatID of sub_category_ids) {
        if (linkedSubCategories[subCatID] == id) continue;

        if (linkedSubCategories[subCatID]) {
          await pickupLocationSubCategory.destroy({
            where: { barID, subCategoryID: subCatID },
          });
        }
        
        await product.update(
          { pickupLocationID: id },
          { where: { subCategoryID: subCatID, barID, isDeleted: 'No' } }
        );

        await pickupLocationSubCategory.create({
          barID,
          subCategoryID: subCatID,
          pickupLocationID: id,
        });
      }
    }

    const data = await pickupLocation.findOne({
      where: { id }, 
      include: [
        {
          model: pickupLocationSubCategory,
          attributes: ['subCategoryID'],
        },
      ]
    });
    res.status(200).send({
      success: 1,
      data,
      message: 'Pickup location updated successfully.',
    });
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: error.message,
    });
  }
};

exports.delete = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID;
  const { id } = req.body;

  try {
    const pickup = await pickupLocation.findOne({
      where: { id, barID, isDeleted: 'No', isDefault: '0' },
    });

    if (!pickup) {
      return res.status(404).send({
        success: 0,
        message: 'Pickup location not found or cannot be deleted.',
      });
    }

    const defaultPickupLocation = await ensureDefaultPickupLocation(barID);

    await pickupLocationSubCategory.update(
      { pickupLocationID: defaultPickupLocation.id },
      { where: { pickupLocationID: id } }
    );

    await product.update(
      { pickupLocationID: defaultPickupLocation.id },
      { where: { subCategoryID: { [Op.in]: await getLinkedCategories(defaultPickupLocation.id) }, isDeleted: 'No', barID } }
    );

    await pickupLocation.update({ isDeleted: 'Yes' }, { where: { id, barID } });

    res.status(200).send({
      success: 1,
      message: 'Pickup location deleted successfully.',
    });
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: error.message,
    });
  }
};

async function getLinkedCategories(pickupLocationID) {
  const links = await pickupLocationSubCategory.findAll({
    where: { pickupLocationID },
    attributes: ['subCategoryID'],
  });
  return links.map((link) => link.subCategoryID);
}
