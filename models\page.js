var Sequelize = require('sequelize')

var page = sequelize.define(
  'pages',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    title: Sequelize.TEXT,
    content: Sequelize.TEXT,
    status: Sequelize.ENUM('Active', 'Inactive'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = page
