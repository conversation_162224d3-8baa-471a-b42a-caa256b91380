const Sequelize = require('sequelize');
const bar = require('./bar');
const subCategory = require('./subCategory');
const coupons = require('./coupons');

const couponsSubCatIds = sequelize.define(
	'couponsSubCatIds',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		couponsID: {
			type: Sequelize.INTEGER,
			allowNull: true,
			references: {
				model: "coupons",
				key: "id"
			},
		},
		subCategoryID: {
			type: Sequelize.INTEGER,
			allowNull: true,
			references: {
				model: "sub_category",
				key: "id"
			},
		},
	},
	{
		freezeTableName: true,
		timestamps: false,
	},
);

couponsSubCatIds.belongsTo(coupons, { foreignKey: "couponsID" })
coupons.hasMany(couponsSubCatIds, { foreignKey: "couponsID" })

couponsSubCatIds.belongsTo(subCategory, { foreignKey: "subCategoryID" })
subCategory.hasMany(couponsSubCatIds, { foreignKey: "subCategoryID" })

module.exports = couponsSubCatIds
