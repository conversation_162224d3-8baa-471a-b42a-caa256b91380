var Sequelize = require('sequelize')
const Op = Sequelize.Op
const moment = require('moment')
var env = require('../../config/environment')
const barModel = require("../../models/bar")
const discount = require("../../models/discount")
const discountSegments = require("../../models/discountSegments")
const discountUsers = require("../../models/discountUsers")
const userModel = require("../../models/user")
const segmentsModel = require("../../models/segment")
const message = require('../../config/message')


exports.updateDiscount = async (req, res) => {
  try {
    var bar_id = res.locals.barID

    const { is_active, id } = req.body || {};
    if (!is_active || !id) {
      return res.status(200).send({
        success: 0,
        message: 'Missing required field'
      })
    }
    const Discount = await discount.findOne({
      where: {
        barID: bar_id,
        id
      }
    });

    if (!Discount) {
      return res.status(200).send({
        success: 0,
        message: 'Discount not found'
      })
    }
    const updateObj = { isActive: is_active };

    await discount.update(updateObj, { where: { barID: bar_id, id } });
    return res.status(200).send({
      success: 1,
      message: 'Discount updated successfully'
    })

  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getDiscountList = async (req, res) => {
  try {
    var bar_id = res.locals.barID
    const { page = 1, limit = 100, sort_by, search } = req.body;
    const offset = (page - 1) * limit;
    const bar = await barModel.findOne({
      where: {
        id: bar_id
      },
      attributes: ['timezone']
    });
    const timezone = bar.timezone || 'UTC';
    const whereClause = { barID: bar_id };

    if (search && search.trim()) {
      whereClause[Op.or] = [{ code: { [Op.like]: `%${search.trim()}%` } }];
    }

    let order;
    switch (sort_by) {
      case 'newest':
        order = [['createdAt', 'DESC']];
        break;
      case 'oldest':
        order = [['createdAt', 'ASC']];
        break;
      case 'alphabetical':
        order = [['code', 'ASC']];
        break;
      // case 'most_used':
      // 	order = [[Sequelize.literal('usage_count'), 'DESC']];
      // 	break;
      // case 'least_used':
      // 	order = [[Sequelize.literal('usage_count'), 'ASC']];
      // 	break;
      default:
        order = [['createdAt', 'DESC']];
    }

    const { rows: discounts, count: totalCount } =
      await discount.findAndCountAll({
        attributes: [
          'id',
          'code',
          'type',
          'discountType',
          'discountValue',
          'startDate',
          'endDate',
          'isActive',
          'totalUsageLimit',
          'perUserLimit',
          'eligibilityType',
          'createdAt',
          'updatedAt'
          //change usage count
          // [
          // 	Sequelize.literal(
          // 		'(SELECT COUNT(*) FROM discount_usage WHERE discount_usage.discountID = discount.id)'
          // 	),
          // 	'usage_count'
          // ]
        ],
        where: whereClause,
        order,
        limit: parseInt(limit),
        offset,
        raw: true
      });

    const now = moment();
    const processedDiscount = discounts.map((discount) => {
      let phase;
      const startDate = moment(discount.startDate);
      const endDate = discount.endDate ? moment(discount.endDate) : null;

      if (startDate.isAfter(now)) {
        phase = 'scheduled';
      } else if (endDate && endDate.isBefore(now)) {
        phase = 'expired';
      } else {
        phase = 'active';
      }

      let discountMethod =
        discount.type === 'automatic' ? 'Automatic' : 'Manual';
      let discountType;
      if (discount.discountType === 'percentage') {
        discountType = `${discount.discountValue}% off on order`;
      } else {
        discountType = `$${discount.discountValue} off on order`;
      }

      return {
        id: discount.id,
        title: discount.code,
        phase,
        discount_method: discountMethod,
        discount_type: discountType,
        is_active: discount.isActive,
        usage_count: 0, //change later
        start_date: discount.startDate
          ? moment(discount.startDate).tz(timezone).format('YYYY-MM-DD')
          : null,
        end_date: discount.endDate
          ? moment(discount.endDate).tz(timezone).format('YYYY-MM-DD')
          : null,
        total_usage_limit: discount.totalUsageLimit,
        per_user_limit: discount.perUserLimit,
        eligibility_type: discount.eligibilityType,
        created_at: discount.createdAt,
        updated_at: discount.updatedAt
      };
    });
    return res.status(200).send({
      success: 1,
      data: {
        discounts: processedDiscount,
        pagination: {
          total: totalCount,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(totalCount / limit)
        }
      },
      message: 'Discount retrieved successfully'
    })

  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getDiscountDetails = async (req, res) => {
  try {
    var bar_id = res.locals.barID
    const { discount_id } = req.body || {};

    if (!discount_id) {
      return res.status(200).send({
        success: 0,
        message: 'discount id required'
      })
    }

    const bar = await barModel.findOne({
      where: {
        id: bar_id
      },
      attributes: ['timezone']
    });


    const timezone = bar?.timezone || 'UTC';

    const Discount = await discount.findOne({
      where: {
        id: discount_id,
        barID: bar_id
      },
      include: [
        {
          model: discountSegments,
          attributes: ['id', 'segmentID'],
          include: [
            {
              model: segmentsModel,
              attributes: ['name']
            }
          ]
        },
        {
          model: discountUsers,
          attributes: ['id', 'userID'],
          include: [
            {
              model: userModel,
              attributes: ['fullName']
            }
          ]
        }
      ]
    });

    if (!Discount) {
      return res.status(200).send({
        success: 0,
        message: 'Discount not found'
      })
    }


    const result = {
      id: Discount.id,
      bar_id: Discount.barID,
      code: Discount.code,
      type: Discount.type,
      discount_type: Discount.discountType,
      discount_value: parseFloat(Discount.discountValue),
      start_date: Discount.startDate
        ? moment(Discount.startDate).tz(timezone).format('YYYY-MM-DD')
        : null,
      end_date: Discount.endDate
        ? moment(Discount.endDate).tz(timezone).format('YYYY-MM-DD')
        : null,
      is_active: Discount.isActive,
      total_usage_limit: Discount.totalUsageLimit,
      per_user_limit: Discount.perUserLimit,
      eligibility_type: Discount.eligibilityType,
      combined_eligibility: Discount.combinedEligibility
    };


    if (Discount.discount_segments && Discount.discount_segments.length > 0) {
      result.segments = Discount.discount_segments.map((segment) => ({
        segment_id: segment.segmentID,
        segment_name: segment.segment ? segment.segment.name : null
      }));
      // result.segment_ids = Discount.segments.map(
      // 	(segment) => segment.segmentID
      // );
    }

    if (Discount.discount_users && Discount.discount_users.length > 0) {
      result.users = Discount.discount_users.map((user) => ({
        user_id: user.userID,
        user_name: user.user ? user.user.fullName : null
        // user_email: user.user ? user.user.email : null
      }));
      // result.user_ids = Discount.eligibleUsers.map((user) => user.userID);
    }
    return res.status(200).send({
      success: 1,
      data: result,
      message: 'Discount details retrieved successfully'
    })



  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}