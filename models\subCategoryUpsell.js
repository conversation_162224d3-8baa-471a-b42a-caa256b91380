const Sequelize = require('sequelize');
const sub_category = require('./subCategory');


const venueSubCategoryUpsell = sequelize.define(
	'venue_sub_category_upsell',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'bar',
				key: 'id'
			}
		},
		parentSubCategoryID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'sub_category',
				key: 'id'
			}
		},
		childSubCategoryID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'sub_category',
				key: 'id'
			}
		},
		createdAt: { type: Sequelize.DATE, allowNull: false },
		updatedAt: { type: Sequelize.DATE },
	},
	{
		freezeTableName: true,
		timestamps: true,
		underscored: false,
	}
)

venueSubCategoryUpsell.belongsTo(sub_category, {
	foreignKey: 'childSubCategoryID',
	as: 'subCategory'
});

sub_category.hasOne(venueSubCategoryUpsell, {
	foreignKey: 'parentSubCategoryID',
	as: 'childSubCategoryLink'
});


module.exports = venueSubCategoryUpsell