var Sequelize = require('sequelize')
const user = require('../models/user')
const order = require('../models/orders')

var order_item_wait_time_notifications = sequelize.define(
  'order_item_wait_time_notifications',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orders",
        key: "id"
      }
    },
    orderItemID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orderItems",
        key: "id"
      }
    },
    userID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "users",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    pickupLocationID: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "pickup_location",
        key: "id"
      }
    },
    waitTime: Sequelize.TIME,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No'),
    notification_type: Sequelize.ENUM('1', '2')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

order_item_wait_time_notifications.belongsTo(user, { foreignKey: 'userID' })
order_item_wait_time_notifications.belongsTo(order, { foreignKey: 'orderID' })

module.exports = order_item_wait_time_notifications