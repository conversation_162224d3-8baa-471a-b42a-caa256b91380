var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')
var coupons = require('./coupons')

var orders = sequelize.define(
  'orders',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderNo: Sequelize.TEXT,
    posOrderId: {
      type: Sequelize.STRING,
      allowNull: true
    },
    posCheckInId: {
      type: Sequelize.STRING,
      allowNull: true
    },
    pickupCode: Sequelize.TEXT,
    subTotal: Sequelize.FLOAT,
    transactionFee: Sequelize.FLOAT,
    stripeFee: Sequelize.FLOAT,
    cardType: Sequelize.TEXT,
    cardNumber: Sequelize.TEXT,
    fundingType: Sequelize.TEXT,
    promocode_id: Sequelize.INTEGER,
    promocode_amount: Sequelize.FLOAT,
    promocode_discount: Sequelize.FLOAT,
    totalDiscountedAmount: Sequelize.FLOAT,
    tax: Sequelize.FLOAT,
    total: Sequelize.FLOAT,
    orderDate: Sequelize.DATEONLY,
    convertedOrderDate: Sequelize.DATEONLY,
    convertedOrderDateTime: Sequelize.DATE,
    orderStatus: Sequelize.ENUM('New', 'Preparing', 'Pickup', 'Pickedup', 'NotPickedup', 'Intoxicated'),
    posOrderStatus: Sequelize.STRING,
    isPosOrder: Sequelize.ENUM('0','1'),
    posOrderFee: Sequelize.STRING,
    posOrderVersion: Sequelize.STRING,
    posFailedCount: Sequelize.INTEGER,
    PreparingStartTime: Sequelize.DATE,
    ReadyTime: Sequelize.DATE,
    PickedupTime: Sequelize.DATE,
    paymentStatus: Sequelize.ENUM('notReceived', 'received', 'failed'),
    orderServiceType: Sequelize.ENUM('TABLE', 'PICKUP'),
    userID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "user",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    paymentType: Sequelize.ENUM('1', '2','3'), // 1 - Card, 2 - Apple Pay , 3 - Google Pay
    paymentIntentId: Sequelize.STRING,
    posTransactionId: Sequelize.STRING,
    posTransactionVersion: Sequelize.STRING,
    transactionID: Sequelize.TEXT,
    transferID: Sequelize.TEXT,
    refundTransactionID: Sequelize.TEXT,
    reversalsTransactionID: Sequelize.TEXT,
    refundStatus: Sequelize.ENUM('No', 'Refunded', 'PartialRefunded'),
    isCanceled: Sequelize.ENUM('Yes', 'No'),
    intoxicatedDate: Sequelize.DATE,
    refundedDate: Sequelize.DATE,
    refundTransactionFee: {
      type:Sequelize.ENUM('Yes', 'No'),
      defaultValue: "No"
    },
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No'),
    isDocketOrder: Sequelize.ENUM('0','1'),
    docketOrderFee: Sequelize.STRING,
    docketPrintingStatus:{
      type: Sequelize.BOOLEAN, 
    }
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

orders.belongsTo(user, { foreignKey: 'userID' })
orders.belongsTo(bar, { foreignKey: 'barID' })
orders.belongsTo(coupons, { foreignKey: 'promocode_id' })
bar.hasMany(orders, { foreignKey: 'barID' })

module.exports = orders