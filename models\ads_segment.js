var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')
var segment = require('./segment')
var ads = require('./ads')

var env = require('../config/environment')

var ads_segment = sequelize.define(
    'ads_segment',
    {
        id: {
            type: Sequelize.BIGINT,
            autoIncrement: true,
            primaryKey: true
        },
        id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true
        },
        segmentID: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'segment',
                key: 'id'
            }
        },
        adsID: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'ads',
                key: 'id'
            }
        },
        barID: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'bar',
                key: 'id'
            }
        }
    },
    {
        freezeTableName: true,
        timestamps: false
    }
)

module.exports = ads_segment