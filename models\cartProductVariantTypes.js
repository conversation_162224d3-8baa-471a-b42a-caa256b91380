var Sequelize = require('sequelize');
var cartItems = require('./cartItems');
var productVariantTypes = require('./productVariantTypes');
var cartProductVariantSubTypes = require('./cartProductVariantSubTypes');

var cart_product_variant_types = sequelize.define(
  'cart_product_variant_types',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    cartItemID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "cartItems",
        key: "id"
      }
    },
    productVariantTypeID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product_variant_types",
        key: "id"
      }
    },
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
);

cart_product_variant_types.belongsTo(productVariantTypes, { foreignKey: 'productVariantTypeID' });
cart_product_variant_types.belongsTo(cartItems, { foreignKey: 'cartItemID' });
cartItems.hasMany(cart_product_variant_types, { foreignKey: 'cartItemID' });
cart_product_variant_types.hasMany(cartProductVariantSubTypes, { foreignKey: 'cartProductVariantTypeID' });

module.exports = cart_product_variant_types;
