var Sequelize = require('sequelize')

var foodOptions = sequelize.define(
  'food_options',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    name: Sequelize.TEXT,
    initials: Sequelize.TEXT,
    status: Sequelize.ENUM('Active', 'Inactive'),
    listingOrder: Sequelize.INTEGER,
    isDeleted: Sequelize.ENUM('Yes', 'No'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = foodOptions