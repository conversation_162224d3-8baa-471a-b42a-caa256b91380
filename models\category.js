var Sequelize = require('sequelize')

var category = sequelize.define(
  'category',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    name: Sequelize.TEXT,
    status: Sequelize.ENUM('Active', 'Inactive'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = category