const Sequelize = require('sequelize');

const crashLogs = sequelize.define(
  'firebase_crash_logs',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    payload: {
      type: Sequelize.JSON,
      allowNull: false
    },
    created_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
    }
  },
  {
    freezeTableName: true,
    timestamps: false
  }
);

module.exports = crashLogs;
