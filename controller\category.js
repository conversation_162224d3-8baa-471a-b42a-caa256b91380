var Sequelize = require('sequelize')
const Op = Sequelize.Op
const { QueryTypes } = require('sequelize');

const category = require('../models/category')
const subCategory = require('../models/subCategory')
const barCategorySequence = require('../models/barCategorySequence')
const productModel = require("../models/product");
const productExtras = require("../models/productExtras");
const bar = require('../models/bar')
const sO = require('../config/database')

var env = require('../config/environment')
var jwt = require('jsonwebtoken')
const commonFunction = require("../common/commonFunction");
const subcategory = require("../models/subCategory");
const barAccessToken = require('../models/barAccessToken')

exports.getCategory = async (req, res) => {
  try {
    category
      .findAll({
        attributes: [
          'id',
          'name'
        ],
        where: {
          isDeleted: 'No'
        }
      })
      .then(response => {
        res.status(200).send({
          success: 1,
          message: 'Success!',
          data: response
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getSubCategory = async (req, res) => {
  var categoryID = req.body.categoryID
  var whereClause = {
    isDeleted: 'No'
  }
  if (categoryID) {
    whereClause['categoryID'] = categoryID
  }
  try {
    subCategory
      .findAll({
        attributes: [
          'id',
          'categoryID',
          'name'
        ],
        where: whereClause,
        include: [{
          model: barCategorySequence,
          as: 'bar_category_sequence',
          required: false,
          attributes: [[sequelize.fn('coalesce', sequelize.col('subCategorySequence'), 'infinite'), 'subCategorySequence']]
        }],
        order: [[sequelize.literal('`bar_category_sequence.subCategorySequence`')]],
      })
      .then(response => {
        res.status(200).send({
          success: 1,
          message: 'Success!',
          data: response
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.barSubCategorySequence = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID
    const subCategoryIds = JSON.parse(req.body.sub_category_ids)
    subCategory
      .count({
        where: {isDeleted: 'No', id: subCategoryIds}
      })
      .then(async response => {
        if (response > 0) {
          await barCategorySequence.destroy({where: {barId: barID}})
            .then(async () =>
              await barCategorySequence.bulkCreate(
                subCategoryIds
                  .map((subcategory, index) => {
                    return {
                      barId: barID,
                      subCategorySequence: index + 1,
                      subCategoryId: subcategory
                    }
                  })))
          res.status(200).send({
            success: 1,
            message: 'Success!',
            data: response
          })
        } else {
          res.status(200).send({
            success: 0,
            message: 'Incorrect sub categories provided!',
            data: response
          })
        }
      }).error(function (err) {
      res.status(200).json({
        success: 0,
        message: err.message,
      })
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getCategoryList = async (req, res) => {
  var whereClause = {
    isDeleted: 'No'
  }

  try {
    subCategory
      .findAll({
        attributes: [
          'id',
          'categoryID',
          'name',
          [
            Sequelize.literal(
              '(select count(product.id) from product WHERE product.subCategoryID = `sub_category`.id AND product.isDeleted="No" AND product.status="Active" AND product.barID=' + req.body.barID + ')'
            ),
            'totalProduct'
          ],
        ],
        where: whereClause,
        having: {
          totalProduct: {
            [Op.gt]: 0
          }
        }
      })
      .then(response => {

        if (response.length > 0) {
          res.status(200).send({
            success: 1,
            message: 'Results Found',
            data: response
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: []
          })
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.categoryPosHandling = class categoryPosHandling {
  upsertKountaCategoryList = async (sub_categories) => {
    let checkedForNonRemoval = [];
    const isSingleRecord = !Array.isArray(sub_categories)
    let subCategoriesIterable = isSingleRecord ? [sub_categories] : sub_categories;

    for (let sub_category of subCategoriesIterable) {
      let {id, name, show_online} = sub_category
      checkedForNonRemoval.push(id)

      const subCategoryDetails = {
        name: name,
        posID: id,
        categoryID: -1, // static id for subcategory pulled from POS is 0
        fromPosId: 1, // static id for kounta in pos_conf table is 1
        status: show_online ? 'Active' : 'Inactive',
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      try {
        subcategory.findOrCreate({
          defaults: subCategoryDetails,
          where: {posID: id}
        }).then(([subcategoryResponse, isCreated]) => {
          if (!isCreated) {
            subcategoryResponse.update({...subCategoryDetails, updatedAt: new Date()})
          }
        })
      } catch (e) {
        console.log("From category sync: ", e)
      }
    }

    // remove any redundant subcategory
    if (!isSingleRecord)
      subcategory.findAll({
        where: {
          posID: {[Op.notIn]: checkedForNonRemoval},
        },
      }).then(removableSubCategories => {
        removableSubCategories.map(removingRecords => {
          removingRecords.destroy()
          productModel.destroy({
            where: {subCategoryID: removingRecords.id}
          })
        })
      })
  }
}

exports.getSubCategoryList = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID
  let getUserCategory = await barAccessToken.findOne({
    where: {
      barID: barID,
      accessToken: req.headers.accesstoken,
    }
  })
  const barData = await bar.findOne({
      attributes: [
        'id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile',
        'stripeID', 'venueId', 'attachedPosConfig', 'docketCommission', 'posStatus'
      ],
      where: {
        id: barID
      },
    })
  getUserCategory = getUserCategory && getUserCategory.subCategoryIDs !== null ? getUserCategory && getUserCategory.subCategoryIDs && getUserCategory.subCategoryIDs.split(',') : [];
  
  var whereClause = []
  if(barData.dataValues.posStatus && barData.dataValues.posStatus == '1'){
    whereClause.push({
      categoryID: '-1'
    })
  }else{
    whereClause.push({
      [Op.not] : [{categoryID: '-1'}]
    })
  }
  try {
    const subCategoryList = await subCategory
    .findAll({
      attributes: [
        [Sequelize.literal(`sub_category.id`), 'subCategoryID'],
        'name',
        [Sequelize.literal(`(select count(*) from product where product.barID = ${barID} and isDeleted = 'no' and subCategoryID = sub_category.id limit 1) `), 'productCount'],
      ],
      where: whereClause,
      include: [{
        model: barCategorySequence,
        as: 'bar_category_sequence',
        required: false,
        attributes: [[sequelize.fn('coalesce', sequelize.col('subCategorySequence'), 1000000000000), 'subCategorySequence']],
        where: {barId: barID}
      }],
      order: [[sequelize.literal('`bar_category_sequence.subCategorySequence`')]],
    })
    if (subCategoryList.length > 0) {
      subCategoryList.map((category) => {
        if(getUserCategory.includes(""+category.dataValues.subCategoryID)){
          category.dataValues.isActive = 1;
        }else{
          category.dataValues.isActive = 0;
        }
        delete category.dataValues.bar_category_sequence;
        return category
      })
      res.status(200).send({
        success: 1,
        message: 'List of subcategories retrieved successfully.',
        data: subCategoryList.filter(category => category.dataValues.productCount > 0)
      })
    } else {
      res.status(200).send({
        success: 1,
        message: 'No subcategories found.',
        data: subCategoryList
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}