const Sequelize = require('sequelize');
const segment_tags = require('./segmentTags');
const product = require('./product');

const SegmentProductTags = sequelize.define(
	'segment_product_tags',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		productID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'product',
				key: 'id'
			}
		},
		tagID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'segment_tags',
				key: 'id'
			}
		},
		
		createdAt: { type: Sequelize.DATE, allowNull: false },
		updatedAt: { type: Sequelize.DATE },
		deletedAt: { type: Sequelize.DATE }
	},
	{
		freezeTableName: true,
		timestamps: true,
		underscored: false,
		paranoid: true
	}
)

product.hasMany(SegmentProductTags, {
	foreignKey: 'productID',
});

SegmentProductTags.belongsTo(product, {
	foreignKey: 'productID',
});

segment_tags.hasMany(SegmentProductTags, {
	foreignKey: 'tagID',
});

SegmentProductTags.belongsTo(segment_tags, {
	foreignKey: 'tagID',
});


module.exports = SegmentProductTags