var Sequelize = require('sequelize')
const Op = Sequelize.Op
const moment = require('moment')
const segmentTagsModel = require("../../models/segmentTags")
const message = require('../../config/message')
const productVariants = require('../../models/productVariants')
const productExtras = require('../../models/productExtras')
const pickupLocation = require('../../models/pickupLocation')
const product = require('../../models/product')
const productVariantTypes = require('../../models/productVariantTypes')
const productVariantSubTypes = require('../../models/productVariantSubTypes')
const segmentProductTagsModel = require('../../models/segmentProductTags')
const subCategoryUpsellModel = require("../../models/subCategoryUpsell")
const subCategory = require('../../models/subCategory')
const bar = require('../../models/bar')
const barCategorySequence = require('../../models/barCategorySequence')
const barOpeningHoursUTC = require('../../models/barOpeningHoursUTC')
const barSubCategoryWaitTimeUTC = require('../../models/barSubCategoryWaitTimeUTC')
const barProductSequence = require('../../models/barProductSequence')
const orders = require('../../models/orders')
const orderItems = require('../../models/orderItems')
const orderItemExtras = require('../../models/orderItemExtras')
const orderItemVariants = require('../../models/orderItemVariants')
const orderProductVariantTypes = require('../../models/orderProductVariantTypes')
const orderProductVariantSubTypes = require('../../models/orderProductVariantSubTypes')
const cartItems = require('../../models/cartItems')
const foodOptions = require('../../models/foodOptions')
const productFoodOptions = require('../../models/productFoodOptions')
const barOpeningHours = require('../../models/barOpeningHours')
const {capitalize} = require('lodash')

exports.getSegmentTags = async (req, res) => {
  try {
    const segmentTags = await segmentTagsModel.findAll();

    res.status(200).send({
      success: 1,
      data: segmentTags,
      message: message.product.listFetched
    })


  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getItemProfile = async (req, res) => {
  var productID = req.body.id
  var barID = req.body.barID
  const serviceType = req.body.serviceType


  try {

    if (!productID || !barID || !serviceType) {
      return res.status(200).send({
        success: 0,
        message: message.product.notFound
      })
    }

    const data = await fetchProductDetails(productID, barID, serviceType);

    if (data) {
      const upsellCategory = data?.sub_category?.childSubCategoryLink?.childSubCategoryID;
      let upsellItems = [];

      if (upsellCategory) {

        const currentDateTimeUTC = moment().utc().format('YYYY-MM-DD HH:mm:ss');
        const currentDay = moment(currentDateTimeUTC).isoWeekday() - 1;
        const currentTime = moment(currentDateTimeUTC).format('HH:mm:ss');

        const activeSubCategory = await subCategory.findOne({
          where: {
            id: upsellCategory,
            [Op.and]: [
              sequelize.literal(`EXISTS (
                SELECT 1 FROM bar_sub_category_opening_hours_utc AS BSH 
                WHERE BSH.subCategoryID = ${upsellCategory} 
                AND BSH.barID = ${barID} 
                AND BSH.weekDay = ${currentDay} 
                AND BSH.isClosed = 0
                AND CAST('${currentTime}' AS TIME) 
                    BETWEEN CAST(BSH.openingHours AS TIME) 
                    AND CAST(BSH.closingHours AS TIME)
              )`)
            ],
          }
        });

        if (activeSubCategory) {
          const productWhereClause = [{
            isDeleted: "No",
            status: "Active",
            barID: barID,
            subCategoryID: upsellCategory,
            [Op.or]: [
              { isStockLimit: "No" },
              {
                  [Op.and]: [
                      { isStockLimit: "Yes" },
                      { stock: { [Op.gt]: 0 } }
                  ]
              }
          ]
          }]

          if (serviceType && serviceType.toLowerCase() !== 'both') {
            productWhereClause.push({
              [Op.or]: [
                { serviceType: serviceType },
                { serviceType: "BOTH" }
              ],
            })
          }

          const upsellProductIds = await product.findAll({
            attributes: ["id"],
            where: productWhereClause,
            limit: 3,
            order: sequelize.literal('RAND()')
          });

          if (upsellProductIds.length) {
            const promises = upsellProductIds.map(item =>
              fetchProductDetails(item.id, barID, serviceType)
            );
            upsellItems = await Promise.all(promises);
          }
        }

      }
      res.status(200).send({
        success: 1,
        data: { ...data.get(), upsell: upsellItems },
        message: 'success!'
      })
    }
    else {
      return res.status(200).send({
        success: 0,
        message: message.product.notFound
      })
    }

  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

async function fetchProductDetails(productID, barID = null, serviceType = null) {
  try {
    const productWhereClause = {
      isDeleted: 'No',
      id: productID
    };

    if (barID != null) {
      productWhereClause.barID = barID;
    }

    let productVariantTypeWhereClause = [{
      isDeleted: "No"
    }];

    if (serviceType && serviceType.toLowerCase() !== 'both') {
      productVariantTypeWhereClause.push({
        [Op.or]: [
          { serviceType: serviceType },
          { serviceType: "BOTH" }
        ],
      });
    }

    const currentDateTimeUTC = moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const currentDay = moment(currentDateTimeUTC).isoWeekday() - 1;
    const currentTime = moment(currentDateTimeUTC).format('HH:mm:ss');

    let result = await product.findOne({
      attributes: [
        'id',
        'barID',
        'categoryID',
        'subCategoryID',
        'name',
        'description',
        'avatar',
        'serviceType',
        'price',
        'posID',
        'pickupLocationID',
        'stock',
        'isStockLimit',
        'dailyStockRenewal',
        'isDailyStockRenewal',
        'status',
        [
          Sequelize.literal(`
            (select waitTime from bar_sub_category_wait_time_utc 
              where 
                barID = product.barID and
                subCategoryID = sub_category.id and
                weekDay = '${currentDay}' and
                startTime <= '${currentTime}' and
                endTime >= '${currentTime}'
            )`
          ), 'waitTime'
        ],
      ],
      where: productWhereClause,
      include: [
        {
          model: pickupLocation,
          attributes: ['id', 'description', 'address', 'latitude', 'longitude']
        },
        {
          model: subCategory,
          as: "sub_category",
          attributes: ['id', 'name'],
          include: {
            model: subCategoryUpsellModel,
            attributes: ["childSubCategoryID"],
            as: "childSubCategoryLink",
            required: false,
            where: { barID },
          },
        },
        {
          model: productVariantTypes,
          as: 'productVariantTypes',
          required: false,
          attributes: ["id", "label", "productID", "serviceType", "posID"],
          where: productVariantTypeWhereClause,
          include: {
            model: productVariantSubTypes,
            as: 'productVariantSubTypes',
            required: false,
            attributes: ["id", ["variantType", "extraItem"], "price", "extraSequence", "posID"],
            where: { isDeleted: "No" }
          }
        }, {
          model: productVariants,
          as: "product_variants",
          required: false,
          where: { isDeleted: 'No' },
          attributes: ['id', 'productID', 'variantType', 'price']
        },
        {
          model: productExtras,
          as: "product_extras",
          required: false,
          where: { isDeleted: 'No' },
          attributes: ['id', 'productID', 'extraItem', 'price', 'posID', 'productOptionposID', 'productOptionName'],
        },
        {
          model: productFoodOptions,
          as: "productFoodOptions",
          attributes: [
            "id",
            "productID",
            "foodOptionID",
            [Sequelize.literal(`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "name"],
            [Sequelize.literal(`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "initials"]
          ],
          include: {
            model: foodOptions,
            as: "foodOptions",
            attributes: []
          }
        },
        {
          model: segmentProductTagsModel,
          attributes: ['id'],
          include: {
            model: segmentTagsModel,
            attributes: ['id', 'name']
          }
        }
      ],
      order: [
        ["productVariantTypes", "id", "ASC"],
        ["productVariantTypes", "productVariantSubTypes", "extraSequence", "ASC"],
        ["productFoodOptions", "foodOptions",'listingOrder', 'ASC'],
        ["product_extras", "extraSequence", "ASC"]
      ]
    })
    return result
  } catch (error) {
    throw error
  }
}

function fetchProductList(category, barID, status = 'Active', StockAvailable = 0, selectedServiceType = null) {
  return new Promise(function (resolve, reject) {
    var productWhereClause = []
    productWhereClause.push({
      isDeleted: 'No',
      subCategoryID: category.id,
      barID: barID,
    })

    if (status == 'Active') {
      productWhereClause.push({
        status: 'Active'
      })
    }

    if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
      productWhereClause.push({
        [Op.or]: [
          {serviceType: selectedServiceType},
          {serviceType: "BOTH"}
        ],
      })
    }

    product
      .findAll({
        attributes: [
          'id',
          'barID',
          'categoryID',
          'subCategoryID',
          'name',
          'description',
          'price',
          'pickupLocationID',
          'status',
          'avatar',
          'serviceType',
          'stock',
          'isStockLimit',
          'dailyStockRenewal',
          'isDailyStockRenewal',
          [
            Sequelize.literal(`(CASE WHEN product.isStockLimit = 'Yes' THEN (CASE WHEN product.stock > 0 THEN '1' ELSE '0' END) ELSE '1' END)`),
            'productIsValid'
          ],
          [
            Sequelize.literal(
              '(select IFNULL(min(product_variants.price), 0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'minPrice'
          ],
          [
            Sequelize.literal(
              '(select IFNULL(max(product_variants.price),0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'maxPrice'
          ],
        ],
        include: [
          {
            model: productFoodOptions,
            as: "productFoodOptions",
            attributes: ["id", "productID", "foodOptionID",
              [Sequelize.literal(`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "name"],
              [Sequelize.literal(`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "initials"],
            ],
            include: {
              model: foodOptions,
              as: "foodOptions",
              attributes: [],
            },
          },
          {
            model: barProductSequence,
            as: 'bar_product_sequence',
            required: false,
            attributes: [[sequelize.fn('coalesce', sequelize.col('productSequence'),100000000000), 'productSequence']],
          }
        ],
        where: productWhereClause,
        distinct: true,
        order: [[Sequelize.literal(`bar_product_sequence.productSequence`), "DESC"], ["productFoodOptions", "foodOptions", "listingOrder", "ASC"], "id"], // 8. Ability to rearrange menu products under subheadings add
      })
      .then(productFetchResponse => {
        var resData = {
          categoryID: category.id,
          mainCategoryID: category.categoryID,
          categoryName: category.name,
          categoryWaitTime: category.dataValues.waitTime,
          categoryProduct: StockAvailable == 1 ? productFetchResponse.filter(product => product.dataValues.productIsValid == '1') : productFetchResponse,
          categoryActive: category.dataValues.operatingFlag ? category.dataValues.operatingFlag : 0
        }
        resolve({
          key: 'categoryWiseProduct',
          value: resData
        })
      })
  })
}

function fetchPopularProductList(barID, categoryID = null, subCategoryIDs = null, serviceType = null) {
  return new Promise(function (resolve, reject) {
    var productWhereClause = []
    if ( subCategoryIDs.length == 0) {
      productWhereClause.push({
        isDeleted: 'No',
        barID: barID
      })
    } else {
      productWhereClause.push({
        isDeleted: 'No',
        barID: barID,
        subCategoryID: subCategoryIDs,
        status: 'Active'
      })
    }

    if (categoryID) {
      productWhereClause.push({
        categoryID: categoryID
      })
    }
    
    if (serviceType && serviceType.toLowerCase() !== 'both') {
      productWhereClause.push({
        [Op.or]: [
          {serviceType: serviceType},
          {serviceType: "BOTH"}
        ],
      })
    }

    product
      .findAll({
        attributes: [
          'id',
          'barID',
          'categoryID',
          'subCategoryID',
          'name',
          'description',
          'price',
          'pickupLocationID',
          'status',
          'avatar',
          'serviceType',
          'stock',
          'isStockLimit',
          'dailyStockRenewal',
          'isDailyStockRenewal',
          [
            Sequelize.literal(`(CASE WHEN product.isStockLimit = 'Yes' THEN (CASE WHEN product.stock > 0 THEN '1' ELSE '0' END) ELSE '1' END)`),
            'productIsValid'
          ],
          [
            Sequelize.literal(
              '(select IFNULL(min(product_variants.price), 0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'minPrice'
          ],
          [
            Sequelize.literal(
              '(select IFNULL(max(product_variants.price),0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
            ),
            'maxPrice'
          ],
          [
            Sequelize.literal(
              '(select SUM(quantity) from order_items WHERE order_items.productID = `product`.id)'
            ),
            'soldQuantity'
          ],
        ],
        include: {
          model: productFoodOptions,
          as: "productFoodOptions",
          attributes: ["id", "productID", "foodOptionID",
            [Sequelize.literal(`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "name"],
            [Sequelize.literal(`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "initials"],
          ],
          include: {
            model: foodOptions,
            as: "foodOptions",
            attributes: [],
            
          }
        },
        where: productWhereClause,
        order: [sequelize.literal('soldQuantity DESC'), ['id', 'DESC'],["productFoodOptions", "foodOptions", "listingOrder", "ASC"]],
        distinct: true,
      })
      .then(productFetchResponse => {
        let productResponse = [];
        for (let index = 0; index < 5; index++) {
          let product = productFetchResponse[index]
          if(product && product.dataValues.productIsValid == '1' && product.dataValues.soldQuantity > '0'){
            productResponse.push(product)
          }
        }
        var resData = {
          categoryID: 0,
          maincategoryID: 0,
          categoryName: 'Popular',
          categoryProduct: productResponse,
          categoryActive: 1
        }
        resolve({
          key: 'categoryWiseProduct',
          value: resData
        })
      })
  })
}

function fetchOrderAgainProductList(barID, productsData = [], serviceType = null) {
  return new Promise(function (resolve, reject) {
    const productDataResponse = [];
    productsData.map(async(productData) => {
      const productPromise = new Promise((resolve, reject) => {
        let productResponse = product
        .findOne({
          attributes: [
            'id',
            'barID',
            'categoryID',
            'subCategoryID',
            'name',
            'description',
            'price',
            'pickupLocationID',
            'status',
            'avatar',
            'serviceType',
            'stock',
            'isStockLimit',
            'dailyStockRenewal',
            'isDailyStockRenewal',
            [
              Sequelize.literal(
                '(select IFNULL(min(product_variants.price), 0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
              ),
              'minPrice'
            ],
            [
              Sequelize.literal(
                '(select IFNULL(max(product_variants.price),0) from product_variants WHERE product_variants.productID = `product`.id AND product_variants.isDeleted="No" AND product_variants.status="Active")'
              ),
              'maxPrice'
            ],
          ],
          include: {
            model: productFoodOptions,
            as: "productFoodOptions",
            attributes: ["id", "productID", "foodOptionID",
              [Sequelize.literal(`(SELECT name FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "name"],
              [Sequelize.literal(`(SELECT initials FROM food_options WHERE id = productFoodOptions.foodOptionID)`), "initials"],
            ],
            include: {
              model: foodOptions,
              as: "foodOptions",
              attributes: [],
            }
          },
          where: { 
            id : productData.id,
          },
          order: [['id', 'DESC'],["productFoodOptions", "foodOptions", "listingOrder", "ASC"]]
        })
        if(productResponse){
          resolve(productResponse)
        }
      })
      productPromise.then((data) => {
        data.dataValues['productExtras'] = productData.productItemExtras ? productData.productItemExtras : []
        data.dataValues['productVariantTypes'] = productData.productVariantTypes ? productData.productVariantTypes : []
        return data
      })
      productDataResponse.push(productPromise)
    });
    Promise.all(productDataResponse)
    .then(async function (finalProductDataList) {
      var resData = {
        categoryID: 0,
        maincategoryID: -2,
        categoryName: 'Order Again',
        categoryProduct: finalProductDataList,
        categoryActive: 1
      }
      resolve({
        key: 'categoryWiseProduct',
        value: resData
      })
    })
  })
}

exports.getProducts = async (req, res) => {
  const userID = res.locals.userID;
  var barID = req.body.barID

  try {
    let serviceType = req.body.serviceType ? req.body.serviceType : null;
    const currentDateTimeUTC = req.body.currentDateTimeUTC ? req.body.currentDateTimeUTC : moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const currentDay = moment(currentDateTimeUTC).isoWeekday() - 1;
    const currentTime = moment(currentDateTimeUTC).format('HH:mm:ss');

    const barData = await bar.findOne({
      attributes: [
        'id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile',
        'stripeID', 'address', 'venueId', 'attachedPosConfig', 'docketCommission', 'posStatus', 'serviceType', 'avatar'
      ],
      include: [
        {
          model: barOpeningHoursUTC,
          attributes: [
            'id',
            'weekDay',
            'barOpeningHoursID',
            'openingHours',
            'closingHours',
            'isClosed'
          ],
          required: true,
          where: { 
            isClosed: '0',
            weekDay: currentDay,
            openingHours: { [Op.lte]: currentTime },
            closingHours: { [Op.gte]: currentTime }
          }
        }
      ],
      where: {
        id: barID
      },
    })
    
    if(!barData) {
      return res.status(200).json({
        success: 0,
        message: 'Venue is currently not active. Please try after sometime.',
        data: []
      });
    }

    if(serviceType && barData.dataValues.serviceType != "BOTH" && serviceType != barData.dataValues.serviceType) {
      return res.status(200).json({
        success: 0,
        message: capitalize(serviceType) +' service is currently not active for this Venue',
        data: []
      });
    }

    if(barData.dataValues.posStatus && barData.dataValues.posStatus === '1'){
      var whereClause = {
        categoryID: '-1',
        isDeleted: 'No'
      }
    }else{
      var whereClause = {
        [Op.not]: [{ categoryID: '-1' }],
        isDeleted: 'No'
      }
    }
    
    let resArray = {}

    subCategory
      .findAll({
        attributes: [
          'id',
          'categoryID',
          'name',
          [
            sequelize.literal(`
              (
                SELECT COALESCE(
                  (SELECT IF(
                    BSH.isClosed = 0 
                    AND BSH.weekDay = ${currentDay} 
                    AND CAST('${currentTime}' AS TIME) BETWEEN CAST(BSH.openingHours AS TIME) 
                    AND CAST(BSH.closingHours AS TIME), 1, 0
                  ) 
                  FROM bar_sub_category_opening_hours_utc AS BSH 
                  WHERE sub_category.id = BSH.subCategoryID 
                  AND BSH.barID = ${barID} 
                  AND BSH.weekDay = ${currentDay} 
                  AND CAST('${currentTime}' AS TIME) BETWEEN CAST(BSH.openingHours AS TIME) AND CAST(BSH.closingHours AS TIME)
                  ORDER BY BSH.id ASC 
                  ), 0
                )
              )
            `),
            'operatingFlag',
          ],
          [
            sequelize.literal("`barSubCategoryWaitTimeUTC`.`waitTime`"),
            "waitTime",
          ],         
        ],
        include: [{
          model: barCategorySequence,
          as: 'bar_category_sequence',
          required: false,
          attributes: [[sequelize.fn('coalesce', sequelize.col('subCategorySequence'), 1000000000000), 'subCategorySequence']],
          where: {barId: barID}
          },
          {
            model: barSubCategoryWaitTimeUTC,
            as: "barSubCategoryWaitTimeUTC",
            required: false,
            attributes: [],
            where: {
              barID: barID,
              weekDay: currentDay,
              startTime: { [Op.lte]: currentTime },
              endTime: { [Op.gte]: currentTime },
            },           
          },
        ],
        order: [
          [sequelize.literal('`operatingFlag`'),'DESC'],
          [Sequelize.literal("`bar_category_sequence.subCategorySequence`")],
          "id"
        ],
        where: {
          ...whereClause,
          [Op.and]: [
            sequelize.literal(`(
            SELECT COUNT(product.id) 
            FROM product 
            WHERE product.subCategoryID = sub_category.id 
              AND product.isDeleted = "No" 
              AND product.status = "Active" 
              AND product.barID = ${barID}
          ) > 0`),
          ],
        },
        
      })
      .then(async (categories) => {
        if (categories.length > 0) {
          var productFetchPromises = []
          var filterLoop = []
          categories.map(cat1 =>  {
            if (cat1.dataValues.operatingFlag == 1) {
              filterLoop.push(cat1.id)
            }
          })

          const orderData = await orders.findOne({
            where: {
              userID: userID,
              barID: barID,
              paymentStatus: 'received',
              refundStatus: 'No',
              orderStatus: 'Pickedup',
            },
            attributes: [
              'id',
              'orderNo',
              'pickupCode',
              [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
              'subTotal',
              'transactionFee',
              'tax',
              'total',
              'orderDate',
              'orderStatus',
              'orderServiceType',
              'paymentType',
              'refundStatus',
              'promocode_id',
              'promocode_amount',
              'promocode_discount',
              'cardType',
              'cardNumber',
              'userID',
              'barID',
              'createdAt',
              'refundedDate'
            ],
            include: [
              {
                required: true,
                attributes: [
                  'id',
                  'orderID',
                  'productID',
                  'price',
                  'quantity',
                  'specialRequest',
                  'isCanceled',
                  'refundAmount',
                  'refundedQuantity',
                  'waitTime',
                  'orderStatus',
                  'PreparingStartTime',
                  'ReadyTime',
                  'PickedupTime',
                  [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
                  [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
                  [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
                ],
                model: orderItems,
                include: [
                  {
                    attributes: [
                      'id',
                      'name',
                      'categoryID',
                      'subCategoryID',
                      'description',
                      'avatar',
                      'posID',
                      'stock',
                      'isStockLimit',
                    ],
                    required:false,
                    model: product,
                    where: {
                      status: 'Active',
                      isDeleted: 'No',
                      subCategoryID: {
                        [Op.in] : filterLoop
                      }
                    },
                    include: [
                      {
                        attributes: [
                          'id',
                          'description',
                          'address'
                        ],
                        model: pickupLocation,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productExtrasID',
                      'price'
                    ],
                    model: orderItemExtras,
                    include: [
                      {
                        attributes: [
                          'id',
                          'extraItem',
                          'posID',
                        ],
                        model: productExtras,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productVariantsID',
                      'price'
                    ],
                    model: orderItemVariants,
                    include: [
                      {
                        attributes: [
                          'id',
                          'variantType'
                        ],
                        model: productVariants,
                      }
                    ]
                  },
                  {
                    attributes: [
                      ['id', "orderProductVariantTypeID"],
                      'orderItemID',
                    ],
                    where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                    model: orderProductVariantTypes,
                    required: false,
                    include: [
                      {
                        attributes: [
                          ['id', "productVariantTypeID"],
                          'label',
                        ],
                        model: productVariantTypes,
                        required: true,
                        include: [
                          {
                            attributes: [
                              ['id', "orderProductVariantSubTypeID"],
                              'orderItemID',
                            ],
                            // where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                            model: orderProductVariantSubTypes,
                            include: [
                              {
                                attributes: [
                                  ['id', "productVariantSubTypeID"],
                                  ['variantType', "extraItem"],
                                  'price',
                                ],
                                model: productVariantSubTypes,
                              }
                            ]
                          }
                        ]
                      }
                    ],
                  }
                ]
              },
            ],
            order: [['id', 'DESC']],
          })

          var orderAgainProductIDs = []
          if(orderData && orderData.orderServiceType == serviceType){
            let incrementalID = 0;
            for (let j = orderData.order_items.length; j >= 0; j--) {
              if(incrementalID < 3){
                const item = orderData.order_items[j];
                var tempProductVariantTypes = []
                var tempProductItemExtras = [];
                if(item && item.product){
                  if(item.product.isStockLimit == 'Yes' && item.product.stock <= 0){
                    continue; 
                  }
                  for (let k = 0; k < item.order_product_variant_types.length; k++) {
                    const variant = orderData.order_items[j].order_product_variant_types[k];
                    let tempVariantTypes = {}
                    tempVariantTypes.id = variant.product_variant_type.dataValues.productVariantTypeID
                    let getProductSubVariant = await orderProductVariantSubTypes.find({
                      attributes: [
                        ['id', "orderProductVariantSubTypeID"],
                        "orderItemID",
                        [
                          Sequelize.literal(
                          '(select variantType from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                          ),
                          'extraItem'
                        ],
                        [
                          Sequelize.literal(
                            '(select price from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                          ),
                          'price'
                        ],
                        [
                          Sequelize.literal(
                          '(select id from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                          ),
                          'productVariantSubTypeID'
                        ],
                      ],
                      where: {
                        orderItemID: variant.orderItemID,
                        productVariantTypeID: variant.product_variant_type.dataValues.productVariantTypeID,
                      }
                    });
                    orderData.order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.productVariantSubTypeID = getProductSubVariant.dataValues.productVariantSubTypeID;
                    orderData.order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.extraItem = getProductSubVariant.dataValues.extraItem;
                    orderData.order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.price = getProductSubVariant.dataValues.price;
                    tempVariantTypes.productVariantSubTypes = [{ id : getProductSubVariant.dataValues.productVariantSubTypeID }]
                    tempProductVariantTypes.push(tempVariantTypes)
                  }
                  if(item.order_item_extras.length > 0){
                    item.order_item_extras.map(itemExtra => {
                      tempProductItemExtras.push({ 'id' : itemExtra.productExtrasID })
                    })
                  }
                  var productOrderData = {
                    id: item.productID,
                    productItemExtras: tempProductItemExtras,
                    productVariantTypes: tempProductVariantTypes
                  }
                  orderAgainProductIDs.push(productOrderData)
                  incrementalID++;
                }
              }
            }
            if(orderAgainProductIDs.length > 0){
              productFetchPromises.push(fetchOrderAgainProductList(barID, orderAgainProductIDs, serviceType))
            }
          }

          if (filterLoop.length == 0) {
            console.log("No Popular")
          } else {
            productFetchPromises.push(fetchPopularProductList(barID, "", filterLoop, serviceType))
          }
          
          categories.forEach(cat => {
            productFetchPromises.push(fetchProductList(cat, barID, "Active", 1, serviceType))
          })

          let cartItemsResponse = await cartItems
          .findAll({
            where: {
              userID: userID
            },
            attributes: [
              'id',
              'barID',
              'productID',
              'quantity',
              'subCategoryID',
              'cartServiceType',
              [Sequelize.literal(`(select price from product where id = cart_items.productID)`), 'productPrice'],
            ]
          })

          Promise.all(productFetchPromises)
          .then(async function (productData) {
              var arryProcessPromises = []
              productData.map(product => {
                if(product.value && product.value.categoryProduct && product.value.categoryProduct.length > 0) {
                  arryProcessPromises.push(product.value)
                }
              })
              resArray.productData = arryProcessPromises
              resArray.cartItemsResponse = cartItemsResponse;
              if(barData.bar_opening_hours_utcs.length > 0){
                const otherOpeingHourRecords = await barOpeningHours.findOne({
                  where: {
                    id: barData.bar_opening_hours_utcs[0].barOpeningHoursID,
                  }
                });
                const sameDayOpeningHourRecords = await barOpeningHours.findAll({
                  where: {
                    weekDay: otherOpeingHourRecords.weekDay,
                    barID: barID,
                  },
                  order: [['openingHours']]
                });
                delete barData.dataValues.bar_opening_hours_utcs;
                barData.dataValues.barOpeningHours = sameDayOpeningHourRecords
              }
              resArray.barData = barData;

              res.status(200).send({
                success: 1,
                data: resArray,
                message: 'success!'
              })
            })
            .catch(function (err) {
              console.log(err);
              res.status(200).json({
                success: 0,
                message: err.message,
              })
            })
        } else {
          // res.status(200).json({
          //   success: 0,
          //   message: 'No Results Found',
          //   data: []
          // })
          
          let cartItemsResponse = await cartItems
            .findAll({
              where: {
                userID: userID
              },
              attributes: [
                'id',
                'barID',
                'productID',
                'quantity',
                'subCategoryID',
                'cartServiceType',
                [Sequelize.literal(`(select price from product where id = cart_items.productID)`), 'productPrice'],
              ]
            })

          resArray.cartItemsResponse = cartItemsResponse;
          resArray.productData = [];
          if(barData.bar_opening_hours_utcs.length > 0){
            const otherOpeingHourRecords = await barOpeningHours.findOne({
              where: {
                id: barData.bar_opening_hours_utcs[0].barOpeningHoursID,
              }
            });
            const sameDayOpeningHourRecords = await barOpeningHours.findAll({
              where: {
                weekDay: otherOpeingHourRecords.weekDay,
                barID: barID,
              },
              order: [['openingHours']]
            });
            delete barData.dataValues.bar_opening_hours_utcs;
            barData.dataValues.barOpeningHours = sameDayOpeningHourRecords
          }
          resArray.barData = barData;

          res.status(200).send({
            success: 1,
            data: resArray,
            message: 'success!'
          })
        }
      }).error(function (err) {
        console.log(err);
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    console.log(error);
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}