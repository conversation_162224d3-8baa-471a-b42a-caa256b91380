const Sequelize = require('sequelize')
const subCategory = require('./subCategory')
const bar = require('./bar')

const bar_category_sequence = sequelize.define(
	'bar_category_sequence',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barId: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "bar",
				key: "id"
			}
		},
		subCategoryId: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "sub_category",
				key: "id"
			}
		},
		subCategorySequence: Sequelize.INTEGER,
	},
	{
		freezeTableName: true,
		timestamps: false
	}
)

bar_category_sequence.belongsTo(subCategory, { foreignKey: 'subCategoryId' })
subCategory.hasMany(bar_category_sequence, { foreignKey: 'subCategoryId', as: 'bar_category_sequence' })

bar_category_sequence.belongsTo(bar, { foreignKey: 'barId' })

module.exports = bar_category_sequence