const Sequelize = require('sequelize');
const user = require("./user");
const bar = require("./bar");
const POSconfig = require("./POSconfig");
const moment = require("moment");

const barPOSMigration = sequelize.define(
	'bar_pos_migration',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barId: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "bar",
				key: "id"
			}
		},
		optedPosId: {
			type: Sequelize.INTEGER,
			allowNull: true,
			references: {
				model: "pos_config",
				key: "id"
			}
		},
		status: Sequelize.ENUM('UN_CONFIGURED', 'INITIATION_PHASE', 'CSV_EXPORT_PHASE', 'DATA_WIPE_PHASE', 'POS_CONFIGURED', 'POS_CONFIGURATION_DECLINED'),
		venuePosConfigDetails: Sequelize.TEXT,
		createdAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false,
		getterMethods: {
			venuePosConfigDetails() {
				return JSON.parse(this.getDataValue('venuePosConfigDetails'))
			}
		},
		setterMethods: {
			venuePosConfigDetails(value) {
				if (typeof value !== 'string') {
					if (value.expires_in) {
						value.expires_at = moment().add(value.expires_in, 's').toDate();
						value.started_at = moment().toDate();
					}
					this.setDataValue('venuePosConfigDetails', JSON.stringify(value));
				} else
					this.setDataValue('venuePosConfigDetails', value);
			}
		}

	}
);

barPOSMigration.belongsTo(POSconfig, {foreignKey: 'optedPosId'})
barPOSMigration.belongsTo(bar, {foreignKey: 'barId'})
bar.belongsTo(barPOSMigration, {foreignKey: 'attachedPosConfig'})

module.exports = barPOSMigration