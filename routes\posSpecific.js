const express = require('express')
const router = express()

const multer = require('multer');
const upload = multer({});

const checkAuth = require("../middleware/barCheckAuth");

const {routeHandlers, webHookHandler} = require("../controller/POS");
const kountaVerifyWebhookAndSetConfigurations = require("../middleware/checkPosSignature");
const product = require("../controller/product");
const category = require("../controller/category");
const POSRouteHandlers = new routeHandlers()
const webHookHandlers = new webHookHandler()

// APP utilised routes
router.post('/listPOS', upload.array(), checkAuth, POSRouteHandlers.listPOS);
router.post('/resetVenueToLastPosState', upload.array(), checkAuth, POSRouteHandlers.resetVenueToLastPosStateRoute);
router.post('/kountaGettingStarted', upload.array(), checkAuth, POSRouteHandlers.kountaGettingStarted);

// POS specific routes

// POS:- Kounta
router.use('/kountaRedirect', POSRouteHandlers.redirectKountaInfo);
// products
router.post('/webhooks/kounta/handle_products', kountaVerifyWebhookAndSetConfigurations, webHookHandlers.handleKountaProductWebhook);
router.post('/webhooks/kounta/remove_product', kountaVerifyWebhookAndSetConfigurations, webHookHandlers.handleProductDeletionWebhook);
// category
router.post('/webhooks/kounta/handle_category', kountaVerifyWebhookAndSetConfigurations, webHookHandlers.handleKountaCategoryWebhook);
router.post('/webhooks/kounta/remove_category', kountaVerifyWebhookAndSetConfigurations, webHookHandlers.handleCategoryDeletionWebhook);
// order
router.post('/webhooks/kounta/update_order_status', kountaVerifyWebhookAndSetConfigurations, webHookHandlers.handleKountaOrderStatusWebhook);
router.post('/webhooks/kounta/update_via_order_status', webHookHandlers.handleKountaOrderStatusWebhook);

module.exports = router