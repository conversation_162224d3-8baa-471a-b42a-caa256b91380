var Sequelize = require('sequelize')

var product_extras = sequelize.define(
  'product_extras',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    productID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product",
        key: "id"
      }
    },
    extraItem: Sequelize.TEXT,
    price: Sequelize.FLOAT,
    posID: Sequelize.STRING,
    productOptionposID: Sequelize.STRING,
    productOptionName: Sequelize.STRING,
    extraSequence: Sequelize.INTEGER,
    status: Sequelize.ENUM('Active', 'Inactive'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = product_extras