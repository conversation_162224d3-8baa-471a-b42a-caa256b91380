var Sequelize = require('sequelize')

var env = require('../config/environment')

var barDocs = sequelize.define(
  'bar_docs',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    name: {
      type: Sequelize.TEXT,
      get() {
        if (this.getDataValue('name') != '') {
          const contentValue =
            env.awsServerURL +
            'bardocuments/' +
            this.getDataValue('name')
          return contentValue
        } else {
          return ''
        }
      }
    },
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = barDocs