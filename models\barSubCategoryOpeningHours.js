const Sequelize = require('sequelize')
const bar = require('./bar')

const barSubCategoryOpeningHours = sequelize.define(
	'bar_sub_category_opening_hours',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "bar",
				key: "id"
			}
		},
		subCategoryID: {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'sub_category',
					key: 'id'
				}
			},
		weekDay: Sequelize.SMALLINT,
		openingHours: Sequelize.TIME,
		closingHours: Sequelize.TIME,
		isClosed: Sequelize.BOOLEAN,
		timeZone: { type: Sequelize.STRING, defaultValue: 'Australia/Perth' },
		createdAt: Sequelize.DATE,
		updatedAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false
	}
)

barSubCategoryOpeningHours.belongsTo(bar, { foreignKey: 'barID' })

module.exports = barSubCategoryOpeningHours