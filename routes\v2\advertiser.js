const express = require('express')
const router = express()
const multer = require('multer')

const advertiser = require('../../controller/v2/advertiser.js')
const checkUserAuth = require('../../middleware/checkAuth')

var upload = multer({})

// router.post('/orderDetail', upload.array(), checkUserAuth, order.orderDetail)
router.post('/advertiserList', upload.array(), checkUserAuth, advertiser.advertiserList)

module.exports = router