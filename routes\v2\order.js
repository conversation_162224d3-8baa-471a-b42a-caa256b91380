const express = require('express')
const router = express()
const multer = require('multer')

const checkBarAuth = require('../../middleware/barCheckAuth')
const order = require('../../controller/v2/order.js')
const orderV1 = require('../../controller/order.js')
const checkUserAuth = require('../../middleware/checkAuth')
const bar = require("../../controller/bar");

var upload = multer({})

router.post('/barOrderListCount', upload.array(), checkBarAuth, order.barOrderListCount)
router.post('/barOrderList', upload.array(), checkBarAuth, order.barOrderList)
router.post('/create', upload.array(), checkUserAuth, bar.isVenueClosedControllerV2, orderV1.cartGetItemsV2, order.create)
router.post('/orderDetail', upload.array(), checkUserAuth, order.orderDetail)
router.post('/barOrderHistoryNewUpdated', upload.array(), checkBarAuth, order.barOrderHistoryNewUpdated)

module.exports = router