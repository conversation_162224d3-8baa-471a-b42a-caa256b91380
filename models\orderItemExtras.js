var Sequelize = require('sequelize')
var orderItems = require('./orderItems')
var productExtras = require('./productExtras')

var order_item_extras = sequelize.define(
  'order_item_extras',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderItemID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orderItems",
        key: "id"
      }
    },
    productExtrasID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "productExtras",
        key: "id"
      }
    },
    price: Sequelize.FLOAT,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

order_item_extras.belongsTo(productExtras, { foreignKey: 'productExtrasID' })

orderItems.hasMany(order_item_extras, { foreignKey: 'orderItemID' })
order_item_extras.belongsTo(orderItems, { foreignKey: 'orderItemID' })

module.exports = order_item_extras