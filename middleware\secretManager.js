// secretManager.js
const AWS = require('aws-sdk');
var env = require('../config/environment')
const { retryWithJitter } = require('./retry');

AWS.config.update({
    region: process.env.awsRegion || 'ap-southeast-2',
    credentials: {
        accessKeyId: process.env.awsAccessKey,
        secretAccessKey: process.env.awsSecretAccessKey
    }
});

async function getSecret() {
    // Configure AWS credentials

    const secretsManager = new AWS.SecretsManager();
    const data = await secretsManager.getSecretValue({ SecretId: env.AWS_SECRET_ID }).promise();
    // console.log('secret data ==',data.SecretString);
    return JSON.parse(data.SecretString);
}

async function fetchSecretWithRetry() {
    return retryWithJitter(
        () => getSecret(),
        {
            retries: 5,
            baseDelay: 200,
            onRetry: (err, attempt, delay) => {
                console.warn(`🔁 Secret fetch retry #${attempt} in ${Math.round(delay)}ms:`, err.message);
            }
        }
    );
}

module.exports = { fetchSecretWithRetry };
