const express = require('express')
var multer = require('multer')
const router = express.Router()

const vender = require('../models/vender')
const checkAuth = require('../middleware/checkAuth')

// --- uploadConfiguration
var AWS = require('aws-sdk')
var bodyParser = require('body-parser')
var multerS3 = require('multer-s3')

AWS.config.update({
  secretAccessKey: env.awsSecretAccessKey,
  accessKeyId: env.awsAccessKey,
  region: env.awsRegion
})

const fileFilter = (req, file, cb) => {
  if (req.body.isLogoUpdated) {
    cb(null, true)
  } else {
    cb(null, false)
  }
}
var s3 = new AWS.S3()
var uploadImage = multer({
  storage: multerS3({
    s3: s3,
    acl: 'public-read',
    contentType: multerS3.AUTO_CONTENT_TYPE,
    bucket: function (req, file, cb) {
      cb(null, env.awsMainBucket + '/vender/brand/' + req.headers.venderid)
    },
    key: function (req, file, cb) {
      cb(null, file.originalname) //new Date().toISOString() +
    }
  }),
  // bug: app couldn't send proper mime detail
  // fileFilter: fileFilter
})

router.put('/', checkAuth, uploadImage.any('brandLogo'), (req, res) => { })

module.exports = router
