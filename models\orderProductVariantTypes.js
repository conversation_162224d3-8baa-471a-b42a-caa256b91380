var Sequelize = require('sequelize')
var orderItems = require('./orderItems')
var productVariantTypes = require('./productVariantTypes')
var orderProductVariantSubTypes = require('./orderProductVariantSubTypes')

var order_product_variant_types = sequelize.define(
  'order_product_variant_types',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderItemID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orderItems",
        key: "id"
      }
    },
    productVariantTypeID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product_variant_types",
        key: "id"
      }
    },
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

order_product_variant_types.belongsTo(productVariantTypes, { foreignKey: 'productVariantTypeID' })
order_product_variant_types.belongsTo(orderItems, { foreignKey: 'orderItemID' })
orderItems.hasMany(order_product_variant_types, { foreignKey: 'orderItemID' })
order_product_variant_types.hasOne(orderProductVariantSubTypes, { foreignKey: 'id' })

module.exports = order_product_variant_types;
