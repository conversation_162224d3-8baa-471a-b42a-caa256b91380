const express = require('express')
const router = express()
const multer = require('multer')

const checkUserAuth = require('../middleware/checkAuth')
const checkBarAuth = require('../middleware/barCheckAuth')
const paymentMethod = require('../controller/paymentMethod')

var upload = multer({})

router.post('/list', upload.array(), checkUserAuth, paymentMethod.list)
router.post('/default-card-set', upload.array(), checkUserAuth, paymentMethod.defaultCardSet)
router.post('/add', upload.array(), checkUserAuth, paymentMethod.add)
router.post('/delete', upload.array(), checkUserAuth, paymentMethod.delete)

router.get('/sentOtp', upload.array(), checkBarAuth, paymentMethod.sentOtp)

router.post('/bankToken', upload.array(), checkBarAuth, paymentMethod.bankToken)
router.post('/banklist', upload.array(), checkBarAuth, paymentMethod.banklist)
router.post('/bankadd', upload.array(), checkBarAuth, paymentMethod.bankadd)
router.post('/bankdelete', upload.array(), checkBarAuth, paymentMethod.bankdelete)

router.get('/complete_connect/:id', upload.array(), paymentMethod.completeConnect)
router.post('/update_default_bank', upload.array(), checkBarAuth, paymentMethod.updateDefaultForCurrency)

module.exports = router