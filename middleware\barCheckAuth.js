const message = require("../config/message")
var env = require('../config/environment')
var jwt = require('jsonwebtoken');
const barAccessToken = require('../models/barAccessToken')

module.exports = (req, res, next) => {
  const token = req.headers.accesstoken;
  if (token == null || token == '') {
    res.status(401).send({
      status: 0,
      message: message.bar.sessionExpired
    })
  } else {
    jwt.verify(token, env.ACCESS_TOKEN_SECRET, async (err, bar) => {
      if (err) {
        res.status(401).send({
          status: 0,
          message: message.bar.sessionExpired
        })
      } else {
        if (bar.userType == 'bar') {
          res.locals.barID = bar.barID
          let getBarAuthDetails = await barAccessToken.findOne({
            where: {
              barID: bar.barID,
              accessToken: token,
            },
          });
          if(!getBarAuthDetails) {
            return res.status(401).send({
              status: 0,
              message: message.user.sessionExpired
            })
          } 
          next()
        } else {
          return res.status(401).send({
            status: 0,
            message: message.bar.sessionExpired
          })
        }
      }
    })
  }
}