// db.js
const { Sequelize } = require('sequelize');
const { retryWithJitter } = require('../middleware/retry');

const db = {};

function createSequelize(config) {
    const {
        host: dbHost,
        dbname: db<PERSON><PERSON>,
        username: db<PERSON><PERSON>,
        password: dbPassword
    } = config

    return new Sequelize(
        dbName,
        dbUser,
        dbPassword,
        {
            host: dbHost,
            port: 3306,
            dialect: 'mysql',
            dialectOptions: {
                dateStrings: false,
                typeCast: true
            },
            pool: {
                max: 5,
                min: 0,
                acquire: 30000,
                idle: 10000
            },
            retry: {
                match: [
                    Sequelize.ConnectionError,
                    Sequelize.ConnectionTimedOutError,
                    Sequelize.TimeoutError,
                    /Deadlock/i,
                    'SQLITE_BUSY'
                ],
                max: 5
            }
        }
    );
}

async function testConnection(sequelize) {
    await sequelize.authenticate();
    console.log('✅ Database connected');

    global.sequelize = sequelize;
    db.sequelize = sequelize;
    db.Sequelize = sequelize;
}

async function initDatabase(config) {
    const sequelize = createSequelize(config);

    return retryWithJitter(
        () => testConnection(sequelize),
        {
            retries: 5,
            baseDelay: 300,
            onRetry: (err, attempt, delay) => {
                console.warn(`🔁 DB retry #${attempt} in ${Math.round(delay)}ms:`, err.message);
            }
        }
    );
    return db;
}

module.exports = {
    initDatabase,
    db
};
