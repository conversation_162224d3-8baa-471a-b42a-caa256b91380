const Sequelize = require('sequelize');
const bar = require('./bar');
const subCategory = require('./subCategory');

const pos_conf = sequelize.define(
	'pos_conf',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		posName: Sequelize.STRING,
		posConf: {
			type: Sequelize.TEXT
		},
		createdAt: Sequelize.DATE,
		updatedAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false,
		getterMethods: {
			posConf() {
				return JSON.parse(this.getDataValue('posConf'))
			}
		},

		setterMethods: {
			posConf(value) {
				this.setDataValue('posConf', JSON.stringify(value));
			}
		}
	}
);

bar.belongsTo(pos_conf, {foreignKey: 'attachedPosConfig'})
subCategory.belongsTo(pos_conf, {foreignKey: 'fromPosId'})

module.exports = pos_conf