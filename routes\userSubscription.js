const express = require('express')
const router = express()
const multer = require('multer')

const checkAuth = require('../middleware/barCheckAuth')
const userSubscription = require('../controller/userSubscription')

var upload = multer({})

router.post('/add', upload.array(), checkAuth, userSubscription.add)
router.get('/receipt-validate', checkAuth, userSubscription.validate)

module.exports = router