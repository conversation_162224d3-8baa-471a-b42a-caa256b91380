const Sequelize = require('sequelize')


const productTax = sequelize.define(
  'product_tax',
  {
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		name: Sequelize.STRING,
		value: Sequelize.FLOAT,
		createdAt: Sequelize.DATE,
		updatedAt:Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: true,
		underscored:false
  }
)

module.exports = productTax