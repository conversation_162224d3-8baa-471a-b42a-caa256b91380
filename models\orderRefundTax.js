// 1. & 2. New Taxes Page under Settings.... Starts
var Sequelize = require('sequelize')
const orders = require('./orders')

var orderRefundTax = sequelize.define(
  'order_refund_tax',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "order",
        key: "id"
      }
    },
    taxID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "tax",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    amount: Sequelize.FLOAT,
    name: Sequelize.TEXT,
    percentage: Sequelize.FLOAT,
    createdAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

orderRefundTax.belongsTo(orders, { foreignKey: 'orderID' })
orders.hasMany(orderRefundTax, { foreignKey: 'orderID' })

module.exports = orderRefundTax

// 1. & 2. New Taxes Page under Settings....  Ends