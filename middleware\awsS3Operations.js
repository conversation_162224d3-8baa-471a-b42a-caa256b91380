require('dotenv').config();

// Used to interact with AWS Service
const AWS = require('aws-sdk');
const fs = require('fs');
const request = require('request');
var env = require('../config/environment')

AWS.config.update({
	secretAccessKey: env.awsSecretAccessKey,
	accessKeyId: env.awsAccessKey,
	region: env.awsRegion
});

const s3PrivateBucket = new AWS.S3({
	params: {
		Bucket: env.awsBucket
	}
});

const s3PublicBucket = new AWS.S3({
	params: {
		Bucket: env.awsPublicBucket
	}
});

// To Upload image media on S3
function s3UploadFile(files, path) {
	return new Promise((resolve, reject) => {
		try {
			fs.readFile(files.path, (err, data) => {
				if (err) throw err;
				const params = {
					Bucket: env.awsBucket,
					Key: path,
					// Body: files,
					ContentType: files.mimetype,
					Body: data
					// ACL: 'public-read'
				};

				s3PrivateBucket.upload(params, function (err1, rese) {
					if (err1) {
						console.log('🚀 ~ file: multerAwsOperations.js ~ line 36 ~ err1', err1);
						throw err1;
					}
					resolve(rese.Location);
				});
			});
		} catch (e) {
			console.log('functions3Upload -> e', e);
			reject({ message: 'Could not upload image', err: e });
		}
	});
}

// To Upload image media on S3
function s3PublicUploadFile(files, path) {
	return new Promise((resolve, reject) => {
		try {
			fs.readFile(files.path, (err, data) => {
				if (err) throw err;
				const params = {
					Bucket: env.awsPublicBucket,
					Key: path,
					// Body: files,
					ContentType: files.mimetype,
					Body: data
					// ACL: 'public-read'
				};

				s3PublicBucket.upload(params, function (err1, rese) {
					if (err1) {
						console.log('🚀 ~ file: s3PublicUploadFile.js ~ line 74 ~ err1', err1);
						throw err1;
					}
					resolve(rese.Location);
				});
			});
		} catch (e) {
			console.log('functions3Upload -> e', e);
			reject({ message: 'Could not upload image', err: e });
		}
	});
}

// To Upload image media on S3
function s3UploadDoshiiFile(file, path) {
	return new Promise((resolve, reject) => {

		const options = {
			uri: file,
			encoding: null
		};
			
		request(options, async function(error, response, body) {
			if (error || response.statusCode !== 200) { 
				console.log("failed to get image");
				console.log(error);
				reject({ message: 'Could not upload image', err: e });
			} else {
				s3PrivateBucket.putObject({
					Body: body,
					Key: path,
					Bucket: env.awsBucket
				}, function(error, data) { 
					if (error) {
						console.log("error downloading image to s3");
						reject({ message: 'Could not upload image', err: e });
					} else {
						resolve({ message: 'image uploaded to s3' });
					}
				}); 
			}   
		});
	});
}

// To Get image media on S3
function s3GetFile(path) {
	return new Promise((resolve, reject) => {
		try {
			const url = s3PrivateBucket.getSignedUrl('getObject', {
				Bucket: env.awsBucket,
				Key: path,
				Expires: env.awsSignedUrlExpireTime * 60 // time in seconds: e.g. 60 * 5 = 5 mins
			});
			resolve(url);
		} catch (e) {
			console.log('functionS3Get -> e', e);
			reject({ message: 'Could not get file', err: e });
		}
	});
}

module.exports = { s3UploadFile, s3PublicUploadFile, s3UploadDoshiiFile, s3GetFile };
