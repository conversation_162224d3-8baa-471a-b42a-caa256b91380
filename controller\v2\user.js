var Sequelize = require('sequelize')
var env = require('../../config/environment')

const moment = require('moment')
const bar = require('../../models/bar')
const cartItems = require('../../models/cartItems')
const userFavVenue = require('../../models/userFavVenue')
const Op = Sequelize.Op
const commonFunction = require('../../common/commonFunction')
const sequelize = require('sequelize')
const barOpeningHoursUTC = require('../../models/barOpeningHoursUTC')

exports.favVenueList = async (req, res) => {
  try {
    const userID = res.locals.userID;
    var { search, latitude, longitude } = req.body;

    let selectedServiceType = req.body.serviceType ? req.body.serviceType : 'BOTH';

    let page = req.body.page ? req.body.page : 1;
    let per_page = req.body.per_page ? parseInt(req.body.per_page) : 10;

    if(req.body.show_all && req.body.show_all == '1'){
      per_page = ***************;
    }
    let offset = (page - 1) * per_page;
    let limit = per_page;

    if (latitude == '' || longitude == '') {
      latitude = env.latitude
      longitude = env.longitude
    }

    var whereClause = []
    whereClause.push({
      isDeleted: 'No',
      status: 'Active',
      accountVerified: 'Approved',
      restaurantName: {
        [Op.like]: '%' + search + '%'
      },
      [Op.and]: [{ stripeID: { [Op.ne]: null } }, { stripeID: { [Op.ne]: '' } }],
    });

    if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
			whereClause.push({
				[Op.or] : [{ serviceType: selectedServiceType }, { serviceType: 'BOTH' }]
			});
		}

    const currentDateTimeUTC = req.body.currentDateTimeUTC ? req.body.currentDateTimeUTC : moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const currentDay = moment(currentDateTimeUTC).isoWeekday() - 1;
    const currentTime = moment(currentDateTimeUTC).format('HH:mm:ss');

    let list = await bar.findAndCountAll({
      where: whereClause,
      attributes: [
        'id',
        'restaurantName',
        'managerName',
        'email',
        'countryCode',
        'mobile',
        'address',
        'latitude',
        'longitude',
        'serviceType',
        'avatar',
        'isVenueServeAlcohol',
        'liquorLicenseNumber',
        [
          sequelize.literal(`
            CASE 
              WHEN EXISTS (
                SELECT 1
                FROM bar_opening_hours_utc AS OP
                WHERE OP.barID = bar.id
                  AND OP.weekDay = ${currentDay}
                  AND OP.isClosed = 0
                  AND '${currentTime}' BETWEEN OP.openingHours AND OP.closingHours
              )
              THEN 1
              ELSE 0
            END
          `),
          'operatingFlag'
        ],
        [sequelize.literal("coalesce(waitTimeDrink, 0)"), "waitTimeDrink"],
        [sequelize.literal("coalesce(waitTimeFood, 0)"), "waitTimeFood"],
        [sequelize.literal("ROUND(" + env.DISVAL + " * acos(cos(radians(" + latitude + ")) * cos(radians(latitude)) * cos(radians(longitude) - radians(" + longitude + ")) + sin(radians(" + latitude + ")) * sin(radians(latitude))), 2)"), 'distance'],
        [sequelize.literal("'" + env.DISTEXT + "'"), 'distance_ext'],
        [sequelize.literal("(SELECT COUNT(id) from orders WHERE orders.paymentStatus = 'received' AND orders.barID=bar.id AND orders.isDeleted='No')"), "totalOrders"]
      ],
      include: [
        {
          model: userFavVenue,
          attributes: ["id", "userID", "barID"],
          where: { userID: userID },
          distinct: true,
          required:true
        },
        {
          model: barOpeningHoursUTC,
          attributes: [
            'id',
            'weekDay',
            'barOpeningHoursID',
            'openingHours',
            'closingHours',
            'isClosed'
          ],
          required: false,
          where: { 
            isClosed: 0,
            weekDay: currentDay,
            openingHours: { [Op.lte]: currentTime },
            closingHours: { [Op.gte]: currentTime }
          }
        }
      ],
      distinct: true,
      order: [sequelize.literal('operatingFlag DESC'),sequelize.literal('distance ASC')],
      offset: offset,
      limit: limit
    })
    if (list.count > 0) {
      list.rows.forEach(async (element, index) => {
        const data = list.rows[index].dataValues;
        const barHours = data.bar_opening_hours_utcs[0].dataValues;
        const closingTime = barHours.closingHours;
        if (data.operatingFlag == '1') {
          if (closingTime == '23:59:59') {
            const otherRecord = await barOpeningHoursUTC.findOne({
              where: {
                barOpeningHoursID: barHours.barOpeningHoursID,
                [Op.not]: { id: barHours.id, weekDay: currentDay }
              }
            });
            data.isOpen = otherRecord ? '1' : moment(closingTime, 'HH:mm:ss').diff(moment(currentTime, 'HH:mm:ss'), 'minutes') < 30 ? '2' : '1';
          } else {
            data.isOpen = moment(closingTime, 'HH:mm:ss').diff(moment(currentTime, 'HH:mm:ss'), 'minutes') < 30 ? '2' : '1';
          }
        } else {
          data.isOpen = '0';
        }
        delete list.rows[index].dataValues.bar_opening_hours_utcs;
      });
    }
    let resArray = {}
    const cartData = await cartItems.findOne({
      where: {
        userID: userID
      },
      include: [
        {
          model: bar,
          attributes: [
            'id',
            'restaurantName',
            'avatar',
          ]
        }
      ]
    })

    resArray.barlist = list.rows
    resArray.count = list.count
    resArray.cartData = {};
    resArray.cartData.cartItemAvailable = (cartData) ? 'Yes' : 'No'
    resArray.cartData.restaurantName = (cartData) ? cartData.bar.restaurantName : ''
    resArray.cartData.barID = (cartData) ? cartData.bar.id : ''
    resArray.cartData.cartServiceType = (cartData) ? cartData.cartServiceType : ''
    resArray.cartData.avatar = (cartData) ? cartData.bar.avatar : ''
    res.status(200).json({
      success: list.count > 0 ? 1 : 0,
      message: list.count > 0 ? "Results Found" : "No Results Found.",
      data: resArray
    })
  } catch (error) {
    console.log(error);
    res.status(500).json({
      success: 0,
      message: error.message
    })
  }
}