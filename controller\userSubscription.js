const userSubscription = require('../models/userSubscription');
const bar = require('../models/bar')

const env = require('../config/environment')
const axios = require('axios');
const moment = require('moment');
const {sendEmail} = require("./common");

exports.add = async (req, res, next) => {
  try {

    const userSubscriptionData = await userSubscription.findOne({
        where: {
          bar_id: res.locals.barID
        }
    });
    let userSubscriptionDetail = null;
    let message = null;
    
    if(!userSubscriptionData) {
      const subascriptionData = {
        product_id: req.body.productId,
        trial_end: req.body.trialEnd,
        current_period_end: req.body.currentPeriodEnd,
        bar_id: res.locals.barID,
        subscription_id: req.body.subscriptionId,
        receipt: req.body.receipt,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      userSubscriptionDetail = await userSubscription.create(subascriptionData);
      message="subscription created successfully";
    }else {
      let subascriptionData = {};
      if(req.body.productId) {
        subascriptionData.roduct_id = req.body.productId;
      }
      if(req.body.trialEnd) {
        subascriptionData.trial_end = req.body.trialEnd;
      }
      if(req.body.subscriptionId) {
        subascriptionData.subscription_id = req.body.subscriptionId;
      }

      if(req.body.receipt) {
        subascriptionData.receipt = req.body.receipt;
      }
      await userSubscription.update(subascriptionData,{
        where: {
          id: userSubscriptionData.id  
        }
      });
      userSubscriptionDetail =  userSubscriptionData;
      message="subscription updated successfully";
    }
    const barDetail = await bar.findOne({where: {id: res.locals.barID}});

    let mailOptions = {
      from: `MyTab <${env.fromEmailAdmin}>`,
      to: barDetail.email, // '<EMAIL>'
      subject: "MyTab Premium+ Docket Printing feature",
      html: `
  <html lang="en">
    <body>
      <p>Hello ${barDetail.restaurantName},</p>
      <p>Thank you for activating the MyTab Premium+ Docket Printing feature for ${barDetail.restaurantName}.</p>
      <p>You can now connect your Venue's docket printer/s to your MyTab Venue account for seamless docket printing integration on accepted EPSON docket printers. Please check our FAQ page in your venue app for the list of compatible docket printers or alternatively, <NAME_EMAIL> for further support.</p>
      <p>This MyTab Premium+ docket printing feature is a $9.99 per week subscription fee will be automatically deducted from your account.</p>
      <p>If you have any questions in regards to this feature, please visit our FAQ page on your Venue app account or email <NAME_EMAIL>.</p>
      <p>If you need any assistance, please email us at ${env.barEmailTo}</p>
      <br>
      <p>Thank you!</p>
      <p>MyTab Venue Support Team</p>
      <p>My Venue | My Way | MyTab</p>
      <p>www.MyTabinfo.com</p>
      <img src="cid:mytab@logo" alt="MyTab Logo" height="150" width="150"/>
    </body>
  </html>`,
      attachments: [{
        filename: 'logo.png',
        path: './static/mailLogo.png',
        cid: 'mytab@logo' //same cid value as in the html img src
      }]
    }
    await sendEmail(mailOptions);
    res.status(200).send({
      success: 1,
      data: userSubscriptionDetail,
      message: message
    })       
  } catch (error) {
    console.log("error", error);
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.validate = async (req, res, next) => {
  try {
    const userSubscriptionData = await userSubscription.findOne({
        where: {
          bar_id: res.locals.barID
        }
    });
    
    if(userSubscriptionData) {
    let config = {
      method: 'post',
      url: process.env.appleVerifyURL,
      headers: { 
        'Content-Type': 'application/json'
      },
      data : {
        "receipt-data": userSubscriptionData.receipt,
        "password": "d6c953755d9f4112911e108721a26f8c",
        "exclude-old-transactions": true
      }
    };

    axios(config)
    .then(async response => {
      if(response.data && response.data.receipt) {
        if(response.data.latest_receipt_info) {

          let receiptInfo = response.data.latest_receipt_info[0];
          let expiresDate = receiptInfo.expires_date_ms/1000;
          userSubscriptionData.current_period_end = expiresDate;
          userSubscriptionData.transaction_id  = receiptInfo.transaction_id;
          let currentDate = moment().unix();
          console.log(expiresDate, currentDate);
          if(expiresDate > currentDate) {
            response.data.subscription_valid = true;
            // if(userSubscriptionData.status != response.data.subscription_valid) {
            //   userSubscriptionData.status  = 1;
            //   const barDetail = await bar.findOne({where: {id: res.locals.barID}});
              
            //   let mailOptions = {
            //     from: `MyTab <${env.fromEmailAdmin}>`,
            //     to: barDetail.email, // '<EMAIL>'
            //     subject: "MyTab Premium+ Docket Printing feature",
            //     html: `
            // <html lang="en">
            //   <body>
            //     <p>Hello ${barDetail.restaurantName},</p>
            //     <p>Thank you for activating the MyTab Premium+ Docket Printing feature for ${barDetail.restaurantName}.</p>
            //     <p>You can now connect your Venue's docket printer/s to your MyTab Venue account for seamless docket printing integration on accepted EPSON docket printers. Please check our FAQ page in your venue app for the list of compatible docket printers or alternatively, <NAME_EMAIL> for further support.</p>
            //     <p>This MyTab Premium+ docket printing feature is a $9.99 per week subscription fee will be automatically deducted from your account.</p>
            //     <p>If you have any questions in regards to this feature, please visit our FAQ page on your Venue app account or email <NAME_EMAIL>.</p>
            //     <p>If you need any assistance, please email us at ${env.barEmailTo}</p>
            //     <br>
            //     <p>Thank you!</p>
            //     <p>MyTab Venue Support Team</p>
            //     <p>My Venue | My Way | MyTab</p>
            //     <p>www.MyTabinfo.com</p>
            //     <img src="cid:mytab@logo" alt="MyTab Logo" height="150" width="150"/>
            //   </body>
            // </html>`,
            //     attachments: [{
            //       filename: 'logo.png',
            //       path: './static/mailLogo.png',
            //       cid: 'mytab@logo' //same cid value as in the html img src
            //     }]
            //   }
            //   await sendEmail(mailOptions);
            // }
          } else {
            response.data.subscription_valid = false;
            userSubscriptionData.status  = 0;
          }
          userSubscriptionData.save();
        }
        res.status(200).send({
          success: 1,
          data: response.data,
          message: 'receipt validate successfully'
        });  
      } else {
        res.status(200).send({
          success: 0,
          //data: response.data,
          message: 'invalidate receipt'
        });
      }
    })
    .catch(function (error) {
      res.status(200).send({
        success: 0,
        message: error
      })
    });
    } else {
      res.status(200).send({
        success: 0,
        message: 'subscription data not found!'
      })
    }
       
  } catch (error) {
    console.log("error", error);
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}