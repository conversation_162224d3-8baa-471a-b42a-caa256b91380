// 1. & 2. New Taxes Page under Settings.... Starts
var Sequelize = require('sequelize')
const orders = require('./orders')

var orderTax = sequelize.define(
  'order_tax',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "order",
        key: "id"
      }
    },
    taxID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "tax",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    amount: Sequelize.FLOAT,
    name: Sequelize.TEXT,
    percentage: Sequelize.FLOAT,
    createdAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

orderTax.belongsTo(orders, { foreignKey: 'orderID' })
orders.hasMany(orderTax, { foreignKey: 'orderID' })

module.exports = orderTax

// 1. & 2. New Taxes Page under Settings....  Ends