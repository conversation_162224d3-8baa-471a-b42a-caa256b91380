var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')
var advertiser_users = require('./advertiser_users')
var coupons = require('./coupons')

var env = require('../config/environment')

var advertiser = sequelize.define(
  'ads',
  {
    id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    
    campaign_id: {
        type: Sequelize.INTEGER,
        allowNull: false
    },
    bar_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
            model: 'bar',
            key: 'id'
        }
    },
    objective: {
        type: Sequelize.ENUM('CPM', 'CPC', 'CPA'),
        allowNull: true
    },
    ad_title: {
        type: Sequelize.STRING(255),
        allowNull: true
    },
    ad_description: {
        type: Sequelize.TEXT,
        allowNull: true
    },
    state: {
        type: Sequelize.TEXT,
        allowNull: true
    },
    city: {
        type: Sequelize.TEXT,
        allowNull: true
    },
    suburb: {
        type: Sequelize.TEXT,
        allowNull: true
    },
    call_to_action_url: {
        type: Sequelize.TEXT,
        allowNull: true
    },
    media_url: {
        type: Sequelize.STRING(500),
        get() {
            if (
                this.getDataValue('media_url') != '' &&
                this.getDataValue('media_url') != null
            ) {
                return (
                    env.awsPrivateBucketCloudFrontURL + 
                    env.awsAdsFolder +
                    this.getDataValue('media_url')
                );
            } else {
                return '';
            }
        },
        defaultValue: null
    },
    call_to_action: {
        type: Sequelize.ENUM('learn_more', 'order_now', 'buy_tickets', 'view_menu', 'visit_website'),
        allowNull: true
    },
    eligibility_type: {
        type: Sequelize.ENUM('all_mytab_customers', 'mytab_customer_segments'),
        allowNull: true
    },
    created_by_type: {
        type: Sequelize.ENUM('venue', 'advertiser', 'cms'),
        allowNull: true
    },
    start_date: {
        type: Sequelize.DATE,
        allowNull: true
    },
    end_date: {
        type: Sequelize.DATE,
        allowNull: true
    },
    start_time: {
        type: Sequelize.TIME,
        allowNull: true
    },
    end_time: {
        type: Sequelize.TIME,
        allowNull: true
    },
    daily_budget: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
    },
    ad_status: {
        type: Sequelize.ENUM('New', 'Approved', 'Rejected'),
        allowNull: false,
        defaultValue: 'New'
    },
    pause_status: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false
    },
    stripe_payment_id: {
        type: Sequelize.STRING(255),
        allowNull: true
    },
    stripe_customer_id: {
        type: Sequelize.STRING(255),
        allowNull: true
    },
    stripe_card_id: {
        type: Sequelize.STRING(255),
        allowNull: true
    },
    payment_method_type: {
        type: Sequelize.ENUM('card', 'bank_account', 'other'),
        allowNull: true
    },
    card_last4: {
        type: Sequelize.STRING(4),
        allowNull: true
    },
    card_brand: {
        type: Sequelize.STRING(50),
        allowNull: true
    },
    payment_status: {
        type: Sequelize.ENUM('pending', 'paid', 'failed', 'refunded'),
        allowNull: true,
        defaultValue: 'pending'
    },
    created_by_id: {
        type: Sequelize.INTEGER,
        allowNull: true
    },
    created_at: {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: Sequelize.NOW
    },
    updated_at: {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: Sequelize.NOW
    },
    deleted_at: {
        type: Sequelize.DATE,
        allowNull: true
    },
    save_card: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
    }
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

advertiser.belongsTo(bar, {foreignKey: 'bar_id'});
advertiser.belongsTo(advertiser_users, {foreignKey: 'adsID',as: 'advertiser_users' });

// advertiser.belongsTo(user, { foreignKey: 'userID' })
// advertiser.belongsTo(bar, { foreignKey: 'barID' })
// advertiser.belongsTo(coupons, { foreignKey: 'promocode_id' })
// bar.hasMany(advertiser, { foreignKey: 'barID' })

module.exports = advertiser