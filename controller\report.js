var Sequelize = require('sequelize')
var multer = require('multer')

const message = require('../config/message')
const common = require('./common')
const bar = require('../models/bar')
const order = require('../models/orders')
const Op = Sequelize.Op
const sO = require('../config/database')
const { QueryTypes } = require('sequelize');

var env = require('../config/environment')
var jwt = require('jsonwebtoken');
const moment = require('moment')

exports.report = async (req, res) => {
  let groupingClause = 'orderFormate'
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    const today = moment(new Date()).format('YYYY-MM-DD')
    const dashboardCount = {}

    var totalEarningWhereClause = {
      isDeleted: 'No',
      barID: barID,
      paymentStatus: 'received',
      refundStatus: 'No'
    }

    let totalEarning = await order.findOne({
      attributes: [
        [sequelize.literal("round(sum(total - (total * 2.9/ 100 + 0.31) - (total * 0.25 / 100 + 0.25) - transactionFee),2)"), 'amount'],
        //  remove the case if needed: this was added to cover the 2 day anomaly that happened due to 2.9% stripe fee + payout fee incorporation after second fee change was completed
      ],
      group: ['barID'],
      where: totalEarningWhereClause
    })

    var todayEarningWhereClause = {
      isDeleted: 'No',
      barID: barID,
      paymentStatus: 'received',
      refundStatus: 'No'
    }

    let todayEarning = await order.findOne({
      attributes: [
        [
          Sequelize.fn('date_format', Sequelize.col('orderDate'), '%Y-%m-%d'),
          'oDate'
        ],
        [sequelize.literal("round(sum(total - (total * 2.9/ 100 + 0.31) - (total * 0.25 / 100 + 0.25) - transactionFee),2)"), 'amount'],
        //  remove the case if needed: this was added to cover the 2 day anomaly that happened due to 2.9% stripe fee + payout fee incorporation after second fee change was completed
      ],
      group: ['oDate'],
      where: todayEarningWhereClause,
      having: {
        oDate: {
          $eq: sequelize.literal("'" + today + "'")
        }
      }
    })

    var orderWhereClause = {
      isDeleted: 'No',
      barID: barID,
      paymentStatus: 'received',
      refundStatus: 'No'
    }

    const orderCount = await order.findAll({
      attributes: [
        'id'
      ],
      where: orderWhereClause
    })

    if (totalEarning) {
      totalEarning = totalEarning.toJSON();
    }

    var earningReportWhereClause = {
      isDeleted: 'No',
      barID: barID,
      paymentStatus: 'received',
      refundStatus: 'No'
    }

    let earningReport = await order.findAll({
      attributes: [
        [
          Sequelize.fn('date_format', Sequelize.fn('ADDDATE', Sequelize.col('orderDate'), Sequelize.literal('INTERVAL 6 MONTH')), '%Y'),
          'orderFormate'
        ],

        // [sequelize.literal("round(sum(total - ((total * (CASE WHEN `orderDate` < DATE('2021-06-04') then 2.9 else 1.75 end) / 100) + 0.3) - (IF((`orderDate` < DATE('2021-06-25')), ((total * 0.25 / 100) + 0.25), 0)) - transactionFee), 2)"), 'amount'],
        [sequelize.literal("round(sum(total - (total * 2.9/ 100 + 0.31) - (total * 0.25 / 100 + 0.25) - transactionFee),2)"), 'amount'],
       //  remove the case if needed: this was added to cover the 2 day anomaly that happened due to 2.9% stripe fee + payout fee incorporation after second fee change was completed
      ],
      group: [groupingClause],
      where: earningReportWhereClause
    })

    var orderReportWhereClause = {
      isDeleted: 'No',
      barID: barID,
      paymentStatus: 'received',
      refundStatus: 'No'
    }

    let orderReport = await order.findAll({
      attributes: [
        [
          Sequelize.fn('date_format', Sequelize.fn('ADDDATE', Sequelize.col('orderDate'), Sequelize.literal('INTERVAL 6 MONTH')), '%Y'),
          'orderFormate'
        ],

        [sequelize.fn('count', sequelize.col('id')), 'totalOrder'],
      ],
      group: [groupingClause],
      where: orderReportWhereClause
    })

    var processingReportWhereClause = {
      isDeleted: 'No',
      barID: barID,
      paymentStatus: 'received',
      refundStatus: 'No',
      orderStatus: {
        $ne: 'New'
      }
    }

    let processingReport = await order.findAll({
      attributes: [
        [
          Sequelize.fn('date_format', Sequelize.fn('ADDDATE', Sequelize.col('orderDate'), Sequelize.literal('INTERVAL 6 MONTH')), '%Y'),
          'orderFormate'
        ],

        [sequelize.fn('sum', sequelize.fn('TIMESTAMPDIFF', sequelize.literal('MINUTE'), sequelize.col('createdAt'), sequelize.col('ReadyTime'))), 'times']
      ],
      group: [groupingClause],
      where: processingReportWhereClause
    })

    dashboardCount.totalEarnings = (totalEarning && totalEarning.amount) ? totalEarning.amount : 0
    dashboardCount.todayEarnings = (todayEarning && todayEarning.dataValues.amount) ? todayEarning.dataValues.amount : 0
    dashboardCount.totalOrders = orderCount.length
    dashboardCount.earningReport = {}
    dashboardCount.earningReport.reportType = 'Yearly'
    dashboardCount.earningReport.earningReport = earningReport
    dashboardCount.orderReport = {}
    dashboardCount.orderReport.reportType = 'Yearly'
    dashboardCount.orderReport.orderReport = orderReport
    dashboardCount.processingReport = {}
    dashboardCount.processingReport.reportType = 'Yearly'
    dashboardCount.processingReport.processingReport = processingReport

    res.status(200).send({
      success: 1,
      message: 'Success!',
      data: dashboardCount
    })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.earningReport = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    
    let earningReport
    if (req.body.reportType == 'Yearly') {
      earningReport = await sO.query(`
      SELECT date_format(orderDate, '%Y') AS orderByFormate, date_format(ADDDATE(orderDate, INTERVAL 6 MONTH), '%Y') AS orderFormate, date_format(orderDate, '%Y') AS yearFormat, round(sum(total - (total * 2.9/ 100 + 0.31) - (total * 0.25 / 100 + 0.25) - transactionFee),2) AS amount FROM orders AS orders WHERE orders.isDeleted = 'No' AND orders.barID = ${barID} AND orders.paymentStatus = 'received' AND orders.refundStatus = 'No' GROUP BY orderByFormate HAVING 1=1 ORDER BY orderFormate ASC
        `,
        { type: QueryTypes.SELECT }
      )
    } else if (req.body.reportType == 'Monthly') {
      earningReport = await sO.query(`
      SELECT date_format(orderDate, '%c') AS orderByFormate, date_format(orderDate, '%b') AS orderFormate, date_format(orderDate, '%Y') AS yearFormat, round(sum(total - (total * 2.9/ 100 + 0.31) - (total * 0.25 / 100 + 0.25) - transactionFee),2) AS amount FROM orders AS orders WHERE date_format(orderDate, '%Y') = year(now()) AND orders.isDeleted = 'No' AND orders.barID = ${barID} AND orders.paymentStatus = 'received' AND orders.refundStatus = 'No' GROUP BY orderByFormate ORDER BY orderByFormate ASC
        `,
        { type: QueryTypes.SELECT }
      )
    } else if (req.body.reportType == 'Weekly') {
      earningReport = await sO.query(`
      SELECT weekday(orderDate) AS orderByFormate, date_format(orderDate, '%a') AS orderFormate, date_format(orderDate, '%V') AS weekFormat, round(sum(total - (total * 2.9/ 100 + 0.31) - (total * 0.25 / 100 + 0.25) - transactionFee),2) AS amount FROM orders AS orders WHERE date_format(orderDate, '%Y') = year(now()) AND orders.isDeleted = 'No' AND orders.barID = ${barID} AND orders.paymentStatus = 'received' AND orders.refundStatus = 'No' GROUP BY orderDate HAVING (weekFormat = week(now())) ORDER BY orderByFormate ASC
        `,
        { type: QueryTypes.SELECT }
      )
    }
    const dashboardCount = {}
    dashboardCount.reportType = req.body.reportType
    dashboardCount.earningReport = earningReport

    res.status(200).send({
      success: 1,
      message: 'Success!',
      data: dashboardCount
    })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.orderReport = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    
    let orderReport
    if (req.body.reportType == 'Yearly') {
      orderReport = await sO.query(`
        SELECT date_format(orderDate, '%Y') AS orderByFormate, date_format(ADDDATE(orderDate, INTERVAL 6 MONTH), '%Y') AS orderFormate, date_format(orderDate, '%Y') AS yearFormat, count(id) AS totalOrder FROM orders AS orders WHERE orders.isDeleted = 'No' AND orders.barID = ${barID} AND orders.paymentStatus = 'received' AND orders.refundStatus = 'No' GROUP BY orderByFormate HAVING 1=1 ORDER BY orderFormate ASC
        `,
        { type: QueryTypes.SELECT }
      )
    } else if (req.body.reportType == 'Monthly') {
      orderReport = await sO.query(`
        SELECT date_format(orderDate, '%c') AS orderByFormate, date_format(orderDate, '%b') AS orderFormate, date_format(orderDate, '%Y') AS yearFormat, count(id) AS totalOrder FROM orders AS orders WHERE date_format(orderDate, '%Y') = year(now()) AND orders.isDeleted = 'No' AND orders.barID = ${barID} AND orders.paymentStatus = 'received' AND orders.refundStatus = 'No' GROUP BY orderByFormate ORDER BY orderByFormate ASC
        `,
        { type: QueryTypes.SELECT }
      )
    } else if (req.body.reportType == 'Weekly') {
      orderReport = await sO.query(`
        SELECT weekday(orderDate) AS orderByFormate, date_format(orderDate, '%a') AS orderFormate, date_format(orderDate, '%V') AS weekFormat, count(id) AS totalOrder FROM orders AS orders WHERE date_format(orderDate, '%Y') = year(now()) AND orders.isDeleted = 'No' AND orders.barID = ${barID} AND orders.paymentStatus = 'received' AND orders.refundStatus = 'No' GROUP BY orderDate HAVING (weekFormat = week(now()) ) ORDER BY orderByFormate ASC;
        `,
        { type: QueryTypes.SELECT }
      )
    }
    const dashboardCount = {}
    dashboardCount.reportType = req.body.reportType
    dashboardCount.orderReport = orderReport

    res.status(200).send({
      success: 1,
      message: 'Success!',
      data: dashboardCount
    })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.processingReport = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {

    let processingReport
    if (req.body.reportType == 'Yearly') {
      processingReport = await sO.query(`
      SELECT date_format(orderDate, '%Y') AS orderByFormate, date_format(ADDDATE(orderDate, INTERVAL 6 MONTH), '%Y') AS orderFormate, date_format(orderDate, '%Y') AS yearFormat, round(AVG(TIMESTAMPDIFF(MINUTE, createdAt, ReadyTime)),2) AS times FROM orders AS orders WHERE orders.isDeleted = 'No' AND orders.barID = ${barID} AND orders.paymentStatus = 'received' AND orders.refundStatus = 'No' AND orders.orderStatus != 'New' GROUP BY orderByFormate HAVING 1=1 ORDER BY orderFormate ASC
        `,
        { type: QueryTypes.SELECT }
      )
    } else if (req.body.reportType == 'Monthly') {
      processingReport = await sO.query(`
      SELECT date_format(orderDate, '%c') AS orderByFormate, date_format(orderDate, '%b') AS orderFormate, date_format(orderDate, '%Y') AS yearFormat, round(AVG(TIMESTAMPDIFF(MINUTE, createdAt, ReadyTime)),2) AS times FROM orders AS orders WHERE date_format(orderDate, '%Y') = year(now()) AND orders.isDeleted = 'No' AND orders.barID = ${barID} AND orders.paymentStatus = 'received' AND orders.refundStatus = 'No' AND orders.orderStatus != 'New' GROUP BY orderByFormate ORDER BY orderByFormate ASC
        `,
        { type: QueryTypes.SELECT }
      )
    } else if (req.body.reportType == 'Weekly') {
      processingReport = await sO.query(`
      SELECT weekday(orderDate) AS orderByFormate, date_format(orderDate, '%a') AS orderFormate, date_format(orderDate, '%V') AS weekFormat, round(AVG(TIMESTAMPDIFF(MINUTE, createdAt, ReadyTime)),2) AS times FROM orders AS orders WHERE date_format(orderDate, '%Y') = year(now()) AND orders.isDeleted = 'No' AND orders.barID = ${barID} AND orders.paymentStatus = 'received' AND orders.refundStatus = 'No' AND orders.orderStatus != 'New' GROUP BY orderDate HAVING (weekFormat = week(now())) ORDER BY orderByFormate ASC
        `,
        { type: QueryTypes.SELECT }
      )
    }
    const dashboardCount = {}
    dashboardCount.reportType = req.body.reportType
    dashboardCount.processingReport = processingReport

    res.status(200).send({
      success: 1,
      message: 'Success!',
      data: dashboardCount
    })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}
