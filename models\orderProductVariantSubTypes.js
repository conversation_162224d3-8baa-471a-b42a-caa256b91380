var Sequelize = require('sequelize')
var productVariantTypes = require('./productVariantTypes')
var productVariantSubTypes = require('./productVariantSubTypes')
var orderItems = require('./orderItems')

var order_product_variant_sub_types = sequelize.define(
  'order_product_variant_sub_types',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderItemID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orderItems",
        key: "id"
      }
    },
    orderProductVariantTypeID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "order_product_variant_types",
        key: "id"
      }
    },
    productVariantTypeID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product_variant_types",
        key: "id"
      }
    },
    productVariantSubTypeID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product_variant_sub_types",
        key: "id"
      }
    },
    price: Sequelize.FLOAT,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

order_product_variant_sub_types.belongsTo(productVariantSubTypes, { foreignKey: 'productVariantSubTypeID' })
productVariantTypes.hasOne(order_product_variant_sub_types, { foreignKey: 'productVariantTypeID' })
order_product_variant_sub_types.belongsTo(orderItems, { foreignKey: 'orderItemID' })
orderItems.hasMany(order_product_variant_sub_types, { foreignKey: 'orderItemID' })

module.exports = order_product_variant_sub_types
