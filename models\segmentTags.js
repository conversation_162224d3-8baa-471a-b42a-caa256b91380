const Sequelize = require('sequelize');

const segmentTags = sequelize.define(
	'segment_tags',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		name: {
			type: Sequelize.STRING,
			allowNull: false
		},
		createdAt: { type: Sequelize.DATE, allowNull: false },
		updatedAt: { type: Sequelize.DATE },
	},
	{
		freezeTableName: true,
		timestamps: true,
		underscored: false,
	}
)



module.exports = segmentTags