var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')

var barNotification = sequelize.define(
  'bar_notification',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    userID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "user",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    dataID: Sequelize.INTEGER,
    notification_type: Sequelize.TEXT,
    message: Sequelize.TEXT,
    status: Sequelize.ENUM('seen', 'unseen'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

barNotification.belongsTo(user, { foreignKey: 'userID' })
barNotification.belongsTo(bar, { foreignKey: 'barID' })

module.exports = barNotification