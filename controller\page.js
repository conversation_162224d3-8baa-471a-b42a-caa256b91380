var Sequelize = require('sequelize')
const message = require('../config/message')
const page = require('../models/page')
const crashLogsModel = require('../models/crashLogs')
const Op = Sequelize.Op
const literal = Sequelize.literal

var env = require('../config/environment')
var jwt = require('jsonwebtoken');

exports.privacyPolicy = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 1
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.faq = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 2
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.terms = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 3
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.barAboutUs = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 4
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.barPrivacyPolicy = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 5
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.barFaq = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 6
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.barTerms = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 7
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}
exports.crashLogs = async (req, res) => {
  try {
    const payload = req.body;
    if (!payload || typeof payload !== 'object') {
      return res.status(400).json({ error: 'Invalid JSON payload' });
    }
    const record = await crashLogsModel.create({ payload });
    res.status(201).json({
      message: 'Crash log saved',
      id: record.id
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
exports.getcrashLogs = async (req, res) => {
  try {
    console.log('getcrashLogs...')
    console.log(req.body);
    const { start, end } = req.body;
    if (!start || !end) {
      return res.status(400).json({ error: 'Start and end dates are required (YYYY-MM-DD)' });
    }
    const logs = await crashLogsModel.findAll({
      where: literal(`DATE(created_at) BETWEEN '${start}' AND '${end}'`),
      order: [['created_at', 'DESC']]
    }); res.status(200).json(logs);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
