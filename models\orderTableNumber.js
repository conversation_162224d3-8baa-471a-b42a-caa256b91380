const Sequelize = require('sequelize')
const orders = require('./orders')

const orderTableNumber = sequelize.define(
  'orderTableNumber',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    orderID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "orders",
        key: "id"
      }
    },
    tableCode: Sequelize.STRING,
    createdAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

orderTableNumber.belongsTo(orders, { foreignKey: 'orderID' })
orders.hasMany(orderTableNumber, { foreignKey: 'orderID' })

module.exports = orderTableNumber