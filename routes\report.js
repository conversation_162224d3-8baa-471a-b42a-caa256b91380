const express = require('express')
const router = express()
const multer = require('multer')

const checkUserAuth = require('../middleware/checkAuth')
const checkBarAuth = require('../middleware/barCheckAuth')
const report = require('../controller/report')

var upload = multer({})

/*Bar API*/
router.post('/report', upload.array(), checkBarAuth, report.report)
router.post('/earningReport', upload.array(), checkBarAuth, report.earningReport)
router.post('/orderReport', upload.array(), checkBarAuth, report.orderReport)
router.post('/processingReport', upload.array(), checkBarAuth, report.processingReport)

module.exports = router