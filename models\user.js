var Sequelize = require('sequelize')

var env = require('../config/environment')
const { s3GetFile } = require('../middleware/awsS3Operations')

var user = sequelize.define(
  'user',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    fullName: Sequelize.TEXT,
    email: Sequelize.TEXT,
    birthday: Sequelize.DATE,
    countryCode: Sequelize.TEXT,
    mobile: Sequelize.TEXT,
    password: Sequelize.TEXT,
    stripeID: Sequelize.TEXT,
    resetPasswordCode: Sequelize.TEXT,
    avatar: {
      type: Sequelize.TEXT,
      get() {
        if (this.getDataValue('avatar') != '') {
          return env.awsPrivateBucketCloudFrontURL + env.awsUserFolder + this.getDataValue('avatar')
          // const contentValue = s3GetFile(
          //   env.awsUserFolder +
          //   this.getDataValue('avatar')
          // );
          // return process.binding('util').getPromiseDetails(contentValue)[1];
        } else {
          return '';
        }
      }
    },
    mobileVerified: Sequelize.ENUM('Yes', 'No'),
    badge: Sequelize.INTEGER,
    oneTimePassword: Sequelize.INTEGER,
    notification: Sequelize.ENUM('Yes', 'No'),
    status: Sequelize.ENUM('Active', 'Inactive'),
    facebook_socialId: Sequelize.TEXT,
    google_socialId: Sequelize.TEXT,
    apple_socialId: Sequelize.TEXT,
    readPopup: Sequelize.ENUM('Yes', 'No'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No')
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = user