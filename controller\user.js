var Sequelize = require('sequelize')
var multer = require('multer')

const moment = require('moment')
const message = require('../config/message')
const common = require('./common')
const user = require('../models/user')
const userTempVerification = require('../models/userTempOtp')
const bar = require('../models/bar')
const cartItems = require('../models/cartItems')
const userAccessToken = require('../models/userAccessToken')
const bcrypt = require('bcrypt')
const state = require('../models/state')
const country = require('../models/country')
const order = require('../models/orders')
const userFavVenue = require('../models/userFavVenue')
const userLocation = require('../models/userLocation')
const Op = Sequelize.Op
const commonFunction = require('../common/commonFunction')
const { s3UploadFile } = require('../middleware/awsS3Operations')
const multerMiddleware = require('../middleware/multer');

var env = require('../config/environment')
var jwt = require('jsonwebtoken');
var stripe = require('stripe')(env.stripe_secret_key)

const sequelize = require('sequelize')
const operatingHours = require('../models/operatingHours')

function _calculateAge(birthday) { // birthday is a date
    var ageDifMs = Date.now() - birthday.getTime();
    var ageDate = new Date(ageDifMs); // miliseconds from epoch
    return Math.abs(ageDate.getUTCFullYear() - 1970);
}

exports.resetPassword = async (req, res) => {
  user
    .findOne({
      attributes: ['id', 'email', 'fullName'],
      where: {
        email: req.body.email,
        isDeleted: 'No'
      }
    })
    .then(async userFetchResponse => {
      if (userFetchResponse) {
        try {
          var randtoken = require('rand-token').generator()
          var sixDigitCode = randtoken.generate(6, '**********')

          var emailContent = 'Your reset password code is: ' + sixDigitCode

          let mailOptions = {
            from: '"MyTab" <' + env.fromEmailAdmin + '>',
            to: req.body.email,
            subject: 'Password Reset Code',
            html: emailContent
          }

          const emailRes = await common.sendEmail(mailOptions)
            .then(response => {
              if (response) {
                user
                  .update(
                    {
                      resetPasswordCode: sixDigitCode,
                      updatedAt: new Date()
                    },
                    {
                      returning: true,
                      where: {
                        id: userFetchResponse.id
                      }
                    }
                  )
                  .then(function () {
                    res.status(200).json({
                      success: 1,
                      message: 'Email sent successfully',
                      data: {
                        email: userFetchResponse.email
                      }
                    })
                  })
              }
            })
        } catch (e) {
          res.status(200).json({
            success: 0,
            message: e.message
          })
        }
      } else {
        res.status(200).send({
          success: 0,
          message: message.landing.emailNotRegistered,
          data: {}
        })
      }
    })
}

exports.verifyResetPasswordCode = (req, res) => {
  try {
    user
      .findOne({
        attributes: ['id', 'email', 'resetPasswordCode'],
        where: {
          email: req.body.email,
          resetPasswordCode: req.body.resetPasswordCode,
          isDeleted: 'No'
        }
      })
      .then(userFetchResponse => {
        if (userFetchResponse) {
          res.status(200).json({
            success: 1,
            message: message.user.otpVerified,
            data: userFetchResponse
          })
        } else {
          res.status(200).json({
            success: 0,
            message: message.user.otpVerificationFailed,
            data: {}
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updatePassword = async (req, res) => {
  try {
    user
      .findOne({
        where: {
          id: req.body.id,
          resetPasswordCode: req.body.resetPasswordCode,
        }
      })
      .then(async userData => {
        if (userData) {
          bcrypt.hash(req.body.password, 10, (err, hash) => {
            if (!err) {
              user
                .update(
                  {
                    password: hash,
                    resetPasswordCode: '',
                    updatedAt: new Date()
                  },
                  {
                    returning: true,
                    where: {
                      id: userData.id
                    }
                  }
                )
                .then(function () {
                  res.status(200).send({
                    success: 1,
                    message: message.user.passwordUpdated
                  })
                })
            } else {
              res.status(200).send({
                success: 0,
                message: "Something went wrong!"
              })
            }
          })
        } else {
          res.status(200).send({
            success: 0,
            message: 'updatePassword: Failed!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.phoneNumber = (req, res) => {
  user
    .update(
      {
        mobile: req.body.mobile,
        countryCode: req.body.countryCode,
        oneTimePassword: '',
        mobileVerified: 'Yes',
        updatedAt: new Date()
      },
      {
        returning: true,
        where: {
          userID: req.body.userID,
          email: req.body.email
        }
      }
    )
    .then(function (rowsUpdated) {
      if (rowsUpdated[1] == 1) {
        res.status(200).json({
          success: 1,
          message: message.user.mobileUpdated,
          rowsUpdated: rowsUpdated
        })
      } else {
        res.status(200).json({
          success: 0,
          message: 'updatePhoneNumber: Please enter valid userID!',
          rowsUpdated: rowsUpdated
        })
      }
    })
    .error(function (err) {
      res.status(200).json({
        success: 0,
        message: 'updatePhoneNumber: Failed!'
      })
    })
}

exports.profile = async (req, res) => {
  //const { userID } = req.body

  var responseObject = {}
  var productPromises = []

  productPromises.push(fetchProfile(req))
  productPromises.push(fetchCollection(req))
  //productPromises.push(fetchLikedFeed(req))
  //productPromises.push(fetchLikedProduct(req))

  const likedFeed = await fetchLikedFeed(req)
  const likeProduct = await fetchLikedProduct(req)
  const likedAll = [...likedFeed.value, ...likeProduct.value]

  const likedData = likedAll.sort((a, b) => a.createdAt + b.createdAt)

  productPromises.push({
    key: 'LikedPosts',
    value: likedData.slice(0, 5)
  })

  productPromises.push(fetchBrandsYouFollow(req))

  Promise.all(productPromises)
    .then(async function (data) {
      for (var i = 0; i < data.length; i++) {
        responseObject[data[i]['key']] = data[i]['value']
      }
      // add entry in recent search area
      // if (req.headers.userid != userID) {
      //   await recentSearch.findOrCreate({
      //     where: {
      //       userID: req.headers.userid,
      //       searchUserID: userID
      //     },
      //     defaults: {
      //       userID: req.headers.userid,
      //       searchUserID: userID,
      //       type: 'User'
      //     }
      //   })
      //   //console.log(userID + '===' + req.headers.userid)
      // }
      res.json({
        success: 1,
        message: 'success!',
        data: responseObject
      })
    })
    .catch(function (err) {
      /* error handling */
    })
}

function fetchProfile(req) {
  return new Promise(function (resolve, reject) {
    var userID = req.headers.userid
    // const { userID } = req.body
    user
      .findOne({
        attributes: [
          'userID',
          'name',
          'countryCode',
          'mobile',
          'avatar',
          'facebookID',
          'googleID'
        ],
        where: {
          userID: userID
        }
      })
      .then(userResponse => {
        resolve({
          key: 'Profile',
          value: userResponse
        })
      })
  })
}

exports.notification = (req, res) => {
  user
    .update(
      {
        notification: req.body.notification,
        updatedAt: new Date()
      },
      {
        returning: true,
        where: {
          userID: req.headers.userid
        }
      }
    )
    .then(function () {
      res.json({
        success: 1,
        message: 'success!'
      })
    })
}

exports.changePassword = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    user
      .findOne({
        where: {
          id: userID
        }
      })
      .then(async userData => {
        if (userData) {
          bcrypt.compare(
            req.body.oldPassword,
            userData.password,
            (err, response) => {
              if (err) {
                res.status(200).send({
                  success: 0,
                  message: "Something went wrong!"
                })
              }
              if (response) {
                bcrypt.hash(req.body.newPassword, 10, (err, hash) => {
                  if (!err) {
                    user
                      .update(
                        {
                          password: hash
                        },
                        {
                          returning: true,
                          where: {
                            id: userID
                          }
                        }
                      )
                      .then(function () {
                        res.status(200).send({
                          success: 1,
                          message: "Your password has been updated"
                        })
                      })
                  } else {
                    res.status(200).send({
                      success: 0,
                      message: "Something went wrong!"
                    })
                  }
                })
              } else {
                res.status(200).send({
                  success: 0,
                  message: "Invalid Old Password"
                })
              }
            }
          );
        } else {
          res.status(200).send({
            success: 0,
            message: message.user.enterValidCredential
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

let fileFilter = function (req, file, cb) {
  var allowedMimes = ['image/jpeg', 'image/png'];
  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb({
      success: false,
      message: 'Invalid file type. Only jpg, png image files are allowed.',
      code: 'LIMIT_FILE_TYPE'
    }, false);
  }
};

exports.checkEmail = async (req, res) => {
  try {
    user
      .findOne({
        attributes: [
          'email',
          'isDeleted'
        ],
        where: {
          email: req.body.email.toLowerCase(),
          isDeleted: 'No'
        }
      })
      .then(userDataResponse => {
        if (userDataResponse && Object.keys(userDataResponse).length > 0) {
          if (userDataResponse.isDeleted == 'Yes') {
            res.status(200).send({
              success: 0,
              message: message.user.userDeleted
            })
          } else {
            res.status(200).send({
              success: 2,
              message: message.user.emailExists
            })
          }
        } else {
          res.status(200).send({
            success: 1,
            message: 'success!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.checkMobile = async (req, res) => {
  try {
    user
      .findOne({
        attributes: [
          'mobile',
          'isDeleted'
        ],
        where: {
          mobile: req.body.mobile,
          isDeleted: 'No'
        }
      })
      .then(userDataResponse => {
        if (userDataResponse && Object.keys(userDataResponse).length > 0) {
          if (userDataResponse.isDeleted == 'Yes') {
            res.status(200).send({
              success: 0,
              message: message.user.userDeleted
            })
          } else {
            res.status(200).send({
              success: 2,
              message: message.user.mobileExists
            })
          }
        } else {
          res.status(200).send({
            success: 1,
            message: 'success!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.sendEmailOTP = async (req, res) => {
  try {
    if(req.body.isRegister == 1){
      user
        .findOne({
          attributes: [
            'email',
            'isDeleted'
          ],
          where: {
            email: req.body.email.toLowerCase(),
            isDeleted: 'No'
          }
        })
        .then(async(userDataResponse) => {
          if (userDataResponse && Object.keys(userDataResponse).length > 0) {
            res.status(200).send({
              success: 0,
              message: message.user.emailExists
            })
          } else {
            var randtoken = require('rand-token').generator()
            var sixDigitCode = randtoken.generate(6, '**********')
            
            const subject = "MyTab Customer Account Verification Code";
            const today = new Date();
            const year = today.getFullYear();
            let mailOptions = {
              from: `MyTab <${env.fromEmailAdmin}>`,
              to: req.body.email,
              subject: subject,
              html: `
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8"><base href="/"/><title>${subject}</title>
                </head>
                <body style="padding:0; margin:0; color:#6e6e6e; font-family:Verdana; -webkit-text-size-adjust: none;">
                  <table style="-webkit-text-size-adjust: none;" width="100%" cellspacing="0" cellpadding="0" border="0">
                    <tr>
                      <td align="center">
                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="min-width:200px; border: 1px solid #6e6e6e; max-width:600px; ">
                          <tr style="border-bottom: 3px solid #58585a">
                            <td>
                              <center>
                                <a style="color:#6e6e6e; text-decoration:none;" href="https://www.mytabinfo.com">
                                  <h1 style="margin-top:30px">
                                    <img src=${process.env.logo} alt=MyTab style="border-width:0; max-width:164px;height:55px; display:block; " />
                                  </h1>
                                </a>
                              </center>
                            </td>
                          </tr>
                          <tr>
                            <td style="padding: 15px; font-size: 10pt; line-height: 22px;">
                              <div>
                                <p>Hello ${req.body.userName},</p>
                                <p>You are almost there! In order to keep our MyTab foodie community safe, please verify your MyTab customer account by entering the 6 digit verification code below in the app.</p>
                                <p>One-Time code: <b>${sixDigitCode}</b></p>
                                <p>If you did not request this verification, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a> for assistance.</p>
                                <p>Kindest,<br/> MyTab Customer Support Team <br/> <a style="color:#ff6460" href="https://www.mytabinfo.com">www.mytabinfo.com</a><p/>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td style="text-align: center; background-color:#58585a; color: #fff; padding-top: 5px; padding-bottom: 5px; font-weight: bold; font-size: 30px;" colspan="2"></td>
                          </tr>
                          <tr>
                            <td style="text-align: center; background-color: #58585a; color: #fff; padding: 0px 0px 10px;" colspan="2">
                              <p style="font-size: 12px;line-height: 1.6;font-weight: normal;"> &copy; ${year} MyTab | All&nbsp;Rights&nbsp;Reserved</p>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </body>
              </html>`
            }
  
            await common.sendEmail(mailOptions);
  
            let venueData = await userTempVerification.find({
              where: { email : req.body.email }
            })
            if(venueData){
              await userTempVerification.update({
                otp: sixDigitCode,
                expiredAt: moment().add(2, "hours")
              },{
                where: { email : req.body.email }
              })
            }else{
              await userTempVerification.create({
                email: req.body.email,
                otp: sixDigitCode,
                expiredAt: moment().add(2, "hours")
              })
            }
  
            res.status(200).json({
              success: 1,
              message: 'A code has been sent to your email',
              data: {}
            })
          }
        })
    }else{
      user
        .findOne({
          attributes: [
            'email',
            'id',
            'fullName'
          ],
          where: {
            email: req.body.email.toLowerCase(),
            status: 'Active',
            isDeleted: 'No'
          }
        })
        .then(async(userDataResponse) => {
          if(userDataResponse){

            var randtoken = require('rand-token').generator()
            var sixDigitCode = randtoken.generate(6, '**********')
            
            const subject = "MyTab Customer Account Verification Code";
            const today = new Date();
            const year = today.getFullYear();
            
            let mailOptions = {
              from: `MyTab <${env.fromEmailAdmin}>`,
              to: req.body.email,
              subject: subject,
              html: `
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8"><base href="/"/><title>${subject}</title>
                </head>
                <body style="padding:0; margin:0; color:#6e6e6e; font-family:Verdana; -webkit-text-size-adjust: none;">
                  <table style="-webkit-text-size-adjust: none;" width="100%" cellspacing="0" cellpadding="0" border="0">
                    <tr>
                      <td align="center">
                        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="min-width:200px; border: 1px solid #6e6e6e; max-width:600px; ">
                          <tr style="border-bottom: 3px solid #58585a">
                            <td>
                              <center>
                                <a style="color:#6e6e6e; text-decoration:none;" href="https://www.mytabinfo.com">
                                  <h1 style="margin-top:30px">
                                    <img src=${process.env.logo} alt=MyTab style="border-width:0; max-width:164px;height:55px; display:block; " />
                                  </h1>
                                </a>
                              </center>
                            </td>
                          </tr>
                          <tr>
                            <td style="padding: 15px; font-size: 10pt; line-height: 22px;">
                              <div>
                                <p>Hello ${userDataResponse.fullName},</p>
                                <p>You are almost there! In order to keep our MyTab foodie community safe, please verify your MyTab customer account by entering the 6 digit verification code below in the app.</p>
                                <p>One-Time code: <b>${sixDigitCode}</b></p>
                                <p>If you did not request this verification, please contact us at <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a> for assistance.</p>
                                <p>Kindest,<br/> MyTab Customer Support Team <br/> <a style="color:#ff6460" href="https://www.mytabinfo.com">www.mytabinfo.com</a><p/>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td style="text-align: center; background-color:#58585a; color: #fff; padding-top: 5px; padding-bottom: 5px; font-weight: bold; font-size: 30px;" colspan="2"></td>
                          </tr>
                          <tr>
                            <td style="text-align: center; background-color: #58585a; color: #fff; padding: 0px 0px 10px;" colspan="2">
                              <p style="font-size: 12px;line-height: 1.6;font-weight: normal;"> &copy; ${year} MyTab | All&nbsp;Rights&nbsp;Reserved</p>
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </body>
              </html>`
            }
  
            await common.sendEmail(mailOptions);
            
            await userDataResponse.update({
              oneTimePassword: sixDigitCode
            })
            
            return res.status(200).json({
              success: 1,
              message: 'A code has been sent to your email',
              data: {}
            })
          }else{
            return res.status(200).json({
              success: 1,
              message: 'Account does not exist',
              data: {}
            })
          }
        })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.verifyEmailOTP = async (req, res) => {
  try {
    if(req.body.isRegister == 1){
      userTempVerification
        .findOne({
          where: {
            email: req.body.email.toLowerCase(),
          }
        })
        .then(async(userDataResponse) => {
          if (userDataResponse) {
            if(userDataResponse.otp == req.body.otp){
              if(new Date() > new Date(userDataResponse.expiredAt)) {
                return res.status(200).json({
                  success: 0,
                  message: 'Your otp has been expired. Please try to generate new one.',
                  data: {}
                })
              }else{
                await userTempVerification.destroy({ where: { email: req.body.email }});
                return res.status(200).json({
                  success: 1,
                  message: 'Your account is verified',
                  data: {}
                })
              }
            }else{
              return res.status(200).json({
                success: 0,
                message: 'Please enter the correct verification code',
                data: {}
              })
            }
          } else {
            return res.status(200).send({
              success: 0,
              message: 'Account does not exist'
            })
          }
        })
    }else{
      user
        .findOne({
          where: {
            email: req.body.email.toLowerCase(),
            status: 'Active',
            isDeleted: 'No'
          }
        })
        .then(async(userDataResponse) => {
          if(userDataResponse){
            if(userDataResponse.oneTimePassword == req.body.otp){
              await userDataResponse.update({ oneTimePassword: 0 });
              return res.status(200).json({
                success: 1,
                message: 'Your account is verified',
                data: {}
              })
            }else{
              return res.status(200).json({
                success: 0,
                message: 'Please enter the correct verification code',
                data: {}
              })
            }
          }else{
            return res.status(200).send({
              success: 0,
              message: 'Account does not exist'
            })
          }
        })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.userRegister = async (req, res) => {
  try {
    await multerMiddleware.singleUserProfilePic(req, res, async function (err) {
      // Multer file validation
      if (err && err.code == 'LIMIT_FILE_SIZE') {
        return res.status(500).send({
          success: 0,
          message: 'File Size is too large. Allowed file size is 5MB'
        });
      } else if (err && err.code == 'LIMIT_FILE_TYPE') {
        return res.status(500).send({
          success: 0,
          message: err.message
        });
      }

      if (req.file != undefined) {
        await s3UploadFile(
          req.file,
          env.awsUserFolder +
          req.file.originalname
        );
      }
      if (req.body.isError) {
        res.status(200).send({
          success: 0,
          message: req.body.message
        })
      } else {
        user
          .findOne({
            where: {
              email: req.body.email.toLowerCase(),
              isDeleted: 'No'
            }
          })
          .then(userDataResponse => {
            if (userDataResponse && Object.keys(userDataResponse).length > 0) {
              if (userDataResponse.isDeleted == 'Yes') {
                res.status(200).send({
                  success: 0,
                  message: message.user.userDeleted
                })
              } else {
                res.status(200).send({
                  success: 0,
                  message: message.user.emailExists
                })
              }
            } else {
              if(req.body.isSocialLogin && req.body.isSocialLogin == '1'){
                var password = 'MyTab123456'
              }else{
                var password = req.body.password
              }

              bcrypt.hash(password, 10, async (err, hash) => {
                if (!err) {
                  var avatar = '';
                  if (req.file != undefined) {
                    avatar = req.file.originalname
                  }

                  user
                    .create({
                      fullName: req.body.fullName ? req.body.fullName : null,
                      email: req.body.email.toLowerCase(),
                      birthday: req.body.birthday ? req.body.birthday : null,
                      mobile: req.body.mobile ? req.body.mobile : null,
                      countryCode: req.body.countryCode ? req.body.countryCode : null,
                      facebook_socialId: req.body.socialId && req.body.loginType == 'FACEBOOK' ? req.body.socialId : null,
                      google_socialId: req.body.socialId && req.body.loginType == 'GOOGLE' ? req.body.socialId : null,
                      apple_socialId: req.body.socialId && req.body.loginType == 'APPLE' ? req.body.socialId : null,
                      password: hash,
                      avatar: avatar,
                      readPopup: 'Yes',
                      createdAt: new Date()
                    })
                    .then(userData => {
                      jwt.sign({ userID: userData.id, userType: 'customer' }, env.ACCESS_TOKEN_SECRET,
                        (err, token) => {
                          if (err) {
                            res.status(200).send({
                              success: 0,
                              message: "Something went wrong!"
                            })
                          } else {
                            userAccessToken
                              .create({
                                accessToken: token,
                                userID: userData.id,
                                loginType: req.body.loginType ? req.body.loginType : 'NORMAL',
                                deviceType: req.body.deviceType,
                                deviceToken: req.body.deviceToken
                              })
                              .then(async(createRes) => {
                                if (createRes) {
                                  await stripe.customers.create(
                                    {
                                      description: userData.id + '_' + userData.fullName,
                                      email: userData.email,
                                      phone: (req.body.countryCode ? req.body.countryCode : '') + (req.body.mobile ? req.body.mobile : ''),
                                      metadata: {
                                        birthday: req.body.birthday ? req.body.birthday : null,
                                        age: req.body.birthday ? _calculateAge(new Date(req.body.birthday)) : null, 
                                      }
                                    },
                                    async function (err, customer) {
                                      if (err == null) {
                                        userStripeID = customer.id
                                        await user.update(
                                          {
                                            stripeID: userStripeID
                                          },
                                          {
                                            where: {
                                              id: userData.id
                                            }
                                          }
                                        )
                                      } else {
                                        console.log("Stripe Error: ",err);
                                      }
                                    }
                                  )
                                  await user
                                    .findOne({
                                      attributes: [
                                        'id',
                                        'fullName',
                                        'email',
                                        'birthday',
                                        'countryCode',
                                        'mobile',
                                        'avatar',
                                        'mobileVerified',
                                        'badge',
                                        'notification',
                                        'status'
                                      ],
                                      where: {
                                        id: userData.id
                                      }
                                    })
                                    .then(async newUserData => {
                                      if (newUserData) {
                                        newUserData.dataValues.loginType = req.body.loginType ? req.body.loginType : 'NORMAL'
                                        newUserData = newUserData.toJSON(); // actually returns a plain object, not a JSON string
                                        newUserData.accessToken = token
                                        newUserData.cartItemAvailable = 'No'
                                        newUserData.barName = ''

                                        res.status(200).send({
                                          success: 1,
                                          data: newUserData,
                                          message: 'success!'
                                        })
                                      } else {
                                        res.status(200).send({
                                          success: 0,
                                          message: message.user.enterValidCredential
                                        })
                                      }
                                    })
                                } else {
                                  res.status(200).send({
                                    success: 0,
                                    message: "Something went wrong!"
                                  })
                                }
                              })
                              .catch(err =>
                                res.status(200).send({
                                  success: 0,
                                  message: "Something went wrong!"
                                })
                              );
                          }
                        }
                      );
                    })
                } else {
                  res.status(200).send({
                    success: 0,
                    message: "Something went wrong!"
                  })
                }
              })
            }
          })
      }
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.login = (req, res) => {
  if(req.body.isSocialLogin && req.body.isSocialLogin == '1'){
    var whereClause = {
      [Op.or]: {
        facebook_socialId: req.body.socialId,
        google_socialId: req.body.socialId,
        apple_socialId: req.body.socialId,
      },
      isDeleted: 'No'
    }
  }else{
    var whereClause = {
      email: req.body.email.toLowerCase(),
      isDeleted: 'No'
    }
  }

  let userAttributes = [
    'id',
    'fullName',
    'email',
    'birthday',
    'password',
    'countryCode',
    'mobile',
    'avatar',
    'mobileVerified',
    'badge',
    'notification',
    'status',
  ];

  try {
    user
      .findOne({
        attributes: userAttributes,
        where: whereClause
      })
      .then(async userData => {
        if (userData) {
          if(req.body.isSocialLogin && req.body.isSocialLogin == '1'){
            if(userData.email != null || userData.email != ''){
              if (userData.status == 'Inactive') {
                return res.status(200).send({
                  success: 0,
                  message: message.user.userInactive
                })
              } else {
                userData = userData.toJSON(); // actually returns a plain object, not a JSON string
                jwt.sign({ userID: userData.id, userType: 'customer' }, env.ACCESS_TOKEN_SECRET,
                  (err, token) => {
                    if (err) {
                      res.status(200).send({
                        success: 0,
                        message: "Something went wrong!"
                      })
                    } else {
                      userAccessToken
                        .create({
                          userID: userData.id,
                          accessToken: token,
                          loginType: req.body.loginType,
                          deviceType: req.body.deviceType,
                          deviceToken: req.body.deviceToken,
                          createdAt: new Date(),
                          updatedAt: new Date()
                        })
                        .then(async (accessToken, created) => {
                          userData.accessToken = token
                          delete userData.password
  
                          const cartData = await cartItems
                            .findOne({
                              where: {
                                userID: userData.id
                              },
                              include: [
                                {
                                  model: bar,
                                  attributes: [
                                    'id',
                                    'restaurantName'
                                  ]
                                }
                              ]
                            })
                          userData.cartItemAvailable = (cartData) ? 'Yes' : 'No'
                          userData.barName = (cartData) ? cartData.bar.restaurantName : ''
                          if (created) {
                            userData.loginType = req.body.loginType
                            res.status(200).send({
                              success: 1,
                              data: userData,
                              message: 'success!'
                            })
                          } else {
                            userData.loginType = req.body.loginType
                            res.status(200).send({
                              success: 1,
                              data: userData,
                              message: 'success!'
                            })
                          }
                        })
                    }
                  });
              }
            }else{
              res.status(200).send({
                success: 0,
                isRegister: 1,
                message: message.user.emailDoesNotExists
              })
            }
          }else{
            bcrypt.compare(
              req.body.password,
              userData.password,
              (err, response) => {
                if (err) {
                  res.status(200).send({
                    success: 0,
                    message: "Something went wrong!"
                  })
                }
                if (response) {
                  if (userData.status == 'Inactive') {
                    return res.status(200).send({
                      success: 0,
                      message: message.user.userInactive
                    })
                  } else {
                    userData = userData.toJSON(); // actually returns a plain object, not a JSON string
                    jwt.sign({ userID: userData.id, userType: 'customer' }, env.ACCESS_TOKEN_SECRET,
                      (err, token) => {
                        if (err) {
                          res.status(200).send({
                            success: 0,
                            message: "Something went wrong!"
                          })
                        } else {
                          userAccessToken
                            .create({
                              userID: userData.id,
                              accessToken: token,
                              deviceType: req.body.deviceType,
                              deviceToken: req.body.deviceToken,
                              createdAt: new Date(),
                              updatedAt: new Date()
                            })
                            .then(async (accessToken, created) => {
                              userData.accessToken = token
                              delete userData.password
  
                              const cartData = await cartItems
                                .findOne({
                                  where: {
                                    userID: userData.id
                                  },
                                  include: [
                                    {
                                      model: bar,
                                      attributes: [
                                        'id',
                                        'restaurantName'
                                      ]
                                    }
                                  ]
                                })
                              userData.cartItemAvailable = (cartData) ? 'Yes' : 'No'
                              userData.barName = (cartData && cartData.bar) ? cartData.bar.restaurantName : ''
                              if (created) {
                                userData.loginType = req.body.loginType
                                res.status(200).send({
                                  success: 1,
                                  data: userData,
                                  message: 'success!'
                                })
                              } else {
                                userData.loginType = req.body.loginType
                                res.status(200).send({
                                  success: 1,
                                  data: userData,
                                  message: 'success!'
                                })
                              }
                            })
                        }
                      });
                  }
                } else {
                  return res.status(200).send({
                    success: 0,
                    message: "Invalid Password"
                  })
                }
              }
            );
          }
        } else {
          if(req.body.isSocialLogin && req.body.isSocialLogin == '1'){
            if(req.body.email && req.body.email != ''){
              let userData = await user.findOne({attributes: userAttributes, where: { email : req.body.email,isDeleted: 'No' }});
              if(userData){
                if(req.body.loginType == 'FACEBOOK' && userData.facebook_socialId != null){
                  return res.status(200).send({
                    success: 0,
                    message: message.user.enterValidCredential
                  })
                }
                if(req.body.log == 'GOOGLE' && userData.google_socialId != null){
                  return res.status(200).send({
                    success: 0,
                    message: message.user.enterValidCredential
                  })
                }
                if(req.body.log == 'APPLE' && userData.apple_socialId != null){
                  return res.status(200).send({
                    success: 0,
                    message: message.user.enterValidCredential
                  })
                }
                if(req.body.loginType == 'FACEBOOK'){
                  await user.update({ facebook_socialId: req.body.socialId },{ where: { email : userData.email, isDeleted: 'No' }});
                }else if(req.body.log == 'GOOGLE'){
                  await user.update({ google_socialId: req.body.socialId },{ where: { email : userData.email, isDeleted: 'No' }});
                }else if(req.body.log == 'APPLE'){
                  await user.update({ apple_socialId: req.body.socialId },{ where: { email : userData.email, isDeleted: 'No' }});
                }
                if (userData.status == 'Inactive') {
                  return res.status(200).send({
                    success: 0,
                    message: message.user.userInactive
                  })
                } else {
                  userData = userData.toJSON(); // actually returns a plain object, not a JSON string
                  jwt.sign({ userID: userData.id, userType: 'customer' }, env.ACCESS_TOKEN_SECRET,
                    (err, token) => {
                      if (err) {
                        res.status(200).send({
                          success: 0,
                          message: "Something went wrong!"
                        })
                      } else {
                        userAccessToken
                          .create({
                            userID: userData.id,
                            accessToken: token,
                            deviceType: req.body.deviceType,
                            loginType: req.body.loginType,
                            deviceToken: req.body.deviceToken,
                            createdAt: new Date(),
                            updatedAt: new Date()
                          })
                          .then(async (accessToken, created) => {
                            userData.accessToken = token
                            delete userData.password
    
                            const cartData = await cartItems
                              .findOne({
                                where: {
                                  userID: userData.id
                                },
                                include: [
                                  {
                                    model: bar,
                                    attributes: [
                                      'id',
                                      'restaurantName'
                                    ]
                                  }
                                ]
                              })
                            userData.cartItemAvailable = (cartData) ? 'Yes' : 'No'
                            userData.barName = (cartData) ? cartData.bar.restaurantName : ''
                            if (created) {
                              userData.loginType = req.body.loginType;
                              res.status(200).send({
                                success: 1,
                                data: userData,
                                message: 'success!'
                              })
                            } else {
                              userData.loginType = req.body.loginType;
                              res.status(200).send({
                                success: 1,
                                data: userData,
                                message: 'success!'
                              })
                            }
                          })
                      }
                    });
                }
              }else{
                return res.status(200).send({
                  success: 0,
                  isRegister: 1,
                  message: message.user.emailDoesNotExists
                })
              }
            }else{
              return res.status(200).send({
                success: 0,
                isRegister: 1,
                message: message.user.emailDoesNotExists
              })
            }
          }else{
            return res.status(200).send({
              success: 0,
              message: message.user.enterValidCredential
            })
          }
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateDeviceToken = async (req, res) => {
  try {
    userAccessToken
      .findOne({
        where: {
          accessToken: req.headers.accesstoken,
        }
      })
      .then(async userAccessTokenData => {
        if (userAccessTokenData) {
          userAccessTokenData
            .update(
              {
                deviceToken: req.body.deviceToken,
                deviceType: req.body.deviceType,
                updatedAt: new Date()
              },
              {
                returning: true,
                where: {
                  accessToken: req.headers.accesstoken,
                }
              }
            )
            .then(function () {
              res.status(200).send({
                success: 1,
                message: message.user.userDeviceTokenUpdated
              })
            })
        } else {
          res.status(200).send({
            success: 0,
            message: 'updateDeviceToken: Failed!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.readPopupMessage = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID;
    user
      .findOne({
        where: {
          id: userID,
          readPopup: 'No'
        }
      })
      .then(async userData => {
        if (userData) {
          userData
            .update(
              {
                readPopup: 'Yes',
              },
              {
                where: {
                  id: userID,
                }
              }
            )
            .then(function () {
              let popupMessage = { "popupMessage" : "We’ve updated our Privacy Policy and Terms & Conditions to better serve you and enhance transparency. You can find the updates on our website: https://mytabinfo.com For questions, email <NAME_EMAIL>, Thanks"}
              res.status(200).send({
                success: 1,
                data: popupMessage,
                message: message.user.messageRead
              })
            })
        } else {
          res.status(200).send({
            success: 0,
            message: message.user.alreadyViewPopup
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.verifyMobileNumber = (req, res) => {
  try {
    var sessionData = jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    user
      .update(
        {
          mobileVerified: 'Yes',
        },
        {
          returning: true,
          where: {
            id: sessionData.userID
          }
        }
      )
      .then(function () {
        res.status(200).json({
          success: 1,
          message: message.user.mobileVerified
        })
      })
  } catch (e) {
    res.status(401).send({
      status: 0,
      message: message.user.sessionExpired
    })
  }
}

exports.checkUniqueUser = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID;
    let validationFlag = 1;
    if(req.body.email && req.body.email != ''){
      await user.findOne({
        attributes: [
          'email',
          'isDeleted'
        ],
        where: {
          $or: {
            email : req.body.email,
          },
          isDeleted: 'No',
          id: {
            $ne: userID
          }
        }
      }).then(userDataResponse => {
        if (userDataResponse && Object.keys(userDataResponse).length > 0) {
          validationFlag = 2;
        }
      })
    }

    if(req.body.mobile && req.body.mobile != ''){
      await user.findOne({
        attributes: [
          'mobile',
          'isDeleted'
        ],
        where: {
          $or: {
            mobile : req.body.mobile,
          },
          isDeleted: 'No',
          id: {
            $ne: userID
          }
        }
      }).then(userDataResponse => {
        if (userDataResponse && Object.keys(userDataResponse).length > 0) {
          validationFlag = 3;
        }
      })
    }
     
    if(validationFlag == 1){
      return res.status(200).send({
        success: 1,
        message: 'success!'
      })
    }else if(validationFlag == 2){
      return res.status(200).send({
        success: 2,
        message: message.user.emailExists
      })
    }else if(validationFlag == 3){
      return res.status(200).send({
        success: 2,
        message: message.user.mobileExists
      })
    }else{
      return res.status(200).send({
        success: 0,
        message: 'error!'
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.editProfile = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID
    await multerMiddleware.singleUserProfilePic(req, res, async function (err) {
      // Multer file validation
      if (err && err.code == 'LIMIT_FILE_SIZE') {
        return res.status(500).send({
          success: 0,
          message: 'File Size is too large. Allowed file size is 5MB'
        });
      } else if (err && err.code == 'LIMIT_FILE_TYPE') {
        return res.status(500).send({
          success: 0,
          message: err.message
        });
      }
      if (req.file != undefined) {
        await s3UploadFile(
          req.file,
          env.awsUserFolder +
          req.file.originalname
        );
      }
      if (req.body.isError) {
        res.status(200).send({
          success: 0,
          message: req.body.message
        })
      } else {
        var updateData = {}
        if (req.file != undefined) {
          const userOldData = await user.findOne({
            where: {
              id: userID
            }
          })
          if (userOldData.dataValues.avatar != '') {
            common.deleteFile(userOldData.dataValues.avatar, '/user/original')
          }
          updateData['avatar'] = req.file.originalname
        }

        user
          .findOne({
            where: {
              email: req.body.email,
              id: {
                $ne: userID
              },
              isDeleted: 'No'
            }
          })
          .then(async userDataResponse => {
            if (userDataResponse && Object.keys(userDataResponse).length > 0) {
              res.status(200).send({
                success: 0,
                message: message.user.emailExists
              })
            } else {
              updateData['fullName'] = req.body.fullName
              updateData['email'] = req.body.email
              updateData['birthday'] = req.body.birthday
              updateData['countryCode'] = req.body.countryCode
              updateData['mobile'] = req.body.mobile
              let userAccessTokenData = await userAccessToken.findOne({ where: { accessToken: req.headers.accesstoken}});
              user
                .update(updateData, {
                  returning: true,
                  where: {
                    id: userID
                  }
                })
                .then(async userUpdateRes => {
                  user
                    .findOne({
                      attributes: [
                        'id',
                        'fullName',
                        'email',
                        'birthday',
                        'countryCode',
                        'mobile',
                        'avatar',
                        'mobileVerified',
                        'badge',
                        'notification',
                        'status',
                      ],
                      where: {
                        id: userID,
                        isDeleted: 'No'
                      }
                    })
                    .then(userData => {
                      if (userData) {
                        userData.dataValues.loginType = userAccessTokenData.loginType
                        res.status(200).send({
                          success: 1,
                          message: 'Your profile has been updated',
                          data: userData
                        })
                      } else {
                        res.status(200).json({
                          success: 0,
                          message: 'error!',
                        })
                      }
                    })
                }).error(function (err) {
                  res.status(200).json({
                    success: 0,
                    message: err.message,
                  })
                })
            }
          })
      }
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getProfile = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID
    user
      .findOne({
        where: {
          id: userID,
        },
        attributes: ['id', 'avatar', 'fullName', 'email', 'birthday', 'countryCode', 'mobile', 'status']
      })
      .then(async userDataResponse => {
        if(userDataResponse){
          res.status(200).send({
            success: 1,
            message: 'Your profile details fetched successfully',
            data: userDataResponse
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No user data found',
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.delete = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID
    // user
    //   .update(
    //     {
    //       isDeleted: 'Yes',
    //       updatedAt: new Date()
    //     },
    //     {
    //       returning: true,
    //       where: {
    //         id: userID
    //       }
    //     }
    //   )
    const userData = await user.findOne({ where: { id : userID } })
    await userFavVenue
      .destroy({
        where: {
          userID: userID
        }
      });
    user.destroy({ where: { id : userID } })
      .then(function () {
        userAccessToken
          .destroy({
            where: {
              userID: userID
            }
          }).then(async function () {
            await stripe.customers.update(
              userData.stripeID,
              {
                description: userData.id + '_MyTab Customer',
                email: null,
                phone: null,
                metadata: {
                  birthday: null,
                  age: null, 
                },
              }
            );

            res.status(200).json({
              success: 1,
              message: 'Your account has successfully been deleted'
            })
          })
      })
  } catch (error) {
    res.status(200).json({
      success: 0,
      message: 'User: Delete Operation Failed!'
    })
  }
}

exports.logout = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var userID = sessionData.userID
    userAccessToken
      .destroy({
        where: {
          userID: userID,
          accessToken: req.headers.accesstoken,
        }
      })
      .then(function () {
        res.status(200).json({
          success: 1,
          message: message.landing.logOut
        })
      })
      .error(function (err) {
        res.status(200).json({
          success: 0,
          message: 'User: LogOut Operation Failed!'
        })
      })
  } catch (error) {
    res.status(200).json({
      success: 0,
      message: 'User: LogOut Operation Failed!'
    })
  }
}

exports.contactUs = async (req, res) => {
  var name = req.body.name
  var email = req.body.email
  var message = req.body.message
  var userID = req.body.userID ? req.body.userID : ''
  var countryCode = req.body.countryCode ? req.body.countryCode : ''
  var phone_type = req.body.phone_type ? req.body.phone_type : ''
  var app_version = req.body.app_version ? req.body.app_version : ''
  var lastOrderFrom = await order.findOne({ include: [{ model: bar }], where: { userID:req.body.userID }, order: [["id","desc"]]})
  try {
    var emailContent =
      '<p><strong>Country Code</strong> : ' + countryCode + '</p>'+
      '<p><strong>Name</strong> : ' + name + '</p>'+
      '<p><strong>Customer ID</strong> : ' + userID + '</p>'+
      '<p><strong>Customer Email</strong> : ' + email + '</p>'+
      '<p><strong>Location (last venue ordered from)</strong> : ' + (lastOrderFrom ? lastOrderFrom.bar.restaurantName : '') + '</p>'+
      '<p><strong>Phone Type</strong> : ' + phone_type + '</p>'+
      '<p><strong>App Version</strong> : ' + app_version + '</p>'+
      '<p><strong>Body (Message)</strong> : ' + message +'</p>';

    let mailOptions = {
      from: '"MyTab" <' + env.fromEmailAdmin + '>',
      to: env.customerEmailTo,
      replyTo: email,
      subject: 'MyTab Customer Support',
      html: emailContent
    }
    
    common.sendEmail(mailOptions)
      .then(response => {
        if (response) {
          res.status(200).json({
            success: 1,
            message: 'Thank you for contacting us, a team member will be in contact shortly'
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'Email Failed. Please try again!'
          })
        }
      })
  } catch (e) {
    res.status(200).json({
      success: 0,
      message: e.message
    })
  }
}

exports.suggestVenue = async (req, res) => {
  var venueName = req.body.venueName
  var suburb = req.body.suburb ? req.body.suburb : ''
  var state = req.body.state ? req.body.state : ''
  var comments = req.body.comments ? req.body.comments : ''
  var customerName = req.body.customerName ? req.body.customerName : ''
  var customerEmail = req.body.customerEmail ? req.body.customerEmail : ''
  try {
    var emailContent =
      '<p><strong>Venue Name</strong> : ' + venueName + '</p>'+
      '<p><strong>Venue Suburb</strong> : ' + suburb + '</p>'+
      '<p><strong>State</strong> : ' + state + '</p>'+
      '<p><strong>Comments</strong> : ' + comments + '</p>'+
      '<p><strong>Customer Name</strong> : ' + customerName + '</p>'+
      '<p><strong>Customer Email</strong> : ' + customerEmail + '</p>'

    let mailOptions = {
      from: '"MyTab" <' + env.fromEmailAdmin + '>',
      to: env.customerEmailTo,
      subject: 'MyTab Customer Venue Suggestion',
      html: emailContent
    }

    if(customerEmail != ''){
      mailOptions.replyTo = customerEmail
    }
    
    common.sendEmail(mailOptions)
      .then(response => {
        if (response) {
          res.status(200).json({
            success: 1,
            message: 'Thank you for your venue suggestion'
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'Email Failed. Please try again!'
          })
        }
      })
  } catch (e) {
    res.status(200).json({
      success: 0,
      message: e.message
    })
  }
}

exports.favVenue = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const userID = sessionData.userID
    const { barID } = req.body;

    let isAlreadyFav = await userFavVenue.findOne({ where: { userID, barID }, raw: true });
    if (!isAlreadyFav) {
      await userFavVenue.create({ userID, barID });
      return res.status(200).json({
        success: 1,
        message: "Success"
      })
    }

    userFavVenue.destroy({ where: { id: isAlreadyFav.id } })
    return res.status(200).json({
      success: 1,
      message: "Success"
    })
  } catch (error) {
    res.status(500).json({
      success: 0,
      message: error.message
    })
  }
}

exports.favVenueList = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const userID = sessionData.userID
    var { search, latitude, longitude } = req.body;

    let selectedServiceType = req.body.serviceType ? req.body.serviceType : 'BOTH';

    let page = req.body.page ? req.body.page : 1;
    let per_page = req.body.per_page ? parseInt(req.body.per_page) : 10;

    if(req.body.show_all && req.body.show_all == '1'){
      per_page = ***************;
    }
    let offset = (page - 1) * per_page;
    let limit = per_page;

    if (latitude == '' || longitude == '') {
      latitude = env.latitude
      longitude = env.longitude
    }

    var whereClause = []
    whereClause.push({
      isDeleted: 'No',
      status: 'Active',
      accountVerified: 'Approved',
      restaurantName: {
        [Op.like]: '%' + search + '%'
      },
      [Op.and]: [{ stripeID: { [Op.ne]: null } }, { stripeID: { [Op.ne]: '' } }],
    });

    if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
			whereClause.push({
				[Op.or] : [{ serviceType: selectedServiceType }, { serviceType: 'BOTH' }]
			});
		}

    let currentDay = moment().tz('Australia/Perth').isoWeekday()
    currentDay = currentDay - 1;
    let list = await bar.findAndCountAll({
      where: whereClause,
      attributes: [
        'id',
        'restaurantName',
        'managerName',
        'email',
        'countryCode',
        'mobile',
        'address',
        'latitude',
        'longitude',
        'serviceType',
        'avatar',
        'isVenueServeAlcohol',
        'liquorLicenseNumber',
        [sequelize.literal(`(SELECT IF(OP.isClosed = '0' and CAST('${moment().tz('Australia/Perth').format('HH:mm:ss')}' AS TIME) between CAST(OP.openingHours AS TIME) and CAST(OP.closingHours AS TIME), 1, 0)isActive FROM operating_hours as OP WHERE bar.id = OP.barID AND OP.weekDay = ${currentDay} HAVING isActive = 1 LIMIT 1)`), 'operatingFlag'],
        [sequelize.literal("coalesce(waitTimeDrink, 0)"), "waitTimeDrink"],
        [sequelize.literal("coalesce(waitTimeFood, 0)"), "waitTimeFood"],
        [sequelize.literal("ROUND(" + env.DISVAL + " * acos(cos(radians(" + latitude + ")) * cos(radians(latitude)) * cos(radians(longitude) - radians(" + longitude + ")) + sin(radians(" + latitude + ")) * sin(radians(latitude))), 2)"), 'distance'],
        [sequelize.literal("'" + env.DISTEXT + "'"), 'distance_ext'],
        [sequelize.literal("(SELECT COUNT(id) from orders WHERE orders.paymentStatus = 'received' AND orders.barID=bar.id AND orders.isDeleted='No')"), "totalOrders"]
      ],
      include: [
        {
          model: userFavVenue,
          attributes: ["id", "userID", "barID"],
          where: { userID: userID },
          distinct: true,
          required:true
        },
        {
          model: operatingHours,
          attributes: [
            'id',
            'weekDay',
            'openingHours',
            'closingHours',
            'isClosed'
          ],
          required: false,
          where: { isClosed: 0 }
        }
      ],
      // having:{
      //   distance: { $lt: 5 }
      // },
      distinct: true,
      order: [sequelize.literal('operatingFlag DESC'),sequelize.literal('distance ASC')],
      offset: offset,
      limit: limit
    })
    if (list.count > 0) {
      list.rows.forEach((element, index) => {
        list.rows[index].dataValues.isOpen = commonFunction.checkBarIsOpen(element.operating_hours);
        // if(list.rows[index].dataValues.operatingFlag == '1'){
        //   list.rows[index].dataValues.isOpen = '1';
        // }else{
        //   list.rows[index].dataValues.isOpen = '0';
        // }
      });
      // SORTING BY OPENED VENUEs
      // list.sort((a, b) => b.dataValues.bar.dataValues.isOpen - a.dataValues.bar.dataValues.isOpen)
    }
    let resArray = {}
    const cartData = await cartItems.findOne({
      where: {
        userID: userID
      },
      include: [
        {
          model: bar,
          attributes: [
            'id',
            'restaurantName',
            'avatar',
          ]
        }
      ]
    })

    resArray.barlist = list.rows
    resArray.count = list.count
    resArray.cartData = {};
    resArray.cartData.cartItemAvailable = (cartData) ? 'Yes' : 'No'
    resArray.cartData.restaurantName = (cartData) ? cartData.bar.restaurantName : ''
    resArray.cartData.barID = (cartData) ? cartData.bar.id : ''
    resArray.cartData.cartServiceType = (cartData) ? cartData.cartServiceType : ''
    resArray.cartData.avatar = (cartData) ? cartData.bar.avatar : ''
    res.status(200).json({
      success: list.count > 0 ? 1 : 0, // NOT REQUIRED BUT AS PER APP REQUIREMENT
      message: list.count > 0 ? "Results Found" : "No Results Found.",
      data: resArray
    })
  } catch (error) {
    console.log(error);
    res.status(500).json({
      success: 0, // NOT REQUIRED BUT AS PER APP REQUIREMENT
      message: error.message
    })
  }
}

exports.addLocation = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const userID = sessionData.userID
    const { latitude, longitude, address, city, state, pinCode } = req.body;
    await userLocation.create({ userId: userID, latitude, longitude, address, city, state, pinCode })
    return res.status(200).json({
      success: 1,
      message: "Success"
    })
  } catch (error) {
    res.status(500).json({
      success: 0,
      message: error.message
    })
  }
}