const Sequelize = require('sequelize')
const bar = require('./bar')
const barOpeningHours = require('./barOpeningHours')

const barOpeningHoursUTC = sequelize.define(
	'bar_opening_hours_utc',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "bar",
				key: "id"
			}
		},
		barOpeningHoursID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: 'bar_opening_hours',
				key: 'id'
			}
		},
		weekDay: Sequelize.SMALLINT,
		openingHours: Sequelize.TIME,
		closingHours: Sequelize.TIME,
		isClosed: Sequelize.BOOLEAN,
		createdAt: Sequelize.DATE,
		updatedAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false
	}
)

barOpeningHoursUTC.belongsTo(bar, { foreignKey: 'barID' })
barOpeningHoursUTC.belongsTo(barOpeningHours, {foreignKey: 'barOpeningHoursID', targetKey: 'id'});
bar.hasMany(barOpeningHoursUTC, { foreignKey: 'barID' })


module.exports = barOpeningHoursUTC