var Sequelize = require('sequelize')
var cartItems = require('./cartItems')
var productVariants = require('./productVariants')

var cart_item_variants = sequelize.define(
  'cart_item_variants',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    cartItemID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "cartItems",
        key: "id"
      }
    },
    productVariantsID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "productVariants",
        key: "id"
      }
    },
    price: Sequelize.FLOAT,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

cart_item_variants.belongsTo(productVariants, { foreignKey: 'productVariantsID' })

cartItems.hasMany(cart_item_variants, { foreignKey: 'cartItemID' })
cart_item_variants.belongsTo(cartItems, { foreignKey: 'cartItemID' })

module.exports = cart_item_variants
