// If Client asks to implement taxes, product based or subcategory based then use this model and make a tabel in PhpMyAdmin according to it.
// Before that, this model is of no use, but implemented for future.

// --------------------------------- *************NO USE**************** --------------------------------------------
// const Sequelize = require('sequelize');
// const bar = require('./bar');
// const product = require('./product');
// const tax = require('./tax');

// const taxProductIds = sequelize.define(
// 	'taxProductIds',
// 	{
// 		id: {
// 			type: Sequelize.BIGINT,
// 			autoIncrement: true,
// 			primaryKey: true
// 		},
// 		taxID: {
// 			type: Sequelize.INTEGER,
// 			allowNull: true,
// 			references: {
// 				model: "tax",
// 				key: "id"
// 			},
// 		},
// 		productID: {
// 			type: Sequelize.INTEGER,
// 			allowNull: true,
// 			references: {
// 				model: "product",
// 				key: "id"
// 			},
// 		},
// 	},
// 	{
// 		freezeTableName: true,
// 		timestamps: false,
// 	},
// );

// taxProductIds.belongsTo(tax, { foreignKey: "taxID" })
// tax.hasMany(taxProductIds, { foreignKey: "taxID" })

// taxProductIds.belongsTo(product, { foreignKey: "productID" })
// product.hasMany(taxProductIds, { foreignKey: "productID" })

// module.exports = taxProductIds
// --------------------------------- *************NO USE**************** --------------------------------------------

//  If Client asks to implement taxes, product based or subcategory based then use this model and make a tabel in PhpMyAdmin according to it.
// Before that, this model is of no use, but implemented for future.