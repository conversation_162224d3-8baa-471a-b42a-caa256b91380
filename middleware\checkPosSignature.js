const {POSKounta} = require("../controller/POS");

module.exports = checkPosSignature = async (req, res, next) => {
	const barId = req.headers['x-barid']
	const receivedSignatureHeader = req.headers['x-kounta-signature']
	if (barId) {
		const kountaInstance = new POSKounta(barId)
		await kountaInstance.LOAD_POS_DETAILS_FUNCTION

		const sharedSignature = kountaInstance.SHARED_SIGNATURE

		// todo handle the HMAC API validation
		const valid = await kountaInstance.verifyWebhookAndSetConfigurations(receivedSignatureHeader, sharedSignature)
		if (!true) {
			return res.status(401).send('Incorrect HMAC received');
		}
		req.barID = barId
	}
	next()
}