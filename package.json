{"name": "mytab", "version": "1.0.0", "description": "MyTab", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "keywords": ["MyTab", "API"], "maintainers": ["<PERSON><PERSON><PERSON><PERSON>"], "author": "Niral", "license": "ISC", "dependencies": {"apn": "^2.2.0", "aws-sdk": "^2.1692.0", "axios": "^0.21.1", "bcrypt": "^4.0.1", "cron": "^1.7.1", "csv-writer": "^1.6.0", "dotenv": "^7.0.0", "exceljs": "^4.3.0", "express": "^4.16.4", "express-actuator": "^1.8.2", "fcm-notification": "^2.0.0", "google-auth-library": "^9.15.1", "jsonwebtoken": "^8.5.1", "md5": "^2.2.1", "moment": "^2.27.0", "morgan": "^1.9.1", "multer": "^1.4.1", "multer-s3": "^2.9.0", "mysql2": "^1.6.5", "node-cron": "^3.0.2", "node-fetch": "^2.6.1", "nodemailer": "^6.4.10", "nodemon": "^1.18.10", "querystring": "^0.2.1", "rand-token": "^0.4.0", "sequelize": "^4.43.1", "stripe": "^11.11.0", "taxjar": "^2.2.2", "twilio": "^3.46.0"}}