const express = require('express')
const router = express()
const multer = require('multer')

const checkAuth = require('../middleware/barCheckAuth')
const pickupLocation = require('../controller/pickupLocation')

var upload = multer({})

router.post('/list', upload.array(), checkAuth, pickupLocation.list)
router.post('/add', upload.array(), checkAuth, pickupLocation.add)
router.post('/edit', upload.array(), checkAuth, pickupLocation.edit)
router.post('/delete', upload.array(), checkAuth, pickupLocation.delete)

module.exports = router