var Sequelize = require('sequelize')
var user = require('./user')

var userAccessToken = sequelize.define('user_accesstoken', {
    id: {
        type: Sequelize.BIGINT,
        autoIncrement: true,
        primaryKey: true
    },
    userID: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
            model: "user",
            key: "id"
        }
    },
    accessToken: Sequelize.TEXT,
    loginType: Sequelize.ENUM('NORMAL', 'GOOGLE', 'APPLE', 'FACEBOOK'),
    deviceType: Sequelize.ENUM('ios', 'android'),
    deviceToken: Sequelize.TEXT,
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE
}, {
    freezeTableName: true,
    timestamps: false
})

user.hasMany(userAccessToken, { foreignKey: 'id' })
userAccessToken.belongsTo(user, { foreignKey: 'userID' })

module.exports = userAccessToken