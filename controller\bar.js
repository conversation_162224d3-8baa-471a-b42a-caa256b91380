var Sequelize = require('sequelize')
var multer = require('multer')

const moment = require('moment')
const message = require('../config/message')
const common = require('./common')
const bar = require('../models/bar')
const barOpeningHours = require('../models/barOpeningHours')
const barOpeningHoursUTC = require('../models/barOpeningHoursUTC')
const barSubCategoryOpeningHours = require('../models/barSubCategoryOpeningHours')
const barSubCategoryOpeningHoursUTC = require('../models/barSubCategoryOpeningHoursUTC')
const barSubCategoryWaitTime = require('../models/barSubCategoryWaitTime')
const barSubCategoryWaitTimeUTC = require('../models/barSubCategoryWaitTimeUTC')
const orders = require('../models/orders')
const category = require('../models/category')
const subcategory = require('../models/subCategory')
const pickupLocation = require('../models/pickupLocation')
const productModel = require('../models/product')
const productVariantTypes = require('../models/productVariantTypes')
const productVariants = require('../models/productVariants')
const productExtras = require('../models/productExtras')
const barAccessToken = require('../models/barAccessToken')
const operatingHours = require('../models/operatingHours')
const operatingBarTax = require('../models/operatingBarTax')
const cartItems = require('../models/cartItems')
const barDocs = require('../models/barDocs')
const POSConfigurable = require('./POS')
const POSconfig = require('../models/POSconfig')
const settingModel = require('../models/settings')
const bcrypt = require('bcrypt')
const Op = Sequelize.Op
const itemActiveHours = require('../models/itemActiveHours')
const coupons = require('../models/coupons')
const couponsSubCatIds = require('../models/couponsSubCatIds')// 9. Promo codes - Subheading specific....
const addUpdateRole = require('../models/role')
const csvAndReport = require('../models/csvAndReport')
const excelJS = require("exceljs");
const { QueryTypes } = require('sequelize');
const printer = require('../models/printerConnect')
const printerSubIds = require('../models/printerSubCatIds')
const barPOSMigration = require('../models/barPOSMigration')
const subCategoryWaitTime = require('../models/subCategoryWaitTime')
const tax = require('../models/tax') // 1. & 2. New Taxes Page under Settings....
const fs = require('fs');
var CronJob = require('cron').CronJob;
const sO = require('../config/database')

var env = require('../config/environment')
var jwt = require('jsonwebtoken');
const commonFunction = require('../common/commonFunction')
const docketSubscribe = require('../models/docketSubsription')
const { s3PublicUploadFile } = require('../middleware/awsS3Operations')
const multerMiddleware = require('../middleware/multer');
var stripe = require('stripe')(env.stripe_secret_key)

const sub_category = require('../models/subCategory')
const user = require('../models/user')
const sequelize = require('sequelize')
const pickupLocationSubCategory = require('../models/pickupLocationSubCategory')

exports.changePassword = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    bar
      .findOne({
        where: {
          id: barID
        }
      })
      .then(async barData => {
        if (barData) {
          bcrypt.compare(
            req.body.oldPassword,
            barData.password,
            (err, response) => {
              if (err) {
                res.status(200).send({
                  success: 0,
                  message: "Something went wrong!"
                })
              }
              if (response) {
                bcrypt.hash(req.body.newPassword, 10, (err, hash) => {
                  if (!err) {
                    bar
                      .update(
                        {
                          password: hash
                        },
                        {
                          returning: true,
                          where: {
                            id: barID
                          }
                        }
                      )
                      .then(function () {
                        res.status(200).send({
                          success: 1,
                          message: "Password changed successfully"
                        })
                      })
                  } else {
                    res.status(200).send({
                      success: 0,
                      message: "Something went wrong!"
                    })
                  }
                })
              } else {
                res.status(200).send({
                  success: 0,
                  message: "Invalid Old Password"
                })
              }
            }
          );
        } else {
          res.status(200).send({
            success: 0,
            message: message.bar.enterValidCredential
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

let fileFilter = function (req, file, cb) {
  var allowedMimes = ['image/jpeg', 'image/png'];
  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb({
      success: false,
      message: 'Invalid file type. Only jpg, png image files are allowed.',
      code: 'LIMIT_FILE_TYPE'
    }, false);
  }
};

exports.checkEmail = async (req, res) => {
  try {
    bar
      .findOne({
        attributes: [
          'email',
        ],
        where: {
          email: req.body.email.toLowerCase(),
        }
      })
      .then(barDataResponse => {
        if (barDataResponse && Object.keys(barDataResponse).length > 0) {
          res.status(200).send({
            success: 0,
            message: message.bar.emailExists
          })
        } else {
          res.status(200).send({
            success: 1,
            message: 'success!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.register = async (req, res) => {
  try {
    await multerMiddleware.singleBarPic(req, res, async function (err) {
      // Multer file validation
      if (err && err.code == 'LIMIT_FILE_SIZE') {
        return res.status(500).send({
          success: 0,
          message: 'File Size is too large. Allowed file size is 5MB'
        });
      } else if (err && err.code == 'LIMIT_FILE_TYPE') {
        return res.status(500).send({
          success: 0,
          message: err.message
        });
      }
      if (req.file != undefined) {
        await s3PublicUploadFile(
          req.file,
          env.awsBarFolder +
          req.file.originalname
        );
      }
      if (req.body.isError) {
        res.status(200).send({
          success: 0,
          message: req.body.message
        })
      } else {
        if (!req.body.businessRegisterId) {
          return res.status(200)
            .send({
              success: 0,
              message: "Please enter ACN/ABN number",
              data: {}
            })
        }

        if (!commonFunction.validateABN(req.body.businessRegisterId) && !commonFunction.validateACN(req.body.businessRegisterId)) {
          return res.status(200)
            .send({
              success: 0,
              message: "Please enter valid ACN/ABN number",
              data: {}
            })
        }
        const requestOperatingHours = JSON.parse(req.body.operating_hours ? req.body.operating_hours : '{}')
        if (requestOperatingHours.length !== 7)
          return res.status(200)
            .send({
              success: 0,
              message: "You'll need to fill all the week day's data to proceed.",
              data: {}
            })

        var venueId = req.body.venueId;
        // if (!venueId) {
        //   return res.status(200).json({ success: 0, message: 'Venue id not found.', data: {} });
        // }
        if (venueId !== '-1') {
          let checkVenueExist = await bar.count({ where: { venueId, isDeleted: 'No' } });
          if (checkVenueExist > 0) {
            return res.status(200).json({ success: 0, message: 'This venue id is already registered.', data: {} });
          }
        }
        bar
          .findOne({
            where: {
              email: req.body.email.toLowerCase(),
              isDeleted: 'No'
            }
          })
          .then(barDataResponse => {
            if (barDataResponse && Object.keys(barDataResponse).length > 0) {
              res.status(200).send({
                success: 0,
                message: message.bar.emailExists
              })
            } else {
              bcrypt.hash(req.body.password, 10, async (err, hash) => {
                if (!err) {
                  var avatar = '';
                  if (req.file != undefined) {
                    avatar = req.file.originalname
                    // for (var i = 0; i < req.files.length; i++) {
                    //   if (req.files[i].fieldname == 'avatar') {
                    //   }
                    // }
                  }
                  // const transaction = await sequelize.transaction();
                  var barData = await bar
                    .create({
                      restaurantName: req.body.restaurantName,
                      managerName: req.body.managerName,
                      address: req.body.address,
                      latitude: req.body.latitude,
                      longitude: req.body.longitude,
                      email: req.body.email.toLowerCase(),
                      mobile: req.body.mobile,
                      countryCode: req.body.countryCode,
                      password: hash,
                      avatar: avatar,
                      serviceType: req.body.service_type ? req.body.service_type : 'PICKUP',
                      liquorLicenseNumber: req.body.liquorLicenseNumber ? req.body.liquorLicenseNumber : null,
                      venueId: venueId,
                      isVenueServeAlcohol: req.body.isVenueServeAlcohol,
                      createdAt: new Date(),
                      businessRegisterId: req.body.businessRegisterId,
                      readPopup: 'Yes',
                    });

                  await settingModel.create({
                    paramKey: 'Transaction fee',
                    barID: barData.id,
                    paramFix: 0.29,
                    paramVar: 2.90,
                  })

                  const pickupLocationData = await pickupLocation.create({
                    barID: barData.id,
                    address: 'Collect at counter',
                    isDefault: '1',
                  });

                  const whereCondition = {
                    ...(barData.posStatus === 1
                      ? { categoryID: -1 }
                      : { categoryID: { [Op.ne]: -1 } })
                  };

                  const allSubCategories = await subcategory.findAll({
                    where: whereCondition,
                    attributes: ['id'],
                    raw: true
                  });

                  const subCategoryIds = allSubCategories.map((subCat) => subCat.id);

                  const newLinks = subCategoryIds.map((subCatId) => ({
                    barID: barData.id,
                    subCategoryID: subCatId,
                    pickupLocationID: pickupLocationData.id
                  }));

                  await pickupLocationSubCategory.bulkCreate(newLinks);

                  const generateWaitTimeEntries = (records, barID) => {
                    return records.flatMap(record =>
                      commonFunction.timeChunkArray(record.openingHours, record.closingHours, '60')
                        .map(({ startTime, endTime }) => ({
                          barID,
                          subCategoryID: record.subCategoryID,
                          barSubCategoryOpeningHoursID: record.barSubCategoryOpeningHoursID || record.id,
                          weekDay: record.weekDay,
                          waitTime: '00:10:00',
                          startTime,
                          endTime
                        }))
                    );
                  };

                  const processOpeningHours = async (requestOperatingHours, barID, timezone, subCategoryIds) => {
                    const barHoursEntries = [];
                    const utcEntriesAll = [];
                    const subCategoryEntriesAll = [];

                    for (const opHours of requestOperatingHours) {
                      const localWeekDay = parseInt(opHours.weekDay, 10);
                      const isClosed = opHours.isClosed;

                      const barHourEntry = {
                        openingHours: opHours.openingHours,
                        closingHours: opHours.closingHours, 
                        weekDay: localWeekDay,
                        isClosed,
                        barID
                      };
                      barHoursEntries.push(barHourEntry);

                      const utcEntries = commonFunction.convertOpeningHours(barHourEntry, timezone, localWeekDay)
                        .map(e => ({
                          ...e,
                          barID,
                          isClosed
                        }));
                      utcEntriesAll.push(...utcEntries);

                      const subEntries = subCategoryIds.map(subCategoryId => ({
                        openingHours: opHours.openingHours,
                        closingHours: opHours.closingHours,
                        weekDay: localWeekDay,
                        subCategoryID: subCategoryId,
                        isClosed,
                        barID
                      }));
                      subCategoryEntriesAll.push(...subEntries);
                    }

                    const newBarHours = await barOpeningHours.bulkCreate(barHoursEntries, { returning: true });

                    for (let i = 0; i < newBarHours.length; i++) {
                      const relatedUtcEntries = utcEntriesAll.filter(e => 
                        e.weekDay === newBarHours[i].weekDay
                      );
                      relatedUtcEntries.forEach(e => {
                        e.barOpeningHoursID = newBarHours[i].id;
                      });
                    }

                    await barOpeningHoursUTC.bulkCreate(utcEntriesAll);

                    const newSubHours = await barSubCategoryOpeningHours.bulkCreate(subCategoryEntriesAll, { returning: true });
                    
                    const utcSubEntries = newSubHours.flatMap(r =>
                      commonFunction.convertOpeningHours(r, timezone, r.weekDay)
                        .map(e => ({
                          ...e,
                          barID,
                          isClosed: r.isClosed,
                          subCategoryID: r.subCategoryID,
                          barSubCategoryOpeningHoursID: r.id
                        }))
                    );
                    await barSubCategoryOpeningHoursUTC.bulkCreate(utcSubEntries);

                    const timChunkEntires = generateWaitTimeEntries(newSubHours, barID);

                    const waitTimeEntries = await barSubCategoryWaitTime.bulkCreate(timChunkEntires, { returning: true });

                    const utcWaitTimeEntries = waitTimeEntries.flatMap(
                      ({
                        id,
                        startTime,
                        endTime,
                        waitTime,
                        weekDay,
                        subCategoryID,
                        barSubCategoryOpeningHoursID
                      }) =>
                        commonFunction.convertOpeningHours({openingHours: startTime, closingHours: endTime}, timezone, weekDay).map((data) => ({
                          ...data,
                          barSubCategoryWaitTimeID: id,
                          startTime: data.openingHours,
                          endTime: data.closingHours,
                          waitTime,
                          subCategoryID,
                          barSubCategoryOpeningHoursID,
                          barID,
                        }))
                    );
                    if (utcWaitTimeEntries.length)
                      await barSubCategoryWaitTimeUTC.bulkCreate(utcWaitTimeEntries);

                    return newBarHours;
                  };

                  if (requestOperatingHours.length === 7) {
                    await processOpeningHours(requestOperatingHours, barData.id, barData.timezone, subCategoryIds);
                  }

                  // let webhookUrl = `http://${req.headers.host}/order/doshii/webhook`;
                  // if (venueId !== "-1") {
                  //   commonFunction.setWebhookForVenue(webhookUrl, venueId, 'order_updated')
                  //     .catch(async (error) => {
                  //       console.log(error);
                  //       // await transaction.rollback();
                  //       res.status(200).send({
                  //         success: 0,
                  //         message: "Something went wrong !"
                  //       });
                  //     });
                  // }
                  // await transaction.commit();
                  jwt.sign({ barID: barData.id, userType: 'bar' }, env.ACCESS_TOKEN_SECRET,
                    (err, token) => {
                      if (err) {
                        res.status(200).send({
                          success: 0,
                          message: "Something went wrong !"
                        })
                      } else {
                        // if (req.files.length > 0) {
                        //   for (var i = 0; i < req.files.length; i++) {
                        //     if (req.files[i].fieldname == 'documents[]') {
                        //       barDocs
                        //         .create({
                        //           barID: barData.id,
                        //           name: req.files[i].key,
                        //           createdAt: new Date()
                        //         })
                        //     }
                        //   }
                        // }
                        barAccessToken
                          .create({
                            accessToken: token,
                            barID: barData.id,
                            deviceType: req.body.deviceType,
                            deviceToken: req.body.deviceToken
                          })
                          .then(createRes => {
                            if (createRes) {
                              var fetchPromises = []
                              fetchPromises.push(fetchBarDetails(barData.id))
                              fetchPromises.push(fetchBarDocs(barData.id))
                              Promise.all(fetchPromises)
                                .then(function (barData) {
                                  const response = barData[0]['value']
                                  response.dataValues.accessToken = token
                                  response.dataValues.passcodeLength = 0
                                  response.dataValues.barDocs = barData[1]['value']
                                  res.status(200).send({
                                    success: 1,
                                    data: response,
                                    message: 'success!'
                                  })
                                })
                                .catch(function (err) {
                                  /* error handling */
                                  console.log(err)
                                  res.status(200).send({
                                    success: 0,
                                    message: message.bar.enterValidCredential
                                  })
                                })
                            } else {
                              res.status(200).send({
                                success: 0,
                                message: "Something went wrong!"
                              })
                            }
                          })
                          .catch(err => {
                            console.log(err);
                            res.status(200).send({
                              success: 0,
                              message: "Something went wrong!"
                            })
                          }
                          );
                      }
                    }
                  );
                } else {
                  res.status(200).send({
                    success: 0,
                    message: "Something went wrong!"
                  })
                }
              })
            }
          })
      }
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

function fetchBarDetails(id) {
  return new Promise(function (resolve, reject) {
    var whereClause = {
      id: id
    }

    bar
      .findOne({
        attributes: [
          'id',
          'restaurantName',
          'managerName',
          'email',
          'countryCode',
          'mobile',
          'address',
          'latitude',
          'longitude',
          'passcode',
          'passcodeStatus',
          'avatar',
          'mobileVerified',
          'badge',
          'accountVerified',
          'status',
          'serviceType',
          'waitTimeServiceType',
          'venueId',
          'businessRegisterId',
          'isVenueServeAlcohol',
          'liquorLicenseNumber'
        ],
        where: whereClause,
      }).then(barFetchResponse => {
        resolve({
          key: 'fetchBar',
          value: barFetchResponse
        })
      })
  })
}

function fetchBarDocs(barID) {
  return new Promise(function (resolve, reject) {
    var whereClause = {
      barID: barID
    }

    barDocs
      .findAll({
        attributes: [
          'id',
          'barID',
          'name'
        ],
        where: whereClause
      })
      .then(response => {
        resolve({
          key: 'fetchBarDocs',
          value: response
        })
      })
  })
}

exports.login = (req, res) => {
  var whereClause = {
    email: req.body.email.toLowerCase(),
    isDeleted: 'No'
  }

  try {
    bar
      .findOne({
        attributes: [
          'id',
          'email',
          'password',
          'status',
          'editorFlag',
          'businessRegisterId'
        ],
        where: whereClause
      })
      .then(async barData => {
        if (barData) {
          bcrypt.compare(
            req.body.password,
            barData.password,
            (err, response) => {
              if (err) {
                res.status(200).send({
                  success: 0,
                  message: "Something went wrong!"
                })
              }
              if (response) {
                if (barData.status == 'Inactive' && barData.dataValues.editorFlag == 'Inactive') {
                  return res.status(200).send({
                    success: 0,
                    message: message.user.userInactive
                  })
                } else {
                  barData = barData.toJSON(); // actually returns a plain object, not a JSON string
                  jwt.sign({ barID: barData.id, userType: 'bar' }, env.ACCESS_TOKEN_SECRET,
                    (err, token) => {
                      if (err) {
                        res.status(200).send({
                          success: 0,
                          message: "Something went wrong!"
                        })
                      } else {
                        barAccessToken
                          .create({
                            // where: {
                            //   barID: barData.id
                            // },
                            // defaults: {
                            barID: barData.id,
                            accessToken: token,
                            deviceType: req.body.deviceType,
                            deviceToken: req.body.deviceToken,
                            createdAt: new Date(),
                            updatedAt: new Date()
                            // }
                          })
                        // .spread((accessToken, created) => {
                        var fetchPromises = []
                        fetchPromises.push(fetchBarDetails(barData.id))
                        fetchPromises.push(fetchBarDocs(barData.id))
                        Promise.all(fetchPromises)
                          .then(function (barDataRes) {
                            const response = barDataRes[0]['value']
                            response.dataValues.accessToken = token
                            response.dataValues.barDocs = barDataRes[1]['value']
                            response.dataValues.passcodeLength = response.dataValues.passcode ? response.dataValues.passcode.length : 0

                            // if (!created) {
                            //   barAccessToken
                            //     .update(
                            //       {
                            //         accessToken: token,
                            //         deviceType: req.body.deviceType,
                            //         deviceToken: req.body.deviceToken
                            //       },
                            //       {
                            //         returning: true,
                            //         where: {
                            //           barID: barData.id
                            //         }
                            //       }
                            //     )
                            // }
                            res.status(200).send({
                              success: 1,
                              data: response,
                              message: 'success!'
                            })
                          })
                          .catch(function (err) {
                            /* error handling */
                            console.log(err)
                            res.status(200).send({
                              success: 0,
                              message: message.bar.enterValidCredential
                            })
                          })
                        // })
                      }
                    });
                }
              } else {
                res.status(200).send({
                  success: 0,
                  message: "Invalid Password"
                })
              }
            }
          );
        } else {
          res.status(200).send({
            success: 0,
            message: message.bar.enterValidCredential
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getProfile = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID;
  try {
    bar
      .findOne({
        attributes: [
          'id',
          'restaurantName',
          'managerName',
          'email',
          'countryCode',
          'mobile',
          'address',
          'latitude',
          'longitude',
          'passcode',
          'passcodeStatus',
          'avatar',
          'mobileVerified',
          'badge',
          'accountVerified',
          'status',
          'serviceType',
          'waitTimeServiceType',
          'venueId',
          'businessRegisterId',
          'isVenueServeAlcohol',
          'liquorLicenseNumber'
        ],
        where: {
          id: barID,
          isDeleted: 'No'
        }
      })
      .then(async barData => {
        if (barData) {
          var fetchPromises = []
          // fetchPromises.push(fetchBarDetails(barData.id))
          fetchPromises.push(fetchBarDocs(barData.id))
          Promise.all(fetchPromises)
            .then(function (barDataRes) {
              // const response = barDataRes[0]['value']
              const response = barData
              response.dataValues.barDocs = barDataRes[0]['value']
              response.dataValues.passcodeLength = response.dataValues.passcode ? response.dataValues.passcode.length : 0

              res.status(200).send({
                success: 1,
                data: response,
                message: 'success!'
              })
            })
            .catch(function (err) {
              /* error handling */
              console.log(err)
              res.status(200).send({
                success: 0,
                message: message.bar.notFound
              })
            })
        } else {
          res.status(200).send({
            success: 0,
            message: message.bar.notFound
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.editProfile = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID
    await multerMiddleware.singleBarPic(req, res, async function (err) {
      // Multer file validation
      if (err && err.code == 'LIMIT_FILE_SIZE') {
        return res.status(500).send({
          success: 0,
          message: 'File Size is too large. Allowed file size is 5MB'
        });
      } else if (err && err.code == 'LIMIT_FILE_TYPE') {
        return res.status(500).send({
          success: 0,
          message: err.message
        });
      }
      if (req.file != undefined) {
        await s3PublicUploadFile(
          req.file,
          env.awsBarFolder +
          req.file.originalname
        );
      }
      if (req.body.isError) {
        res.status(200).send({
          success: 0,
          message: req.body.message
        })
      } else {
        var updateData = {}
        if (req.file != undefined) {
          const barOldData = await bar.find({
            where: {
              id: barID
            }
          })
          if (barOldData.dataValues.avatar != '') {
            common.deletePublicFile(barOldData.dataValues.avatar, '/mytab/bar/original')
          }
          updateData['avatar'] = req.file.originalname
        }

        if (!req.body.businessRegisterId) {
          return res.status(200)
            .send({
              success: 0,
              message: "Please enter ACN/ABN number",
              data: {}
            })
        }

        if (!commonFunction.validateABN(req.body.businessRegisterId) && !commonFunction.validateACN(req.body.businessRegisterId)) {
          return res.status(200)
            .send({
              success: 0,
              message: "Please enter valid ACN/ABN number",
              data: {}
            })
        }

        // let checkBusinessId = await bar.findOne({
        //   where: {
        //     businessRegisterId: req.body.businessRegisterId,
        //     id: {
        //       $ne: barID
        //     },
        //     isDeleted: 'No'
        //   }
        // });

        // if(checkBusinessId) {
        //   res.status(200).send({
        //     success: 0,
        //     message: "Business id already in used"
        //   })
        // }

        const requestServiceType = req.body.service_type ? req.body.service_type : 'PICKUP'
        const confirmChangeFlag = req.body.confirm_change_flag === '1';

        bar
          .findOne({
            where: {
              email: req.body.email,
              id: {
                $ne: barID
              },
              isDeleted: 'No'
            }
          })
          .then(async barDataResponse => {
            if (barDataResponse && Object.keys(barDataResponse).length > 0) {
              res.status(200).send({
                success: 0,
                message: message.bar.emailExists
              })
            } else {
              var barData = await bar.findOne({ where: { id: barID } });
              if (barData.serviceType != requestServiceType) {
                const currentDateTimeUTC = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                const [currentDay, currentTime] = [moment(currentDateTimeUTC).isoWeekday() - 1, moment(currentDateTimeUTC).format('HH:mm:ss')];
                
                const openingHours = await barOpeningHoursUTC.count({
                  attributes: [],
                  where: { 
                    isClosed: 0,
                    weekDay: currentDay,
                    openingHours: { [Op.lte]: currentTime },
                    closingHours: { [Op.gte]: currentTime },
                    barID: barID,
                  }
                });

                let isVenueUpdatable = openingHours > 0 ? 1 : 0;
                if (isVenueUpdatable) {
                  return res.status(200).json({
                    success: 0,
                    message: "Your venue is still open, updating your service type is only available outside your venue's opening hours.",
                    data: { venueOpenFlag: 1 },
                  })
                }

                if (!confirmChangeFlag) {
                  const switchedCount = await orders.count({
                    where: {
                      orderServiceType: requestServiceType.toLowerCase() === 'pickup' ? 'TABLE' : 'PICKUP',
                      barID: barData.id
                    }
                  })
                  if (requestServiceType.toLowerCase() !== 'both' && switchedCount > 0) {
                    return res.status(200)
                      .send({
                        success: 1,
                        message: `You have ${switchedCount} orders in ${barData.serviceType.toLowerCase()} service, these order will not be visible after you switch over to ${requestServiceType.toLowerCase()} service.`,
                        data: { popUpFlag: 1 }
                      })
                  }
                }

              }

              updateData['restaurantName'] = req.body.restaurantName
              updateData['managerName'] = req.body.managerName
              updateData['address'] = req.body.address
              updateData['latitude'] = req.body.latitude
              updateData['longitude'] = req.body.longitude
              updateData['email'] = req.body.email
              updateData['countryCode'] = req.body.countryCode
              updateData['mobile'] = req.body.mobile
              updateData['serviceType'] = requestServiceType
              updateData['businessRegisterId'] = req.body.businessRegisterId
              if (requestServiceType != 'BOTH') {
                if (requestServiceType != barData.waitTimeServiceType) {
                  if (barData.waitTimeServiceType == 'BOTH') {
                    updateData['waitTimeServiceType'] = requestServiceType
                  } else {
                    updateData['waitTimeServiceType'] = null
                  }
                }
              }
              if (req.body.isVenueServeAlcohol && req.body.isVenueServeAlcohol !== '') {
                updateData['isVenueServeAlcohol'] = req.body.isVenueServeAlcohol
                if (req.body.liquorLicenseNumber && req.body.liquorLicenseNumber !== '' && req.body.isVenueServeAlcohol === 'Yes') {
                  updateData['liquorLicenseNumber'] = req.body.liquorLicenseNumber
                } else {
                  updateData['liquorLicenseNumber'] = null
                }
              }

              bar
                .update(updateData, {
                  returning: true,
                  where: {
                    id: barID
                  }
                })
                .then(async barUpdateRes => {
                  bar
                    .findOne({
                      attributes: [
                        'id',
                        'restaurantName',
                        'managerName',
                        'email',
                        'countryCode',
                        'mobile',
                        'address',
                        'latitude',
                        'longitude',
                        'passcode',
                        'passcodeStatus',
                        'avatar',
                        'mobileVerified',
                        'badge',
                        'accountVerified',
                        'status',
                        'serviceType',
                        'venueId',
                        'businessRegisterId',
                        'isVenueServeAlcohol',
                        'liquorLicenseNumber',
                      ],
                      where: {
                        id: barID,
                        isDeleted: 'No'
                      }
                    })
                    .then(barData => {
                      if (barData) {
                        res.status(200).send({
                          success: 1,
                          message: 'Profile updated successfully',
                          data: barData
                        })
                      } else {
                        res.status(200).json({
                          success: 0,
                          message: 'error!',
                        })
                      }
                    })
                }).error(function (err) {
                  res.status(200).json({
                    success: 0,
                    message: err.message,
                  })
                })
            }
          })
      }
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateDeviceToken = async (req, res) => {
  try {
    barAccessToken
      .findOne({
        where: {
          accessToken: req.headers.accesstoken,
        }
      })
      .then(async barAccessTokenData => {
        if (barAccessTokenData) {
          barAccessToken
            .update(
              {
                deviceToken: req.body.deviceToken,
                deviceType: req.body.deviceType,
                updatedAt: new Date()
              },
              {
                returning: true,
                where: {
                  accessToken: req.headers.accessToken,
                }
              }
            )
            .then(function () {
              res.status(200).send({
                success: 1,
                message: 'Device token has been updated successfully'
              })
            })
        } else {
          res.status(200).send({
            success: 0,
            message: "Invalid access token"
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.readPopupMessage = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID
    bar
      .findOne({
        where: {
          id: barID,
          readPopup: 'No'
        }
      })
      .then(async barData => {
        if (barData) {
          bar
            .update(
              {
                readPopup: 'Yes',
              },
              {
                where: {
                  id: barID,
                }
              }
            )
            .then(function () {
              let popupMessage = { "popupMessage": "We’ve updated our Privacy Policy and Terms & Conditions to better serve you and enhance transparency. You can find the updates on our website: https://mytabinfo.com For questions, email <NAME_EMAIL>, Thanks" }
              res.status(200).send({
                success: 1,
                data: popupMessage,
                message: message.bar.messageRead
              })
            })
        } else {
          res.status(200).send({
            success: 0,
            message: message.bar.alreadyViewPopup
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateMobile = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    bar
      .findOne({
        where: {
          id: barID,
          mobile: req.body.oldMobile,
          countryCode: req.body.oldCountryCode,
        }
      })
      .then(async barData => {
        if (barData) {
          bar
            .update(
              {
                mobile: req.body.mobile,
                countryCode: req.body.countryCode,
                updatedAt: new Date()
              },
              {
                returning: true,
                where: {
                  id: barData.id
                }
              }
            )
            .then(function () {
              res.status(200).send({
                success: 1,
                message: 'Your account mobile number has been successfully updated'
              })
            })
        } else {
          res.status(200).send({
            success: 0,
            message: "Please enter the correct verification code to update your mobile number"
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateWaitTimeServiceType = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    bar
      .findOne({
        where: {
          id: barID,
          isDeleted: 'No'
        }
      }).then(async response => {
        if (response) {
          response.update({ waitTimeServiceType: req.body.waitTimeServiceType ? req.body.waitTimeServiceType : null })
            .then(async result => {
              res.status(200).json({
                success: 1,
                message: 'Profile updated successfully',
                data: { waitTimeServiceType: result.dataValues.waitTimeServiceType }
              })
            }).error(function (err) {
              res.status(200).json({
                success: 0,
                message: err.message,
              })
            })
        } else {
          res.status(200).send({
            success: 0,
            message: 'Error in updating record!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error in updating record!'
    })
  }
}

exports.resetPassword = async (req, res) => {
  bar
    .findOne({
      attributes: ['id', 'email'],
      where: {
        email: req.body.email,
        isDeleted: 'No'
      }
    })
    .then(async barFetchResponse => {
      if (barFetchResponse) {
        try {
          var randtoken = require('rand-token').generator()
          var sixDigitCode = randtoken.generate(6, '0123456789')

          var emailContent = 'Your reset password code is: ' + sixDigitCode

          let mailOptions = {
            from: '"MyTab" <' + env.fromEmailAdmin + '>',
            to: req.body.email,
            subject: 'Password Reset Code',
            html: emailContent
          }

          const emailRes = await common.sendEmail(mailOptions)
            .then(response => {
              if (response) {
                bar
                  .update(
                    {
                      resetPasswordCode: sixDigitCode,
                      updatedAt: new Date()
                    },
                    {
                      returning: true,
                      where: {
                        id: barFetchResponse.id
                      }
                    }
                  )
                  .then(function () {
                    res.status(200).json({
                      success: 1,
                      message: 'Email sent successfully',
                      data: {
                        email: barFetchResponse.email
                      }
                    })
                  })
              }
            })
        } catch (e) {
          res.status(200).json({
            success: 0,
            message: e.message
          })
        }
      } else {
        res.status(200).send({
          success: 0,
          message: message.landing.emailNotRegistered,
          data: {}
        })
      }
    })
}

exports.verifyResetPasswordCode = (req, res) => {
  try {
    bar
      .findOne({
        attributes: ['id', 'email', 'resetPasswordCode'],
        where: {
          email: req.body.email,
          resetPasswordCode: req.body.resetPasswordCode,
          isDeleted: 'No'
        }
      })
      .then(barFetchResponse => {
        if (barFetchResponse) {
          res.status(200).json({
            success: 1,
            message: message.user.otpVerified,
            data: barFetchResponse
          })
        } else {
          res.status(200).json({
            success: 0,
            message: message.user.otpVerificationFailed,
            data: {}
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updatePassword = async (req, res) => {
  try {
    bar
      .findOne({
        where: {
          id: req.body.id,
          resetPasswordCode: req.body.resetPasswordCode,
        }
      })
      .then(async barData => {
        if (barData) {
          bcrypt.hash(req.body.password, 10, (err, hash) => {
            if (!err) {
              bar
                .update(
                  {
                    password: hash,
                    resetPasswordCode: '',
                    updatedAt: new Date()
                  },
                  {
                    returning: true,
                    where: {
                      id: barData.id
                    }
                  }
                )
                .then(function () {
                  res.status(200).send({
                    success: 1,
                    message: 'Password updated successfully'
                  })
                })
            } else {
              res.status(200).send({
                success: 0,
                message: "Something went wrong!"
              })
            }
          })
        } else {
          res.status(200).send({
            success: 0,
            message: 'updatePassword: Failed!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.logout = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID
    barAccessToken
      .destroy({
        where: {
          barID: barID,
          accessToken: req.headers.accesstoken
        }
      })
      .then(function () {
        res.status(200).json({
          success: 1,
          message: message.landing.logOut
        })
      })
      .error(function (err) {
        res.status(200).json({
          success: 0,
          message: 'LogOut Operation Failed!'
        })
      })
  } catch (error) {
    console.log(error)
    res.status(200).json({
      success: 0,
      message: 'LogOut Operation Failed!'
    })
  }
}

exports.delete = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID
    // bar
    //   .update(
    //     {
    //       isDeleted: 'Yes',
    //       updatedAt: new Date()
    //     },
    //     {
    //       returning: true,
    //       where: {
    //         id: barID
    //       }
    //     }
    //   )
    barPOSMigration.destroy({ where: { barID: barID } })
    operatingHours.destroy({ where: { barID: barID } })
    barOpeningHours.destroy({ where: { barID: barID } })
    barOpeningHoursUTC.destroy({ where: { barID: barID } })
    barSubCategoryOpeningHours.destroy({ where: { barID: barID } })
    barSubCategoryOpeningHoursUTC.destroy({ where: { barID: barID } })
    barSubCategoryWaitTime.destroy({ where: { barID: barID } })
    barSubCategoryWaitTimeUTC.destroy({ where: { barID: barID } })
    pickupLocationSubCategory.destroy({ where: { barID: barID } })
    bar.destroy({ where: { id: barID } })
      .then(function () {
        barAccessToken
          .destroy({
            where: {
              barID: barID
            }
          }).then(function () {
            res.status(200).json({
              success: 1,
              message: 'Your account has successfully been deleted'
            })
          })
      })
  } catch (error) {
    res.status(200).json({
      success: 0,
      message: 'Bar: Delete Operation Failed!'
    })
  }
}

exports.getBars = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    var { search, latitude, longitude } = req.body
    if (latitude == '' || longitude == '') {
      latitude = env.latitude
      longitude = env.longitude
    }

    let selectedServiceType = req.body.serviceType ? req.body.serviceType : 'BOTH';
    let distanceKm = req.body.distanceKm ? parseFloat(req.body.distanceKm) : ***************;

    let page = req.body.page ? req.body.page : 1;
    let per_page = req.body.per_page ? parseInt(req.body.per_page) : 10;

    if (req.body.show_all && req.body.show_all == '1') {
      per_page = ***************;
    }

    let offset = (page - 1) * per_page;
    let limit = per_page;

    var whereClause = []
    whereClause.push({
      isDeleted: 'No',
      status: 'Active',
      accountVerified: 'Approved',
      restaurantName: {
        [Op.like]: '%' + search + '%'
      },
      [Op.and]: [{ stripeID: { [Op.ne]: null } }, { stripeID: { [Op.ne]: '' } }],
    });

    if (selectedServiceType && selectedServiceType.toLowerCase() !== 'both') {
      whereClause.push({
        [Op.or]: [{ serviceType: selectedServiceType }, { serviceType: 'BOTH' }]
      });
    }

    let currentDay = moment().tz('Australia/Perth').isoWeekday()
    currentDay = currentDay - 1;

    let queryOptions = {
      attributes: [
        'id',
        'restaurantName',
        'managerName',
        'email',
        'countryCode',
        'mobile',
        'address',
        'latitude',
        'longitude',
        'serviceType',
        'avatar',
        'isVenueServeAlcohol',
        'liquorLicenseNumber',
        [
          sequelize.literal(`
            CASE 
              WHEN EXISTS (
                SELECT 1
                FROM operating_hours AS OP
                WHERE OP.barID = bar.id
                  AND OP.weekDay = ${currentDay}
                  AND OP.isClosed = 0
                  AND '${moment().tz('Australia/Perth').format('HH:mm:ss')}' 
                      BETWEEN OP.openingHours AND OP.closingHours
              )
              THEN 1
              ELSE 0
            END
          `),
          'operatingFlag'
        ],
        [sequelize.literal("coalesce(waitTimeDrink, 0)"), "waitTimeDrink"],
        [sequelize.literal("coalesce(waitTimeFood, 0)"), "waitTimeFood"],
        // 'isVenueServeAlcohol',
        // [sequelize.literal("CASE WHEN (isVenueServeAlcohol = 'Yes') THEN 'Sorry, due to RSA guidelines you must be over 18 to order from this Venue.' ELSE '' END"), 'venueGuidelineMessage'],
        [sequelize.literal("ROUND(" + env.DISVAL + " * acos(cos(radians(" + latitude + ")) * cos(radians(latitude)) * cos(radians(longitude) - radians(" + longitude + ")) + sin(radians(" + latitude + ")) * sin(radians(latitude))), 2)"), 'distance'],
        [sequelize.literal("'" + env.DISTEXT + "'"), 'distance_ext'],
        [
          sequelize.literal(`(
                SELECT EXISTS(
                  SELECT 1 
                  FROM user_fav_venue 
                  WHERE barID = bar.id AND userID = ${userID} 
                  LIMIT 1
                )
              )`),
          "isVenueFavorite",
        ],
        [
          sequelize.literal(`(
              SELECT COUNT(1) 
              FROM orders 
              WHERE orders.paymentStatus = 'received' 
              AND orders.barID = bar.id 
              AND orders.isDeleted = 'No'
            )`),
          "totalOrders",
        ],

      ],
      include: [
        {
          model: operatingHours,
          attributes: [
            'id',
            'weekDay',
            'openingHours',
            'closingHours',
            'isClosed'
          ],
          required: false,
          where: { isClosed: 0 }
        }
      ],
      where: whereClause,
      distinct: true,
      having: {
        distance: { $lt: distanceKm }
      },
    };

    let barCount = await bar.findAll(queryOptions);

    bar
      .findAll({
        ...queryOptions,
        order: [sequelize.literal('operatingFlag DESC'), sequelize.literal('distance ASC')],        
      })
      .then(async response => {
        if (barCount.length > 0) {
          response.forEach((element, index) => {
            element.dataValues.isOpen = commonFunction.checkBarIsOpen(element.operating_hours);
          });
          
          // Sort by isOpen (2 or 1 first), then by distance if needed
          response.sort((a, b) => {
            const openA = a.dataValues.isOpen >= 1 ? 1 : 0;
            const openB = b.dataValues.isOpen >= 1 ? 1 : 0;
          
            // If isOpen values are different, sort descending (2 > 1 > 0)
            if (openA !== openB) {
              return openB - openA;
            }
            return a.dataValues.distance - b.dataValues.distance;
          });

          const paginatedResults = response.slice(offset, offset + limit);

          let resArray = {}
          const [cartData, userData] = await Promise.all([
            cartItems.findOne({
              where: { userID },
              include: [{
                model: bar,
                attributes: ['id', 'restaurantName', 'avatar']
              }]
            }),
            user.findOne({
              where: { id: userID },
              attributes: ['readPopup']
            })
          ]);

          resArray.barlist = paginatedResults
          resArray.count = barCount.length
          resArray.cartData = {};
          resArray.userData = {};
          resArray.cartData.cartItemAvailable = (cartData) ? 'Yes' : 'No'
          resArray.cartData.restaurantName = (cartData) ? cartData.bar.restaurantName : ''
          resArray.cartData.barID = (cartData) ? cartData.bar.id : ''
          resArray.cartData.cartServiceType = (cartData) ? cartData.cartServiceType : ''
          resArray.cartData.avatar = (cartData) ? cartData.bar.avatar : '',
            resArray.userData.readPopup = (userData) ? userData.dataValues.readPopup : '',
            res.status(200).send({
              success: 1,
              message: 'Results Found',
              data: resArray
            })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: {}
          })
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updatePasscodeStatus = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    bar
      .update(
        {
          passcodeStatus: req.body.passcodeStatus,
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: barID
          }
        }
      )
      .then(function () {
        res.json({
          success: 1,
          message: 'success!'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updatePasscode = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    bar
      .update(
        {
          passcode: req.body.passcode,
          updatedAt: new Date()
        },
        {
          returning: true,
          where: {
            id: barID
          }
        }
      )
      .then(function () {
        res.json({
          success: 1,
          message: 'success!'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.changePasscode = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    bar
      .findOne({
        attributes: ['id', 'email', 'passcode'],
        where: {
          id: barID
        }
      })
      .then(barData => {
        if (barData) {
          if (barData.passcode == req.body.oldPasscode) {
            bar
              .update(
                {
                  passcode: req.body.newPasscode,
                  updatedAt: new Date()
                },
                {
                  returning: true,
                  where: {
                    id: barID
                  }
                }
              )
              .then(function () {
                res.status(200).send({
                  success: 1,
                  message: "Passcode changed successfully."
                })
              })
          } else {
            res.status(200).send({
              success: 0,
              message: "Invalid old passcode!"
            })
          }
        } else {
          res.status(200).send({
            success: 0,
            message: 'Bar data not found!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.verifyPasscode = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    bar
      .findOne({
        attributes: ['id', 'email', 'passcode'],
        where: {
          id: barID,
          passcode: req.body.passcode,
          passcodeStatus: 'Active',
          isDeleted: 'No'
        }
      })
      .then(barFetchResponse => {
        if (barFetchResponse) {
          res.status(200).json({
            success: 1,
            message: 'Passcode verify successfully!'
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'Please enter valid passcode!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.contactUs = async (req, res) => {
  var name = req.body.name
  var email = req.body.email
  var message = req.body.message
  var userID = req.body.userID ? req.body.userID : ''
  var countryCode = req.body.countryCode ? req.body.countryCode : ''
  var address = req.body.address ? req.body.address : ''
  var app_version = req.body.app_version ? req.body.app_version : ''
  try {
    var emailContent =
      '<p><strong>Country Code</strong> : ' + countryCode + '</p>' +
      '<p><strong>Name</strong> : ' + name + '</p>' +
      '<p><strong>Venue ID</strong> : ' + userID + '</p>' +
      '<p><strong>Email</strong> : ' + email + '</p>' +
      '<p><strong>Location (venue address)</strong> : ' + address + '</p>' +
      '<p><strong>App Version</strong> : ' + app_version + '</p>' +
      '<p><strong>Body (Message)</strong> : ' + message + '</p>';

    let mailOptions = {
      from: '"MyTab" <' + env.fromEmailVenueAdmin + '>',
      to: env.barEmailTo,
      replyTo: email,
      subject: 'MyTab Venue Support',
      html: emailContent
    }

    await common.sendEmail(mailOptions)
      .then(response => {
        if (response) {
          res.status(200).json({
            success: 1,
            message: 'Thank you for contacting us, a team member will be in contact shortly'
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'Email Failed. Please try again!'
          })
        }
      })
  } catch (e) {
    res.status(200).json({
      success: 0,
      message: e.message
    })
  }
}

exports.validateVenueId = async (req, res) => {
  try {
    var venueId = req.body.venueId;
    if (!venueId) {
      return res.status(200).json({ success: 0, message: 'Venue id not found.', data: {} });
    }
    // let result = await commonFunction.subscribeVenue(venueId);
    // console.log(result);
    let isValid = await commonFunction.isVenueIdValid(venueId);
    if (isValid) {
      return res.status(200).json({ success: 1, message: 'Venue id is valid.', data: {} });
    }
    return res.status(200).json({ success: 0, message: 'Venue id is not valid.', data: {} });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.syncMenuItems = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID;
    var barData = await bar.findOne({
      where: { id: barID },
      attributes: ['attachedPosConfig'],
      raw: true
    });
    if (!barData) {
      return res.status(200).json({ success: 0, message: 'Something went wrong with venue details.', data: {} });
    }
    if (!barData.attachedPosConfig) {
      return res.status(200).json({ success: 0, message: 'Could not find POS attached to your venue.', data: {} });
    }
    var categoryData = await category.findOne({
      where: { name: 'kitchen', isDeleted: 'No' },
      attributes: ['id'],
      raw: true
    });
    if (!categoryData) {
      return res.status(200).json({ success: 0, message: 'Category not found.', data: {} });
    }

    let posConfigured = new POSConfigurable.posUtils(barID);
    let response = await posConfigured.syncPosSystem()
    return res.status(200).json(response)

    // PURPOSE OF BELOW CODE - UNKNOWN
    // let location = await commonFunction.getDoshiVenueDetails(barData.venueId);
    // var locationAddress = location.addressLine1 == null ? location.addressLine2 : location.addressLine1;
    // if ((!parseInt(location.latitude) && !parseInt(location.longitude)) || !locationAddress) {
    //   return res.status(200).json({ success: 0, message: 'Bar pickup location not found.', data: {} });
    // }
    // create pickup location
    // var locationData = await pickupLocation.findOrCreate({ where: { barID: barID, latitude: location.latitude, longitude: location.longitude }, defaults: { address: locationAddress, createdAt: new Date(), updatedAt: new Date() } });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.updateVenueId = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID;
    var venueId = req.body.venueId;
    if (!venueId) {
      return res.status(200).json({ success: 0, message: 'Venue id not found.', data: {} });
    }
    var barData = await bar.findOne({
      where: { id: barID }
    });
    if (barData.venueId == venueId) {
      return res.status(200).json({ success: 0, message: 'Venue Id is already registered with this bar!', data: {} });
    }
    var checkbarExists = await bar.findOne({
      where: { venueId, id: { [Op.ne]: barID } },
      attributes: ['id']
    });
    if (checkbarExists != null) {
      return res.status(200).json({ success: 0, message: 'Entered Venue id is already connected to another account.', data: {} });
    }
    if (barData.venueId) {
      // subscribe venue in doshii
      await commonFunction.subscribeVenue(venueId);
      let isValid = await commonFunction.isVenueIdValid(venueId);
      if (!isValid) {
        return res.status(200).json({ success: 0, message: 'Venue id is not valid.', data: {} });
      }

      // set webhook for new venue id
      let webhookUrl = `http://${req.headers.host}/order/doshii/webhook`;
      await commonFunction.setWebhookForVenue(webhookUrl, venueId, 'order_updated')
      // unsubscribe venue id that are stored in database
      await commonFunction.unsubscribeVenue(barData.venueId);
      // remove webhook for venue that are stored in database
      await commonFunction.unsetWebhookForVenue(barData.venueId, 'order_updated');

      var productsAssociated = await productModel.findAll({
        where: {
          isDeleted: 'No',
          barID: barID,
          posID: { [Op.ne]: null }
        },
        include: [{
          model: productExtras,
          as: 'product_extras',
          required: false,
          where: {
            isDeleted: 'No'
          }
        }]
      });
      for (var productDetail of productsAssociated) {
        for (let productExtraDetail of productDetail.product_extras) {
          productExtraDetail.update({ isDeleted: 'Yes' });
        }
        productDetail.update({ isDeleted: 'Yes' });
      }
    } else {
      await commonFunction.subscribeVenue(venueId);
      let isValid = await commonFunction.isVenueIdValid(venueId);
      if (!isValid) {
        return res.status(200).json({ success: 0, message: 'Venue id is not valid.', data: {} });
      }
      // set webhook for new venue id
      let webhookUrl = `http://${req.headers.host}/order/doshii/webhook`;
      await commonFunction.setWebhookForVenue(webhookUrl, venueId, 'order_updated');
    }
    barData.update({ venueId });

    // sync menu items with new venue Id
    var categoryData = await category.findOne({
      where: { name: 'kitchen', isDeleted: 'No' },
      attributes: ['id'],
      raw: true
    });
    if (!categoryData) {
      return res.status(200).json({ success: 0, message: 'Category not found.', data: {} });
    }
    let result = await commonFunction.getDoshiiMenuItems(venueId);
    if (typeof result == 'object' && result.hasOwnProperty('products')) {
      for (let product of result.products) {
        if (isNaN(product.posId)) continue;
        for (let sub_category of product.tags) {
          sub_category = commonFunction.titleCase(sub_category);
          // create sub category
          var subCategoryData = await subcategory.findOrCreate({ where: { name: sub_category, categoryID: categoryData.id }, defaults: { name: sub_category, categoryID: categoryData.id, createdAt: new Date(), updatedAt: new Date() } });
        }

        let item = {
          barID: barID,
          categoryID: categoryData.id,
          subCategoryID: subCategoryData[0].id,
          name: product.name,
          description: product.description,
          price: (product.unitPrice / 100).toFixed(2),
          // pickupLocationID: locationData[0].id,
          posID: product.posId,
          createdAt: new Date(), updatedAt: new Date()
        };
        // create or update product
        // var productData = await productModel.findOrCreate({ where: { name: product.name, categoryID: categoryData.id, subCategoryID: subCategoryData[0].id }, defaults: item });
        var productData = await productModel.findOrCreate({ where: { barID: barID, posID: product.posId, isDeleted: 'No' }, defaults: item });
        if (productData[0].isNewRecord == false) {
          productData[0].update({ name: product.name, price: (product.unitPrice / 100).toFixed(2), subCategoryID: subCategoryData[0].id, description: product.description, posID: product.posId, updatedAt: new Date() });
        }

        // create or update product variant
        if (product.options.length > 0) {
          for (let option of product.options) {
            for (let variant of option.variants) {
              if (isNaN(variant.posId)) continue;
              let product_variants = {
                productID: productData[0].id,
                variantType: variant.name,
                price: (variant.price / 100).toFixed(2),
                posID: variant.posId,
                productOptionposID: option.posId,
                productOptionName: option.name,
                createdAt: new Date(), updatedAt: new Date()
              };
              // var productVariantData = await productVariants.findOrCreate({ where: { productID: productData[0].id, posID: variant.posId }, defaults: product_variants });
              // if (productVariantData[0].isNewRecord == false) {
              //   productVariantData[0].update({ variantType: variant.name, price: (variant.price / 100).toFixed(2), posID: variant.posId, productOptionposID: option.posId, productOptionName: option.name, updatedAt: new Date() });
              // }
              var productVariantData = await productExtras.findOrCreate({ where: { productID: productData[0].id, posID: variant.posId }, defaults: product_variants });
              if (productVariantData[0].isNewRecord == false) {
                productVariantData[0].update({ extraItem: variant.name, price: (variant.price / 100).toFixed(2), posID: variant.posId, productOptionposID: option.posId, productOptionName: option.name, updatedAt: new Date() });
              }
            }
          }
        }
      }
    }
    // end sync menu items code

    // reload bar instance
    await barData.reload();

    return res.status(200).json({ success: 1, message: 'Venue Id updated successfully!.', data: barData });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}
/*
exports.updateVenueServeAlchohol = async (req, res) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID;
    console.log(barID);
    var barData = await bar.findOne({
      where: { id: barID }
    });
    if (!barData) {
      return res.status(200).json({ success: 0, message: 'bar not found.', data: {} });
    }
    barData.update({ isVenueServeAlcohol: req.body.isVenueServeAlcohol });
    res.status(200).send({
      success: 1,
      message: "Bar options updated successfully!.",
      data: barData
    });
  } catch (error) {
    console.log(error);
    res.status(200).send({
      success: 0,
      message: 'error!'
    });
  }
}*/

exports.updateWaitTime = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID
    const timeDrink = req.body.wait_time_drink ? req.body.wait_time_drink : null
    const timeFood = req.body.wait_time_food ? req.body.wait_time_food : null

    bar
      .findOne({
        attributes: ['id'],
        where: {
          id: barID,
          isDeleted: 'No'
        }
      })
      .then(barFetchResponse => {
        if (barFetchResponse) {
          try {
            bar
              .update(
                { waitTimeDrink: timeDrink, waitTimeFood: timeFood, updatedAt: new Date() },
                { where: { id: barFetchResponse.id } })
              .then(() => {
                res.status(200)
                  .json({
                    success: 1,
                    message: 'Wait time updated successfully.',
                    data: {}
                  })
              })
              .catch(err =>
                res.status(200)
                  .json({
                    success: 0,
                    message: err
                  }))
          } catch (e) {
            res.status(200)
              .json({
                success: 0,
                message: e.message
              })
          }
        } else {
          res.status(200)
            .send({
              success: 0,
              message: "Cannot find matching venue.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}
exports.getWaitTime = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID

    bar
      .findOne({
        attributes: ['id', 'waitTimeDrink', 'waitTimeFood'],
        where: {
          id: barID,
          isDeleted: 'No'
        }
      })
      .then(barFetchResponse => {
        if (barFetchResponse) {
          res.status(200)
            .json({
              success: 1,
              message: 'Wait times.',
              data: barFetchResponse
            })
        } else {
          res.status(200)
            .send({
              success: 0,
              message: "Cannot find matching venue details.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}


exports.updateServiceTypeFlag = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID
    const requestServiceType = req.body.service_type ? req.body.service_type : 'PICKUP'
    const confirmChangeFlag = req.body.confirm_change_flag === '1';

    bar
      .findOne({
        attributes: [
          'id',
          'serviceType',
          'waitTimeServiceType'
        ],
        where: {
          id: barID,
          isDeleted: 'No'
        }
      })
      .then(async barFetchResponse => {
        if (barFetchResponse.serviceType === requestServiceType) {
          return res.status(200)
            .send({
              success: 0,
              message: `Service Type is already at ${requestServiceType}.`,
              data: {}
            })
        }

        if (!confirmChangeFlag) {
          const switchedCount = await orders.count({
            where: {
              orderServiceType: requestServiceType.toLowerCase() === 'pickup' ? 'TABLE' : 'PICKUP',
              barID: barFetchResponse.id
            }
          })
          if (requestServiceType.toLowerCase() !== 'both' && switchedCount > 0) {
            return res.status(200)
              .send({
                success: 1,
                message: `You have ${switchedCount} orders in ${barFetchResponse.serviceType.toLowerCase()} service, these order will not be visible after you switch over to ${requestServiceType.toLowerCase()} service.`,
                data: { popUpFlag: 1 }
              })
          }
        }
        if (barFetchResponse) {
          if (requestServiceType != 'BOTH') {
            if (requestServiceType != barFetchResponse.waitTimeServiceType) {
              if (barFetchResponse.waitTimeServiceType == 'BOTH') {
                await bar
                  .update({
                    waitTimeServiceType: requestServiceType,
                  }, { where: { id: barFetchResponse.id } })
                  .catch(error => error)
              } else {
                await bar
                  .update({
                    waitTimeServiceType: null,
                  }, { where: { id: barFetchResponse.id } })
                  .catch(error => error)
              }
            }
          }
          await bar
            .update({
              serviceType: requestServiceType,
            }, { where: { id: barFetchResponse.id } })
            .catch(error => error)

          await barFetchResponse.reload()

          var productsAssociated = await productModel.findAll({
            where: {
              isDeleted: 'No',
              isUpdateByUser: 'No',
              barID: barID,
            },
            include: [{
              model: productVariantTypes,
              as: 'productVariantTypes',
              required: false,
              where: {
                isDeleted: 'No',
                isUpdateByUser: 'No',
              }
            }]
          });
          //console.log("productsAssociated", productsAssociated);
          for (var productDetail of productsAssociated) {
            for (let productExtraDetail of productDetail.productVariantTypes) {
              productExtraDetail.update({ serviceType: requestServiceType });
            }
            productDetail.update({ serviceType: requestServiceType });
          }

          res.status(200)
            .json({
              success: 1,
              message: 'Your venue\'s service type is successfully updated.',
              data: barFetchResponse
            })
        } else {
          res.status(200)
            .send({
              success: 0,
              message: "Cannot find matching venue details.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.getVenueOperatingHours = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID

    operatingHours
      .findAll({
        attributes: [
          [sequelize.fn('date_format', sequelize.col('openingHours'), '%k:%i'), 'openingHours'],
          [sequelize.fn('date_format', sequelize.col('closingHours'), '%k:%i'), 'closingHours'],
          'weekDay',
          [Sequelize.literal(`IF(isClosed=1, '1', '0')`), 'isClosed'],
        ],
        where: { barID: barID },
        order: [['weekDay']]
      })
      .then(async barFetchResponse => {
        if (barFetchResponse) {
          res.status(200)
            .json({
              success: 1,
              message: 'Operating hours retrieved successfully.',
              data: barFetchResponse
            })
        } else {
          res.status(200)
            .send({
              success: 1,
              message: "Venue's Operating Hours are missing.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.updateVenueOperatingHours = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID
    const requestOperatingHours = req.body.operating_hours
    if (requestOperatingHours.length !== 7)
      return res.status(200)
        .send({
          success: 0,
          message: "You'll need to fill all the week day's data to proceed.",
          data: {}
        })

    await operatingHours
      .destroy({
        where: {
          barID: barID,
        }
      });
    requestOperatingHours
      .map(opHours => {
        operatingHours
          .create(
            {
              openingHours: opHours.openingHours,
              closingHours: opHours.closingHours,
              weekDay: opHours.weekDay,
              isClosed: opHours.isClosed,
              createdAt: new Date(),
              updatedAt: new Date(),
              barID: barID,
            }
          )
      })
    res.status(200)
      .json({
        success: 1,
        message: 'Operating hours updated successfully.',
        data: {}
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.enableSubCategoryIds = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID
    const subCategoryIDs = req.body.subCategoryIDs ? req.body.subCategoryIDs : null
    barAccessToken
      .findOne({
        where: {
          barID: barID,
          accessToken: req.headers.accesstoken,
        },
      })
      .then(async response => {
        if (response) {
          try {
            barAccessToken
              .update(
                { subCategoryIDs: subCategoryIDs },
                { where: { id: response.dataValues.id } })
              .then(() => {
                res.status(200)
                  .json({
                    success: 1,
                    message: 'Sub Category enabled successfully.',
                    data: {}
                  })
              })
              .catch(err =>
                res.status(200)
                  .json({
                    success: 0,
                    message: err
                  })
              )
          } catch (e) {
            res.status(200)
              .json({
                success: 0,
                message: e.message
              })
          }
        } else {
          res.status(200)
            .send({
              success: 0,
              message: "Cannot find matching record.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.isVenueClosedController = async (req, res, next) => {
  try {
    const openrationgHoursList = await operatingHours.findAll({
      attributes: [
        'weekDay',
        'openingHours',
        'closingHours',
        'isClosed',
      ],
      where: {
        barID: req.body.barID,
        isClosed: 0
      },
    });

    let isVenueClosed = commonFunction.checkBarIsOpen(openrationgHoursList);
    if (!isVenueClosed) {
      return res.status(200).json({
        success: -2,
        message: "Unfortunately, you can not place this order as this venue is closed. Please try again within the venue's opening hours. Thank you!",
      })
    } else {
      next()
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.isVenueClosedControllerV2 = async (req, res, next) => {
  try {
    const currentDateTimeUTC = req.body.currentDateTimeUTC || moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const [currentDay, currentTime] = [moment(currentDateTimeUTC).isoWeekday() - 1, moment(currentDateTimeUTC).format('HH:mm:ss')];
    const venueOpen = await barOpeningHoursUTC.findOne({
      where: { isClosed: 0, weekDay: currentDay, openingHours: { [Op.lte]: currentTime }, closingHours: { [Op.gte]: currentTime }, barID: req.body.barID }
    });
    return venueOpen ? next() : res.status(200).json({ success: -2, message: "Unfortunately, you can not place this order as this venue is closed. Please try again within the venue's opening hours. Thank you!" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: 0, message: 'Something went wrong, please try again' });
  }
}

exports.isVenueUpdatableController = async (req, res, next) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID

    const openrationgHoursList = await operatingHours.findAll({
      attributes: [
        'weekDay',
        'openingHours',
        'closingHours',
        'isClosed',
      ],
      where: {
        barID: barID,
        isClosed: 0
      },
    });

    let isVenueUpdatable = commonFunction.checkBarIsOpen(openrationgHoursList);
    if (isVenueUpdatable) {
      return res.status(200).json({
        success: 0,
        message: "Your venue is still open, updating your service type is only available outside your venue's opening hours.",
        data: { venueOpenFlag: 1 },
      })
    } else {
      next()
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.posSetup = async (req, res, next) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID

    const confirmWiping = req.body.confirmWiping === 1
    const optedPosId = req.body.optedPosId

    const posMachine = await POSconfig.findOne({ where: { id: optedPosId } })
    if (posMachine) {

      let response;
      let result;

      const POSConfigured = new POSConfigurable.POS(barID)
      await POSConfigured.fetchVenuePOSMigrationDetails()

      const currentOptedPosId = POSConfigured.barPOSDetails.bar.attachedPosConfig
      switch (POSConfigured.barPOSDetails.status) {

        case 'INITIATION_PHASE':
          response = await POSConfigured.exportCSVFile(optedPosId)
          return res.status(200).json(response);

        case 'POS_CONFIGURED':
        case 'UN_CONFIGURED':
          await POSConfigured.updateVenuePosHandle(optedPosId, 'INITIATION_PHASE')
          result = await POSConfigured.fetchVenuePOSMigrationDetails()
          return res.status(200).json({ success: 1, message: 'Operation successful', data: result });

        case 'POS_CONFIGURATION_DECLINED':
          await POSConfigured.updateVenuePosHandle(optedPosId, 'CSV_EXPORT_PHASE')
          result = await POSConfigured.fetchVenuePOSMigrationDetails()
          return res.status(200).json({ success: 1, message: 'Operation successful', data: result });

        case 'CSV_EXPORT_PHASE':
          response = await POSConfigured.handleDataWipe(optedPosId, confirmWiping, currentOptedPosId)
          result = await POSConfigured.fetchVenuePOSMigrationDetails()
          return res.status(200).json({ ...response, data: result });

        default:
          return res.status(200).json({ success: 1, message: 'Nil Operation successful', data: {} });
      }
    } else {
      return res.status(200).json({ success: 0, message: 'Incorrect Selection for POS', data: {} });
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: error });
  }
}

exports.posSettingDetails = async (req, res, next) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID

    const POSConfigured = new POSConfigurable.POS(barID)
    const posConfigDetails = await POSConfigured.fetchVenuePOSMigrationDetails()

    res.status(200).json({ success: 1, message: 'POS configuration details', data: posConfigDetails });

  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

// Checking if the Active & Inactive Hours if valid in the Venue's Operating Hours....
exports.isOperatingHoursValid = async (req, res, next) => {
  try {
    const openrationgHoursList = await operatingHours.findAll({
      attributes: [
        'weekDay',
        'openingHours',
        'closingHours',
        'isClosed',
      ],
      where: {
        barID: req.body.barID,
        isClosed: 0
      },
    });

    let isVenueClosed = commonFunction.checkBarIsOpen(openrationgHoursList, req.body.activeHours, req.body.inActiveHours, req.body.weekDay);

    if (!isVenueClosed) {
      return res.status(200).json({
        success: -2,
        message: "The selected time is out of Venue's operating Hours. Please try again.",
      })
    } else {
      next()
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

// Api for adding specific item's Active & Inactive Hours....
exports.addItemActiveHours = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = req.body.barID
  var subCategoryID = req.body.subCategoryID
  var acHours = moment(`${req.body.activeHours}`, 'HH:mm:ss');
  var inAcHours = moment(`${req.body.inActiveHours}`, 'HH:mm:ss');

  try {

    itemActiveHours.findAll({
      where: { subCategoryID: subCategoryID, barID: barID },
      attributes: [
        'weekDay',
        'activeHours',
        'inActiveHours',
      ]
    })
      .then(async result => {
        let failure = false
        if (moment(inAcHours).isBefore(acHours) || moment(acHours).isAfter(inAcHours)) {
          failure = true
          res.status(200).send({
            success: 0,
            message: "Invalid Time-Slot! The opening hours cann't be set after closing hours."
          })
        } else {
          result.forEach(element => {
            if (req.body.weekDay == element.dataValues.weekDay) {

              var activeHRS = moment(`${element.dataValues.activeHours}`, 'HH:mm:ss');
              var inActiveHRS = moment(`${element.dataValues.inActiveHours}`, 'HH:mm:ss');

              if (moment(acHours).isBetween(activeHRS, inActiveHRS) || moment(inAcHours).isBetween(activeHRS, inActiveHRS) || moment(acHours).isSame(activeHRS) || moment(acHours).isSame(inActiveHRS) || moment(inAcHours).isSame(activeHRS) || moment(inAcHours).isSame(inActiveHRS)) {
                failure = true
              }
              if (moment(activeHRS).isBetween(acHours, inAcHours) || moment(inActiveHRS).isBetween(acHours, inAcHours) || moment(activeHRS).isSame(acHours) || moment(activeHRS).isSame(acHours) || moment(inActiveHRS).isSame(inAcHours) || moment(inActiveHRS).isSame(inAcHours)) {
                failure = true
              }
            }
          });
          if (failure === false) {
            itemActiveHours
              .create({
                barID: barID,
                subCategoryID: subCategoryID,
                activeHours: moment(acHours).format('HH:mm:ss'),
                inActiveHours: moment(inAcHours).format('HH:mm:ss'),
                weekDay: req.body.weekDay,
                status: req.body.status
              })
              .then(async response => {
                var id = response.id
                let subCategoryData = commonFunction.timeChunkArray(acHours, inAcHours, '60')
                let subCategoryWaitTimeArray = subCategoryData && subCategoryData.map((item) => ({
                  ...item,
                  barID: barID,
                  subCategoryID: subCategoryID,
                  itemActiveHoursID: response.id,
                  weekDay: req.body.weekDay,
                  waitTime: '00:10:00' //default 10 minutes 
                }));
                if (subCategoryWaitTimeArray) {
                  subCategoryWaitTime.bulkCreate(subCategoryWaitTimeArray)
                    .catch(function (err) {
                      console.log(err)
                    });
                }
                res.status(200).send({
                  success: 1,
                  data: response,
                  message: 'Item added successfully'
                })
              })
              .catch(function (err) {
                console.log(err)
              })
          } else {
            res.status(200).send({
              success: 0,
              message: 'Time-Slot already exists! Enter another one.'
            })
          }
        }
      })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

// Api for updating specific item's Active & Inactive Hours....
exports.updateItemActiveHours = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = req.body.barID
  var subCategoryID = req.body.subCategoryID
  var itemActiveHoursId = req.body.id
  var acHours = moment(`${req.body.activeHours}`, 'HH:mm:ss');
  var inAcHours = moment(`${req.body.inActiveHours}`, 'HH:mm:ss');

  try {
    if (itemActiveHoursId) {
      itemActiveHours.findAll({
        where: { subCategoryID: subCategoryID, barID: barID },
        attributes: [
          'id',
          'weekDay',
          'activeHours',
          'inActiveHours',
          'status',
        ]
      })
        .then(async result => {
          let failure = false
          if (moment(inAcHours).isBefore(acHours) || moment(acHours).isAfter(inAcHours)) {
            failure = true
            res.status(200).send({
              success: 0,
              message: "Invalid Time-Slot! The opening hours cann't be set after closing hours."
            })
          } else {
            result.forEach(element => {
              if (req.body.weekDay == element.dataValues.weekDay) {

                var activeHRS = moment(`${element.dataValues.activeHours}`, 'HH:mm:ss');
                var inActiveHRS = moment(`${element.dataValues.inActiveHours}`, 'HH:mm:ss');
                // console.log(itemActiveHoursId != element.dataValues.id);
                if ((moment(acHours).isBetween(activeHRS, inActiveHRS) || moment(inAcHours).isBetween(activeHRS, inActiveHRS) || moment(acHours).isSame(activeHRS) || moment(acHours).isSame(inActiveHRS) || moment(inAcHours).isSame(activeHRS) || moment(inAcHours).isSame(inActiveHRS)) && itemActiveHoursId != element.dataValues.id) {
                  failure = true
                }
                if ((moment(activeHRS).isBetween(acHours, inAcHours) || moment(inActiveHRS).isBetween(acHours, inAcHours) || moment(activeHRS).isSame(acHours) || moment(activeHRS).isSame(acHours) || moment(inActiveHRS).isSame(inAcHours) || moment(inActiveHRS).isSame(inAcHours)) && itemActiveHoursId != element.dataValues.id) {
                  failure = true
                }
              }
            });
            if (failure === false) {
              itemActiveHours
                .update(
                  { activeHours: moment(acHours).format('HH:mm:ss'), inActiveHours: moment(inAcHours).format('HH:mm:ss'), status: req.body.status },
                  { where: { id: itemActiveHoursId } }
                )
                .then(async response => {
                  await subCategoryWaitTime.destroy({ where: { itemActiveHoursId: itemActiveHoursId } });
                  let subCategoryWaitTimeData = commonFunction.timeChunkArray(acHours, inAcHours, '60');

                  let subCategoryWaitTimeArray = subCategoryWaitTimeData && subCategoryWaitTimeData.map((item) => ({
                    ...item,
                    barID: barID,
                    subCategoryID: subCategoryID,
                    itemActiveHoursID: itemActiveHoursId,
                    weekDay: req.body.weekDay,
                    waitTime: '00:10:00' //default 10 minutes
                  }));
                  if (subCategoryWaitTimeArray) {
                    subCategoryWaitTime.bulkCreate(subCategoryWaitTimeArray)
                      .catch(function (err) {
                        console.log(err)
                      });
                  }
                  res.json({
                    success: 1,
                    message: 'Time Slot Updated Successfully.'
                  })
                })
                .catch(function (err) {
                  console.log(err)
                })
            } else {
              res.status(200).send({
                success: 0,
                message: 'Time-Slot already exists! Enter another one.'
              })
            }
          }
        })

    } else {
      res.status(200).json({
        success: 0,
        message: "No Time slot Found."
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error in updating record!'
    })
  }
}

// Api to get time slot(active hour & inactive hours) of an Item....
exports.getItemActiveHours = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID
    var subCategoryID = req.body.subCategoryID

    itemActiveHours
      .findAll({
        where: { subCategoryID: subCategoryID, barID: barID },
        attributes: [
          [sequelize.literal("`weekDay`,JSON_ARRAYAGG(JSON_OBJECT('id',`id`,'activeHours',TIME_FORMAT(`activeHours`,'%H:%i'),'inActiveHours',TIME_FORMAT(`inActiveHours`,'%H:%i'),'status',`status`))"), 'WeekDay_ItemActiveHours'],
        ],
        group: ['weekDay'],
        order: ['weekDay'],
        raw: true,
      })
      .then(async response => {
        if (response) {
          res.status(200)
            .json({
              success: 1,
              message: 'Active hours retrieved successfully.',
              data: response
            })
        } else {
          res.status(200)
            .send({
              success: 1,
              message: "Item's Active Hours are missing.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

// Api for deleting time slot(active hour & inactive hours) of an Item....
exports.deleteItemActiveHours = async (req, res, next) => {
  var itemActiveHoursId = req.body.id
  try {
    if (itemActiveHoursId) {
      itemActiveHours
        .destroy({ where: { id: itemActiveHoursId } })
        .then(async function () {
          subCategoryWaitTime.destroy({ where: { itemActiveHoursId: itemActiveHoursId } });
          res.json({
            success: 1,
            message: 'Active Hour Deleted Successfully.'
          })
        }).error(function (err) {
          res.status(200).json({
            success: 0,
            message: err.message,
          })
        })
    } else {
      res.status(200).json({
        success: 0,
        message: "No Active Timeslot Found."
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error in deleting record!'
    })
  }
}

// Api for adding & updating time slot(active hour & inactive hours) of an Item....
exports.addUpdateItemActiveHours = async (req, res, next) => {
  // var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  // var barID = req.body.barID
  // var subCategoryID = req.body.subCategoryID
  var itemActiveHoursId = req.body.id

  // var acHours = moment(`${req.body.activeHours}`, 'HH:mm:ss');
  // var inAcHours = moment(`${req.body.inActiveHours}`, 'HH:mm:ss');

  if (itemActiveHoursId) {
    this.updateItemActiveHours(req, res)
  } else {
    this.addItemActiveHours(req, res)
  }
}

// Api to get sub-heading wait-time slot
exports.getSubHeadingWaitTime = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID
    var subCategoryID = req.body.subCategoryID
    subCategoryWaitTime
      .findAll({
        where: { subCategoryID: subCategoryID, barID: barID },
        attributes: [
          [sequelize.literal("waitTimeType"), 'type'],
          [sequelize.literal("`weekDay`,JSON_ARRAYAGG(JSON_OBJECT('id',`id`,'startTime',TIME_FORMAT(`startTime`,'%H:%i'),'endTime',TIME_FORMAT(`endTime`,'%H:%i'),'waitTime',TIME_FORMAT(`waitTime`,'%H:%i')))"), 'weekDay_WaitingTime'],
        ],
        group: ['weekDay'],
        order: ['weekDay'],
        raw: true,
      })
      .then(async response => {
        if (response.length > 0) {
          let newResponse = response.map((day) => {
            // sort response with time (ASC)
            let weekDay_WaitingTime = day.weekDay_WaitingTime.sort(function (a, b) {
              return a.startTime.localeCompare(b.startTime);
            });
            return { ...day, weekDay_WaitingTime: weekDay_WaitingTime }
          })
          res.status(200)
            .json({
              success: 1,
              message: 'Wait time retrieved successfully.',
              data: newResponse
            })
        } else {
          res.status(200)
            .send({
              success: 0,
              message: "No wait time found for this sub category. Please add the kitchen time slot first.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

// Api to update sub-heading wait-time for one slot
exports.updateSubHeadingWaitTime = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID
    const waitTimeType = req.body.type
    const subCategoryID = req.body.subCategoryID

    const waitTimeArray = JSON.parse(req.body && req.body.data);

    subCategoryWaitTime
      .findOne({
        where: {
          subCategoryID: subCategoryID,
          barID: barID,
        },
      })
      .then(async response => {
        if (response) {
          try {
            await subCategoryWaitTime.update(
              {
                waitTimeType: waitTimeType,
              },
              {
                where: {
                  subCategoryID: subCategoryID,
                  barID: barID,
                },
              }
            )
            waitTimeArray && waitTimeArray.map((day) => {
              day && day.weekDay_WaitingTime && day.weekDay_WaitingTime.map(async (item) => {
                await subCategoryWaitTime.update(
                  {
                    waitTime: item.waitTime >= "00:00" ? item.waitTime : "00:10"
                  },
                  {
                    where: {
                      id: item.id,
                      weekDay: day.weekDay
                    },
                  }
                )
              })
            })
            res.status(200)
              .json({
                success: 1,
                message: 'Wait time updated successfully.',
                data: {}
              })
          } catch (e) {
            res.status(200)
              .json({
                success: 0,
                message: e.message
              })
          }
        } else {
          res.status(200)
            .send({
              success: 0,
              message: "Cannot find matching record.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

// Api to update sub-heading wait-time for old time slots
exports.updateSubHeadingWaitTimeForOldRecords = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID
    itemActiveHours
      .findAll({
        where: {
          status: '1'
        }
      })
      .then(async response => {
        if (response) {
          try {
            await response.map(async (record) => {
              await subCategoryWaitTime.destroy({ where: { itemActiveHoursId: record.dataValues.id } });
              let subCategoryWaitTimeData = await commonFunction.timeChunkArray(record.dataValues.activeHours, record.dataValues.inActiveHours, '60');

              let subCategoryWaitTimeArray = subCategoryWaitTimeData && subCategoryWaitTimeData.map((item) => ({
                ...item,
                barID: record.dataValues.barID,
                subCategoryID: record.dataValues.subCategoryID,
                itemActiveHoursID: record.dataValues.id,
                weekDay: record.dataValues.weekDay,
                waitTime: '00:10:00' //default 10 minutes
              }));
              if (subCategoryWaitTimeArray) {
                await subCategoryWaitTime.bulkCreate(subCategoryWaitTimeArray)
              }
            })
            res.status(200)
              .json({
                success: 1,
                message: 'Wait time updated successfully.',
                data: {}
              })
          } catch (e) {
            res.status(200)
              .json({
                success: 0,
                message: e.message
              })
          }
        } else {
          res.status(200)
            .send({
              success: 0,
              message: "Cannot find matching record.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: 0, message: 'Something went wrong, please try again', data: {} });
  }
}

exports.manualNotificationsToAllUsers = async (req, res) => {
  try {
    let message = req.body.message || 'There is a new update available for your MyTab app, please update your app to the newest version. We continue to work hard to improve your experience whilst ordering at your favourite venues. Thank you.'
    let notification_title = req.body.title || 'Application Update'
    await commonFunction.statusNotificationToUser(notification_title, message)

    res.json({
      success: 1,
      message: 'success!'
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

// Add New Promo-Code Coupons...
exports.addNewPromoCode = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID

  try {
    // 9. Promo codes - Subheading specific....  Starts
    //var subIds = JSON.parse(req.body.subCategoryID)
    // var arrCouponsSubIds = []
    // 9. Promo codes - Subheading specific....  Ends
    coupons.findOne({
      where: { barID: barID, [Op.or]: [{ name: req.body.name }, { code: req.body.code }], isDeleted: 'No' },
      attributes: [
        'name',
        'code',
        'discount_amount',
        'startsAt',
        'expiresAt'
      ],
    }).then(async couponResult => {
      if (!couponResult) {
        if (req.body.discount_amount > 80) {
          res.status(200).send({
            success: 0,
            message: 'Your venues promo codes can only be set to a maximum of 80% off due merchant fees. Thank you.'
          })
        } else {
          coupons.create({
            barID: barID,
            code: req.body.code,
            name: req.body.name,
            description: req.body.description,
            startsAt: req.body.startsAt,
            expiresAt: req.body.expiresAt,
            discount_amount: req.body.discount_amount,
            status: 'Active',
            createdAt: new Date(),
          })
            .then(async response => {
              // var couponId = response.dataValues.id
              // // 9. Promo codes - Subheading specific....  Starts
              // var ids = []
              // for (const ele of subIds) {
              //   ids = {
              //     couponsID: couponId,
              //     subCategoryID: ele
              //   }
              //   arrCouponsSubIds.push(ids)
              // }
              // couponsSubCatIds.bulkCreate(arrCouponsSubIds)
              // // 9. Promo codes - Subheading specific....  Ends
              // .then(async addPromoCode => {
              //   addPromo = await coupons.findOne({
              //     attributes: [
              //       'id',
              //       'name',
              //       'code',
              //       'discount_amount',
              //       'startsAt',
              //       'expiresAt',
              //     ],
              //     where: {
              //       barID: barID, 
              //       isDeleted: 'No',
              //       id: couponId
              //     },
              //     // 9. Promo codes - Subheading specific....  Starts
              //     include: [{
              //       model: couponsSubCatIds,
              //       attributes: ['id', 'subCategoryID'],
              //       include: [{
              //         model: sub_category,
              //         attributes: ['name'],
              //       }]
              //     }],
              //     // 9. Promo codes - Subheading specific....  Ends
              //   })
              //   if (addPromo) {
              //     res.status(200).send({
              //       success: 1,
              //       data: addPromo,
              //       message: 'Coupon added successfully'
              //     })
              //   } else {
              //     res.status(200).json({
              //       success: 0,
              //       message: 'error!',
              //     })
              //   }
              // })
              if (response) {
                res.status(200).send({
                  success: 1,
                  data: response,
                  message: 'Coupon added successfully'
                });
              } else {
                res.status(200).json({
                  success: 0,
                  message: 'error!',
                })
              }
            })
            .catch(function (err) {
              console.log(err)
            })
        }
      } else {
        res.status(200).send({
          success: 0,
          message: 'Coupon code or name already in use.'
        })
      }
    })
  } catch (error) {
    console.log("error", error);
    res.status(200).send({
      success: 0,
      message: 'Error!'
    })
  }
}
// Edit exisiting Promo-Code Coupons...
exports.editPromoCode = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID
  var couponId = req.body.id

  try {
    if (couponId) {
      // 9. Promo codes - Subheading specific....  Starts
      // var subIds = JSON.parse(req.body.subCategoryID)
      // var arrCouponsSubIds = []
      // 9. Promo codes - Subheading specific....  Ends
      coupons.findOne({
        where: { barID: barID, isDeleted: 'No', id: couponId },
        attributes: [
          'id',
          'name',
          'code',
          'discount_amount',
          'startsAt',
          'expiresAt',
        ],
      }).then(async couponResult => {
        if (couponResult.dataValues.id == couponId) {
          coupons.findAll({
            attributes: ['*'],
            where: { barID: barID, isDeleted: 'No', id: { [Op.not]: couponId }, [Op.or]: { name: req.body.name, code: req.body.code } },
          }).then(async couponRecord => {
            if (couponRecord.length == 0) {
              if (req.body.discount_amount > 80.00) {
                res.status(200).send({
                  success: 0,
                  message: 'Your venues promo codes can only be set to a maximum of 80% off due merchant fees. Thank you.'
                })
              } else {
                coupons.update(
                  {
                    barID: barID,
                    code: req.body.code,
                    name: req.body.name,
                    description: req.body.description,
                    startsAt: req.body.startsAt,
                    expiresAt: req.body.expiresAt,
                    discount_amount: req.body.discount_amount,
                    status: req.body.status,
                    updatedAt: new Date()
                  },
                  { where: { id: couponId } }
                )
                  .then(async response => {
                    // 9. Promo codes - Subheading specific....  Starts
                    // couponsSubCatIds.destroy({ where: { couponsID: couponId } })
                    // var ids = []
                    // for (const ele of subIds) {
                    //   ids = {
                    //     couponsID: couponId,
                    //     subCategoryID: ele
                    //   }
                    //   arrCouponsSubIds.push(ids)
                    // }
                    // couponsSubCatIds.bulkCreate(arrCouponsSubIds)
                    // // 9. Promo codes - Subheading specific....  Ends
                    // .then(async updatePromoCode => {
                    //   updateData = await coupons.findOne({
                    //     attributes: [
                    //       'id',
                    //       'name',
                    //       'code',
                    //       'discount_amount',
                    //       'startsAt',
                    //       'expiresAt',
                    //     ],
                    //     where: {
                    //       barID: barID, 
                    //       isDeleted: 'No',
                    //       id: couponId
                    //     },
                    //     // 9. Promo codes - Subheading specific....  Starts
                    //     include: [{
                    //       model: couponsSubCatIds,
                    //       attributes: ['id', 'subCategoryID'],
                    //       include: [{
                    //         model: sub_category,
                    //         attributes: ['name'],
                    //       }]
                    //     }],
                    //     // 9. Promo codes - Subheading specific....  Ends
                    //   })
                    //   if (updateData) {
                    //     res.status(200).send({
                    //       success: 1,
                    //       data: updateData,
                    //       message: 'Coupon updated successfully'
                    //     })
                    //   } else {
                    //     res.status(200).json({
                    //       success: 0,
                    //       message: 'error!',
                    //     })
                    //   }
                    // })
                    if (response) {
                      res.status(200).send({
                        success: 1,
                        data: response,
                        message: 'Coupon updated successfully'
                      })
                    } else {
                      res.status(200).json({
                        success: 0,
                        message: 'error!',
                      })
                    }
                  })
                  .catch(function (err) {
                    console.log(err)
                  })
              }
            } else {
              res.status(200).send({
                success: 0,
                message: 'Coupon code or name already in use.'
              })
            }
          })
        } else {
          res.status(200).send({
            success: 0,
            message: 'Error in retrieving list of coupons.'
          })
        }
      })
    }
    else {
      res.status(200).json({
        success: 0,
        message: "No coupon Found."
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error!'
    })
  }
}

exports.listPromoCode = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID;
  const today = moment(new Date()).format('YYYY-MM-DD')
  try {
    coupons.findAll({
      where: { barID: barID, isDeleted: 'No' },
      attributes: [
        'id',
        'barID',
        'name',
        'code',
        'discount_amount',
        'description',
        'startsAt',
        [sequelize.literal(`IF(expiresAt = '0000-00-00', null, expiresAt)`), 'expiresAt'],
        'status',
        'createdAt',
        [sequelize.literal(`IF('${today}' > (SELECT IF(expiresAt = '0000-00-00', null, expiresAt)), 1, 0)`), 'isExpired'],
      ],
      // 9. Promo codes - Subheading specific....  Starts
      // include: [{
      //   model: couponsSubCatIds,
      //   attributes: ['id', 'subCategoryID'],
      //   include: [{
      //     model: sub_category,
      //     attributes: ['name'],
      //   }]
      // }],
      // 9. Promo codes - Subheading specific....  Ends
      order: [[sequelize.literal('isExpired', 'ASC')], ['id', 'DESC']],
    }).then(async couponResult => {
      if (couponResult != 0) {
        res.status(200).send({
          success: 1,
          message: 'All Coupons Retrieved Sccessfully!',
          data: couponResult
        })
      } else {
        res.status(200).send({
          success: 0,
          message: 'No Coupons Found for the Venue!',
          data: {}
        })
      }
    })
      .catch(function (err) {
        console.log(err)
      })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'Error in retrieving coupons.'
    })
  }
}

exports.deletePromoCode = async (req, res) => {
  var couponId = req.body.id
  try {
    if (couponId) {
      coupons
        .update({ isDeleted: 'Yes', updatedAt: new Date() }, { where: { id: couponId } })
        // couponsSubCatIds.destroy({ where: { couponsID: couponId } })// 9. Promo codes - Subheading specific....
        .then(async function () {
          res.json({
            success: 1,
            message: 'Coupon Deleted Successfully.'
          })
        }).error(function (err) {
          res.status(200).json({
            success: 0,
            message: err.message,
          })
        })
    } else {
      res.status(200).json({
        success: 0,
        message: "No Coupons Found."
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error in deleting record!'
    })
  }
}

// Add Role(Email & Time Duration) for the Export Sheet......
exports.addUpdateRole = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID

  try {
    addUpdateRole
      .findOrCreate({
        where: {
          barID: barID,
          email: req.body.email
        },
        defaults: {
          barID: barID,
          email: req.body.email,
        }
      }).then(async response => {
        if (response) {
          response[0].update({ secondaryEmail: req.body.secondaryEmail })
          csvAndReport.findOrCreate({
            where: {
              roleID: response[0].id,
            },
            defaults: {
              roleID: response[0].id,
              frequency: req.body.frequency ? req.body.frequency : 'daily',
              createdAt: new Date(),
              updatedAt: new Date(),
            }
          }).then(async result => {
            if (result[1] == false) {
              result[0].update({ frequency: req.body.frequency, updatedAt: new Date() });
            }
          }).error(function (err) {
            res.status(200).json({
              success: 0,
              message: err.message,
            })
          })
        }
        res.json({
          success: 1,
          message: 'Report Period Successfully Updated.'
        })
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error in creating record!'
    })
  }
}

// Api to get role and it's frequency for export sheet....
exports.getRole = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    var barID = sessionData.barID

    addUpdateRole
      .findOne({
        where: { barID: barID },
        attributes: [
          'email',
          'secondaryEmail',
          'barID',
          [Sequelize.literal(`(select frequency from csvAndReport where roleID = role.id)`), 'frequency'],
        ],
      })
      .then(async response => {
        if (response) {
          res.status(200)
            .json({
              success: 1,
              message: "Roles & it's Frequency retrieved successfully.",
              data: response
            })
        } else {
          res.status(200)
            .send({
              success: 1,
              message: "There are no such roles.",
              data: {}
            })
        }
      })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      success: 0,
      message: 'Something went wrong, please try again',
      data: {}
    });
  }
}

// Function that fetch, write & export the excel sheet to venues according the selected time-duration.....
const exportCSVtoVenue = async () => {

  var lastDayOfMonth = Number(moment(new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)).format('DD'))
  var tdy = moment(new Date()).format('YYYY-MM-DD')

  await addUpdateRole.findAll({
    attributes: [
      'email',
      'barID',
      'secondaryEmail'
    ],
    include: [
      {
        model: csvAndReport,
        attributes: ['roleID', 'frequency'],
        required: true,
        where: {
          [Op.or]: [
            {
              frequency: 'daily'
            },
            {
              [Op.and]: [
                sequelize.where(sequelize.fn('dayofweek', sequelize.fn('now')), 1),
                { frequency: 'weekly' }
              ],
            },
            {
              [Op.and]: [
                sequelize.where(sequelize.fn('last_day', sequelize.fn('now')), tdy),
                { frequency: 'monthly' }
              ],
            },
            {
              [Op.and]: [
                sequelize.where(sequelize.literal("dayofmonth(now())%14"), 0),
                { frequency: 'fortnightly' }
              ],
            }
          ],
        }
      }
    ],
    // Activate the below code if client asks to only send reports to the venues that are Active.
    // include: [
    //   {
    //     model: bar,
    //     where: sequelize.where(Sequelize.col('status'), '=', ['Active']),
    //   }
    // ],
  }).then(async res => {
    mailContent = []
    frequencyConversion = {
      'daily': 0,
      'weekly': 6,
      'fortnightly': 13,
      'monthly': lastDayOfMonth - 1
    }
    for (const ele of res) {
      const emails = ele.dataValues.email
      const secondaryEmails = ele.dataValues.secondaryEmail
      for (const freq of ele.csvAndReports) {
        let frequency = frequencyConversion[freq.frequency]
        let venueName = await bar.findOne({
          attributes: ['restaurantName'],
          where: { id: ele.barID }
        })
        let exportDetail = await orders.findAll({
          attributes: [
            'orderDate',
            'orderNo',
            'total',
            'transactionFee',
            [sequelize.literal("(select round(sum((total * 0.25 / 100) + 0.25),2) from orders as o where o.id = `orders`.id)"), 'merchant_platform_fee'],
            [sequelize.literal("(select round(sum(total - ((total * 2.9 / 100 + 0.31) + (IF((`orderDate` < DATE('2021-06-25')), ((total * 0.25 / 100) + 0.25), 0)))), 2) from orders as o where o.id = `orders`.id)"), 'venue_earnings'],
          ],
          where: {
            barID: ele.barID,
            orderDate: { [Op.between]: [sequelize.literal(`DATE(now()) - INTERVAL ${frequency} DAY`), sequelize.fn('DATE', sequelize.fn('now'))] }
          },
          // order: [['orderDate', 'DESC']] // If Client ask for Desc order..
        })

        const stockCountMainProductsCat1 = await sO.query(`
          SELECT
            p_main.name, COUNT(oi.productID) as Quantity, categoryID
          FROM
            orders AS o
            left join order_items as oi on o.id = oi.orderID
            inner join product as p_main on oi.productID = p_main.id 
          WHERE
            o.barID = ${ele.barID} AND orderDate between CURRENT_DATE() - interval ${frequency} Day and CURRENT_DATE() AND categoryID = 1
          GROUP BY
            p_main.name
          `,
          { type: QueryTypes.SELECT }
        )
        const stockCountMainProductsCat2 = await sO.query(`
          SELECT
            p_main.name, COUNT(oi.productID) as Quantity, categoryID
          FROM
            orders AS o
            left join order_items as oi on o.id = oi.orderID
            inner join product as p_main on oi.productID = p_main.id 
          WHERE
            o.barID = ${ele.barID} AND orderDate between CURRENT_DATE() - interval ${frequency} Day and CURRENT_DATE() AND categoryID = 2
          GROUP BY
            p_main.name
          `,
          { type: QueryTypes.SELECT }
        )
        const stockCountExtrasCat1 = await sO.query(`
          SELECT 
            p.extraItem, COUNT(oie.productExtrasID) as Quantity, categoryID 
          FROM 
            orders as o 
            inner join order_items as oi on o.id = oi.orderID 
            LEFT join order_item_extras oie on oi.id = oie.orderItemID 
            left join product_extras as p on p.id = oie.productExtrasID 
            inner join product p_main on p.productID = p_main.id  
          WHERE 
            o.barID = ${ele.barID} AND orderDate between CURRENT_DATE() - interval ${frequency} Day and CURRENT_DATE() AND categoryID = 1 
          GROUP BY 
            p.extraItem
          `,
          { type: QueryTypes.SELECT }
        )
        const stockCountExtrasCat2 = await sO.query(`
          SELECT 
            p.extraItem, COUNT(oie.productExtrasID) as Quantity , categoryID 
          FROM 
            orders as o 
            inner join order_items as oi on o.id = oi.orderID 
            LEFT join order_item_extras oie on oi.id = oie.orderItemID 
            left join product_extras as p on p.id = oie.productExtrasID 
            inner join product p_main on p.productID = p_main.id  
          WHERE 
            o.barID = ${ele.barID} AND orderDate between CURRENT_DATE() - interval ${frequency} Day and CURRENT_DATE() AND categoryID = 2 
          GROUP BY 
            p.extraItem
          `,
          { type: QueryTypes.SELECT }
        )

        const stockCountOfProductsFromKounta = await sO.query(`
        SELECT
          p_main.name, COUNT(oi.productID) as Quantity, categoryID
        FROM
          orders AS o
          left join order_items as oi on o.id = oi.orderID
          inner join product as p_main on oi.productID = p_main.id 
        WHERE
          o.barID = ${ele.barID} AND orderDate between CURRENT_DATE() - interval ${frequency} Day and CURRENT_DATE() AND categoryID = -1
        GROUP BY
          p_main.name
        `,
          { type: QueryTypes.SELECT }
        )

        const stockCountOfExtrassFromKounta = await sO.query(`
        SELECT 
          p.extraItem, COUNT(oie.productExtrasID) as Quantity , categoryID 
        FROM 
          orders as o 
          inner join order_items as oi on o.id = oi.orderID 
          LEFT join order_item_extras oie on oi.id = oie.orderItemID 
          left join product_extras as p on p.id = oie.productExtrasID 
          inner join product p_main on p.productID = p_main.id  
        WHERE 
          o.barID = ${ele.barID} AND orderDate between CURRENT_DATE() - interval ${frequency} Day and CURRENT_DATE() AND categoryID = -1 
        GROUP BY 
          p.extraItem
        `,
          { type: QueryTypes.SELECT }
        )

        // Writing XLSX FILE......................
        var reportGenerationDate = new Date()
        var tempDate = new Date()
        var reportDataDate = tempDate.setDate(tempDate.getDate() - frequency);
        let today = ''
        if (frequency == 0) {
          today = moment(new Date(reportDataDate)).format('DD/MM/YYYY')
        } else {
          today = moment(new Date(reportDataDate)).format('DD/MM/YYYY') + " to " + moment(new Date(reportGenerationDate)).format('DD/MM/YYYY')
        }
        const exportXLSX = async (req, res) => {
          const workbook = new excelJS.Workbook();  // Create a new workbook
          // const worksheet = workbook.addWorksheet("ORDER BREAKDOWN"); // New Worksheet

          // worksheet.properties.defaultRowHeight = 15;
          // worksheet.properties.defaultColWidth = 30;
          const VENUE = "VENUE NAME: " + `${venueName.dataValues.restaurantName}`

          // // For Venue Name..........................
          // worksheet.getCell('A1').font = {
          //   name: 'Calibri',
          //   color: { argb: '000000' },
          //   family: 2,
          //   size: 20,
          // };
          // worksheet.getCell('A1').alignment = { vertical: 'middle' };
          // worksheet.getCell('A1:U1').fill = {
          //   type: 'pattern',
          //   pattern: 'solid',
          //   fgColor: { argb: 'F08080' },
          // };
          // worksheet.mergeCells('A1:U1');
          // worksheet.getCell('A1').value = `${VENUE}`;
          // worksheet.getRow(1).height = 32

          // // For DATE of EXPORT.....................
          // worksheet.getCell('A2:U2').fill = {
          //   type: 'pattern',
          //   pattern: 'solid',
          //   fgColor: { argb: 'FDE584' },
          // };
          // worksheet.mergeCells('A2:U2');
          // // worksheet.getCell('A2').value = "DATE: ";
          // worksheet.getCell('B2').font = {
          //   name: 'Calibri',
          //   color: { argb: '000000' },
          //   family: 2,
          //   size: 14,
          // };
          // worksheet.getCell('B2').value = "DATE: " + `${today}`;

          // // For Static Line of support EMAIL................
          // worksheet.getCell('A3').font = {
          //   name: 'Calibri',
          //   color: { argb: '000000' },
          //   family: 2,
          //   size: 14,
          // };
          // worksheet.mergeCells('A3:U3');
          // worksheet.getCell('A3').value = "For any queries please email <NAME_EMAIL>";

          // // For static line of not for payouts....................
          // worksheet.getCell('A4').font = {
          //   name: 'Calibri',
          //   color: { argb: '000000' },
          //   family: 2,
          //   size: 14,
          // };
          // worksheet.mergeCells('A4:U4');
          // worksheet.getCell('A4').value = "Stripe payouts will be received in your nominated Bank Account connected to MyTab Venue. Please note: This spreadsheet will only include the breakdown of your orders so you can see the ORDER ID for each transaction listed on your Stripe Express Dashboard.";

          // // For static line of special note.............
          // worksheet.getCell('A5').font = {
          //   name: 'Calibri',
          //   color: { argb: '000000' },
          //   family: 2,
          //   size: 11,
          //   bold: true,
          // };
          // worksheet.mergeCells('A5:C5');
          // worksheet.getCell('A5').value = "PLEASE NOTE ALL VALUES ARE GST INCLUSIVE";

          // // For the static lines of info to Venue Owner.........
          // worksheet.mergeCells('A6:G6');
          // worksheet.getCell('A6').value = "This sheet is to be used in correlation with your Stripe Express Connected Account financial dashboard."
          // // worksheet.getRow(6).height = 60
          // worksheet.getCell('A6').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '9E4244' },
          //   family: 2,
          //   size: 10,
          //   bold: true
          // };
          // worksheet.mergeCells('A7:G7');
          // worksheet.getCell('A7').value = "As all financial transactions (customer payments, customer refunds, your venue payouts, stripe fees) are accurately listed via your Stripe dashboard,  please use Stripe financial reports of your Venue for accounting and tax purposes."
          // worksheet.getCell('A7').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '9E4244' },
          //   family: 2,
          //   size: 10,
          //   bold: true
          // };
          // worksheet.mergeCells('A8:G8');
          // worksheet.getCell('A8').value = "You can use the below sheet to cross reference your 'Payments from MyTab' transactions found on your Stripe dashboard with the below Order ID from your MyTab Venue orders."
          // worksheet.getCell('A8').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '9E4244' },
          //   family: 2,
          //   size: 10,
          //   bold: true
          // };
          // worksheet.mergeCells('A9:G9');
          // worksheet.getCell('A9').value = "For further investigation, you can then use the below Order ID numbers to cross reference on your MyTab Venue Order History Page to see exactly what the customer has ordered."
          // worksheet.getCell('A9').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '9E4244' },
          //   family: 2,
          //   size: 10,
          //   bold: true
          // };
          // worksheet.mergeCells('A10:G10');
          // worksheet.getCell('A10').value = "This is not needed for tax purposes, however, we have provided these breakdowns for complete order transparency."
          // worksheet.getCell('A10').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '9E4244' },
          //   family: 2,
          //   size: 10,
          //   bold: true
          // };

          // // For Static MYTAB line......................
          // worksheet.getCell('A12').font = {
          //   name: 'Calibri',
          //   color: { argb: '000000' },
          //   family: 2,
          //   size: 22,
          //   bold: true,
          // };
          // worksheet.mergeCells('A12:U12');
          // worksheet.getCell('A12').value = "MyTab Venue Orders Financial Breakdown";
          // worksheet.getRow(12).height = 30
          // // For the Actual Calculation Data...............................

          // worksheet.getCell('A14').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '3A9BDC' },
          //   family: 2,
          //   size: 11,
          //   bold: true,
          // };
          // worksheet.getCell('A14').value = "DATE";
          // worksheet.getCell('B14').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '3A9BDC' },
          //   family: 2,
          //   size: 11,
          //   bold: true,
          // };
          // worksheet.getCell('B14').value = "ORDER ID";
          // worksheet.getCell('C14').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '3A9BDC' },
          //   family: 2,
          //   size: 11,
          //   bold: true,
          // };
          // worksheet.getCell('C14').value = "TOTAL PAID BY CUSTOMER";
          // worksheet.getCell('D14').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '3A9BDC' },
          //   family: 2,
          //   size: 11,
          //   bold: true,
          // };
          // worksheet.getCell('D14').value = "MYTAB FEE";
          // worksheet.getCell('E14').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '3A9BDC' },
          //   family: 2,
          //   size: 11,
          //   bold: true,
          // };
          // worksheet.getCell('E14').value = "MERCHANT PLATFORM FEE";
          // worksheet.getCell('F14').font = {
          //   name: 'Calibri Light',
          //   color: { argb: '3A9BDC' },
          //   family: 2,
          //   size: 11,
          //   bold: true,
          // };
          // worksheet.getCell('F14').value = "NET VENUE REVENUE (AUD)";
          // // Looping through data
          // // let TOTALPAYOUT = 0
          // let newRows = 15
          // exportDetail.forEach((calculation, index) => {
          //   worksheet.getCell("A" + (newRows + index)).value = moment(calculation.dataValues.orderDate).format('DD/MM/YYYY');
          //   worksheet.getCell("B" + (newRows + index)).value = calculation.dataValues.orderNo;
          //   worksheet.getCell("C" + (newRows + index)).value = "$" + calculation.dataValues.total.toFixed(2);
          //   worksheet.getCell("C" + (newRows + index)).alignment = { horizontal: 'right' };
          //   worksheet.getCell("D" + (newRows + index)).value = "$" + calculation.dataValues.transactionFee.toFixed(2);
          //   worksheet.getCell("D" + (newRows + index)).alignment = { horizontal: 'right' };
          //   worksheet.getCell("E" + (newRows + index)).value = "$" + calculation.dataValues.merchant_platform_fee.toFixed(2);
          //   worksheet.getCell("E" + (newRows + index)).alignment = { horizontal: 'right' };
          //   worksheet.getCell("F" + (newRows + index)).value = "$" + calculation.dataValues.venue_earnings.toFixed(2);
          //   worksheet.getCell("F" + (newRows + index)).alignment = { horizontal: 'right' };
          //   // TOTALPAYOUT += calculation.dataValues.venue_earnings
          // });
          // let lastRow = exportDetail.length + newRows
          // worksheet.getCell("A" + lastRow).value = "TOTAL PAYOUT";
          // worksheet.getCell("A" + lastRow).alignment = { horizontal: 'center' };
          // worksheet.getCell("F" + lastRow).value = "$" + `${TOTALPAYOUT.toFixed(2)}`;
          // worksheet.getCell("F" + lastRow).alignment = { horizontal: 'right' };

          // Making first line in excel bold
          // worksheet.getRow(11).eachCell((cell) => {
          //   cell.font = { bold: true };
          // });

          // ..............................SECOND SHEET OF STOCKS......................................

          const ws = workbook.addWorksheet("STOCK COUNT"); // New Worksheet
          ws.properties.defaultRowHeight = 15;
          ws.properties.defaultColWidth = 15;

          // For Venue Name..........................
          ws.getCell('A1').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 20,
          };
          ws.getCell('A1').alignment = { vertical: 'middle' };
          ws.getCell('A1:U1').fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F08080' },
          };
          ws.mergeCells('A1:U1');
          ws.getCell('A1').value = `${VENUE}`;
          ws.getRow(1).height = 32

          // For DATE of EXPORT.....................
          ws.getCell('A2:U2').fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FDE584' },
          };
          ws.mergeCells('A2:U2');
          ws.getCell('B2').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 14,
          };
          ws.getCell('B2').value = "DATE: " + `${today}`;

          // For Static Line of support EMAIL................
          ws.getCell('A3').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 14,
          };
          ws.mergeCells('A3:U3');
          ws.getCell('A3').value = "For any queries please email <NAME_EMAIL>";

          // For static line of not for payouts....................
          ws.getCell('A4').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 14,
          };
          ws.mergeCells('A4:U4');
          ws.getCell('A4').value = "You can also login to your MyTab Venue account under Order History at anytime to view the below itemised stock sales.";

          // For Static MYTAB line......................
          ws.getCell('A7').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 22,
            bold: true,
          };
          ws.mergeCells('A7:U7');
          ws.getCell('A7').value = "MyTab Venue Item Summary Report";
          ws.getRow(7).height = 30

          // DRINKS.........
          ws.getCell('A9').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 16,
          };
          ws.getCell('A9').alignment = { horizontal: 'center', vertical: 'middle' };
          ws.getCell('A9:E9').fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F08080' },
          };
          ws.mergeCells('A9:E9');
          ws.getCell('A9').value = `DRINKS`;
          ws.getRow(1).height = 22
          // Menu Items...........
          ws.getCell('A10').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 10,
            bold: true,
          };
          ws.mergeCells('A10:C10');
          ws.getCell('A10').value = `MENU ITEMS :`;
          // Quantity...........
          ws.getCell('E10').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 10,
            bold: true,
          };
          ws.getCell('E10').value = `Quantity`;
          var nRows = 12
          stockCountMainProductsCat1.forEach((stock, index) => {
            ws.getCell("A" + (nRows + index)).value = stock.name
            ws.getCell("E" + (nRows + index)).value = stock.Quantity
          });
          let lRows = stockCountMainProductsCat1.length + nRows
          var i = lRows + 1
          ws.getCell("A" + i).font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 10,
            bold: true,
          };
          ws.getCell("A" + i).value = `EXTRAS :`;
          var nRow = i + 1
          stockCountExtrasCat1.forEach((stock, newIndex) => {
            ws.getCell(("A") + (nRow + newIndex)).value = stock.extraItem
            ws.getCell(("E") + (nRow + newIndex)).value = stock.Quantity
          });

          //................................... KOUNTA PRODUCTS...............................
          // Whenever a venue will have kounta integrated and have some products with category -1 
          if (stockCountOfProductsFromKounta.length > 0) {
            ws.getCell('N9').font = {
              name: 'Calibri',
              color: { argb: '000000' },
              family: 2,
              size: 16,
              // bold: true,
              // vertAlign: 'subscript',
            };
            ws.getCell('N9').alignment = { horizontal: 'center', vertical: 'middle' };
            ws.getCell('N9:Q9').fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'F08080' },
            };
            ws.mergeCells('N9:Q9');
            ws.getCell('N9').value = `PRODUCTS FROM KOUNTA`;
            ws.getRow(1).height = 22
            // Menu Items...........
            ws.getCell('N10').font = {
              name: 'Calibri',
              color: { argb: '000000' },
              family: 2,
              size: 10,
              bold: true,
            };
            ws.mergeCells('N10:O10');
            ws.getCell('N10').value = `ITEMS :`;
            // Quantity...........
            ws.getCell('Q10').font = {
              name: 'Calibri',
              color: { argb: '000000' },
              family: 2,
              size: 10,
              bold: true,
            }
            ws.getCell('Q10').value = `Quantity`;
            stockCountOfProductsFromKounta.forEach((stock, index) => {
              ws.getCell("N" + (nRows + index)).value = stock.name
              ws.getCell("Q" + (nRows + index)).value = stock.Quantity
            })
            lRows = stockCountOfProductsFromKounta.length + nRows
            var i = lRows + 1
            ws.getCell("N" + i).font = {
              name: 'Calibri',
              color: { argb: '000000' },
              family: 2,
              size: 10,
              bold: true,
            };
            ws.getCell("N" + i).value = `EXTRAS :`;
            var nRow = i + 1
            stockCountOfExtrassFromKounta.forEach((stock, newIndex) => {
              ws.getCell(("N") + (nRow + newIndex)).value = stock.extraItem
              ws.getCell(("Q") + (nRow + newIndex)).value = stock.Quantity
            });
          }

          // FOOD.........
          ws.getCell('H9').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 16,
          };
          ws.getCell('H9').alignment = { horizontal: 'center', vertical: 'middle' };
          ws.getCell('H9:L9').fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F08080' },
          };
          ws.mergeCells('H9:L9');
          ws.getCell('H9').value = `FOOD`;
          ws.getRow(1).height = 22
          // Menu Items...........
          ws.getCell('H10').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 10,
            bold: true,
          };
          ws.mergeCells('H10:J10');
          ws.getCell('J10').value = `MENU ITEMS :`;
          // Quantity...........
          ws.getCell('L10').font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 10,
            bold: true,
          };
          ws.getCell('L10').value = `Quantity`;
          var nRows = 12
          stockCountMainProductsCat2.forEach((stock, index) => {
            ws.getCell("H" + (nRows + index)).value = stock.name
            ws.getCell("L" + (nRows + index)).value = stock.Quantity
          });
          lRows = stockCountMainProductsCat2.length + nRows
          var i = lRows + 1
          ws.getCell("H" + i).font = {
            name: 'Calibri',
            color: { argb: '000000' },
            family: 2,
            size: 10,
            bold: true,
          };
          ws.getCell("H" + i).value = `EXTRAS :`;
          var nRow = i + 1
          stockCountExtrasCat2.forEach((stock, newIndex) => {
            ws.getCell(("H") + (nRow + newIndex)).value = stock.extraItem
            ws.getCell(("L") + (nRow + newIndex)).value = stock.Quantity
          });

          try {

            let f = ''
            if (frequencyConversion[freq.frequency] == 0) {
              f = 'Daily'
            } else if (frequencyConversion[freq.frequency] == 6) {
              f = 'Weekly'
            } else if (frequencyConversion[freq.frequency] == 13) {
              f = 'Fortnightly'
            } else if (frequencyConversion[freq.frequency] == (lastDayOfMonth - 1)) {
              f = 'Monthly'
            }
            let newReportName = venueName.dataValues.restaurantName + '-' + f + ' Report Sheet'
            const sheetsPath = `${__dirname}` + '/../Report_Sheets'
            const data = await workbook.xlsx.writeFile(`${sheetsPath}/${newReportName}.xlsx`)
            let receivers = [emails, secondaryEmails]
            var emailContent =
              '<p><img src="' +
              env.logo +
              '" title="MyTab" style="width: 100px;"/></p><p><strong>Restuarant Name : ' +
              venueName.dataValues.restaurantName +
              '</strong></p><p>Please find the ' + f + ' Excel Sheet, attached below.</p><p>Thanks,<br/>MyTab Team</p>'

            let mailOptions = {
              from: '"MyTab" <' + env.fromEmailAdmin + '>',
              to: receivers,
              subject: 'Your ' + f + ' Report',
              attachments: [
                {
                  path: `${sheetsPath}/${newReportName}.xlsx`
                },
              ],
              html: emailContent
            }

            // Mail Files to associated emails of venue.....
            await common.sendEmail(mailOptions)

            // Delete Files after being mailed....
            if (fs.existsSync(`${sheetsPath}/${newReportName}.xlsx`)) {
              fs.unlink(`${sheetsPath}/${newReportName}.xlsx`, function (err) {
                if (err) {
                  throw err;
                } else {
                  console.log('Successfully deleted the file');
                }
              })
            }
          } catch (err) {
            console.log(err)
          }
        };

        exportXLSX();

        // mailContent.push({ [emails]: exportDetail })
      }
    }
    // console.log(mailContent)
  })
}
// Cron Job to mail the export sheet to the Venue Owner as per the selected time-duration.....
// new CronJob('59 23 * * *', exportCSVtoVenue, null, true, 'Australia/Perth');

// Add New Printer...
exports.addNewPrinter = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID

  try {
    var subIds = JSON.parse(req.body.subCategoryID)
    var arrPrinterSubIds = []
    printer.findAll({
      where: { barID: barID },
      attributes: [
        'printerName',
        'printerModel',
        'networkAddress',
        'status',
        'createdAt',
        'updatedAt',
      ],
      // raw: true
    }).then(async result => {
      let failure = false
      if (result != null) {
        result.forEach(element => {
          if (req.body.networkAddress == element.networkAddress || req.body.printerName == element.printerName) {
            failure = true
          }
        });
      }
      if (failure == false) {
        printer.create({
          barID: barID,
          printerName: req.body.printerName,
          printerModel: req.body.printerModel,
          networkAddress: req.body.networkAddress,
          status: req.body.status,
          createdAt: new Date(),
        })
          .then(async response => {
            var ids = []
            for (const ele of subIds) {
              ids = {
                printerConnectID: response.dataValues.id,
                subCategoryID: ele
              }
              arrPrinterSubIds.push(ids)
            }
            printerSubIds.bulkCreate(arrPrinterSubIds)
            res.status(200).send({
              success: 1,
              data: response,
              message: 'Printer added successfully'
            })
          })
          .catch(function (err) {
            console.log(err)
          })
      } else {
        res.status(200).send({
          success: 0,
          data: [],
          message: 'A printer with the same name or network address already exists.'
        })
      }
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error!'
    })
  }
}

// Edit exisiting Printer...
exports.editPrinter = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID

  try {
    if (req.body.id) {
      var subIds = JSON.parse(req.body.subCategoryID)

      printer.findAll({
        where: { barID: barID },
        attributes: [
          'id',
          'barID',
          'printerName',
          'printerModel',
          'networkAddress',
          'status',
          'createdAt',
          'updatedAt'
        ],
      }).then(async result => {
        failure = false
        if (result != null) {
          result.forEach(element => {
            if (req.body.networkAddress == element.networkAddress && req.body.id != element.id) {
              failure = true
            }
            if (req.body.printerName == element.printerName && req.body.id != element.id) {
              failure = true
            }
          });
        }
        if (failure == false) {
          printer.update(
            {
              printerName: req.body.printerName,
              printerModel: req.body.printerModel,
              networkAddress: req.body.networkAddress,
              status: req.body.status,
              updatedAt: new Date(),
            },
            { where: { id: req.body.id } }
          )
            .then(async response => {
              if (subIds.length > 0) {
                let arrPrinterSubIds = [];
                await printerSubIds.destroy({ where: { printerConnectID: req.body.id } })
                var ids = []
                for (const ele of subIds) {
                  ids = {
                    printerConnectID: req.body.id,
                    subCategoryID: ele
                  }
                  arrPrinterSubIds.push(ids)
                }
                await printerSubIds.bulkCreate(arrPrinterSubIds)
              } else {
                await printerSubIds.destroy({ where: { printerConnectID: req.body.id } })
              }
              if (req.body.status == '1') {
                const activePrinters = await barAccessToken.findOne({
                  where: {
                    accessToken: req.headers.accesstoken,
                    barID: barID
                  }
                })
                if (activePrinters.activePrintersIDs != null) {
                  const activePrintersIDs = activePrinters.activePrintersIDs.split(',')
                  const found = activePrintersIDs.filter(id => id == req.body.id)
                  if (!found.length > 0) {
                    activePrintersIDs.push(req.body.id);
                    await barAccessToken.update({
                      activePrintersIDs: activePrintersIDs.join()
                    }, {
                      where: {
                        accessToken: req.headers.accesstoken,
                        barID: barID
                      }
                    });
                  }
                } else {
                  await barAccessToken.update({
                    activePrintersIDs: req.body.id
                  }, {
                    where: {
                      accessToken: req.headers.accesstoken,
                      barID: barID
                    }
                  });
                }
              } else {
                const activePrinters = await barAccessToken.findOne({
                  where: {
                    accessToken: req.headers.accesstoken,
                    barID: barID
                  }
                })
                if (activePrinters.activePrintersIDs != null) {
                  const activePrintersIDs = activePrinters.activePrintersIDs.split(',')
                  const found = activePrintersIDs.filter(id => id == req.body.id)
                  if (found.length > 0) {
                    await barAccessToken.update({
                      activePrintersIDs: activePrintersIDs.filter(id => id != req.body.id).join() != '' ? activePrintersIDs.filter(id => id != req.body.id).join() : null
                    }, {
                      where: {
                        accessToken: req.headers.accesstoken,
                        barID: barID
                      }
                    });
                  }
                }
              }
              res.status(200).send({
                success: 1,
                data: response,
                message: 'Printer updated successfully'
              })
            })
            .catch(function (err) {
              console.log(err)
            })
        } else {
          res.status(200).send({
            success: 0,
            message: 'A printer with the same name or network address already exists.'
          })
        }
      })
    }
    else {
      res.status(200).json({
        success: 0,
        message: "No printer found."
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error!'
    })
  }
}

exports.listPrinters = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID;
  try {
    printer.findAll({
      where: { barID: barID },
      attributes: [
        'id',
        'printerName',
        'printerModel',
        'networkAddress',
      ],
      include: [{
        model: printerSubIds,
        include: [{
          model: sub_category,
          attributes: ['name'],
        }]
      }],
      order: [['id', 'DESC']],
    }).then(async response => {
      if (response != 0) {
        const activePrinters = await barAccessToken.findOne({
          where: {
            accessToken: req.headers.accesstoken,
            barID: barID
          }
        })
        let activePrintersArr = []
        if (activePrinters.activePrintersIDs != null) {
          activePrintersArr = activePrinters.activePrintersIDs.split(',')
        }
        response.map(printer => {
          if (activePrintersArr.length > 0) {
            const found = activePrintersArr.filter(id => id == printer.id)
            if (found.length > 0) {
              printer.dataValues.status = '1'
            } else {
              printer.dataValues.status = '0'
            }
          } else {
            printer.dataValues.status = '0'
          }
          return printer
        })
        res.status(200).send({
          success: 1,
          message: 'All printers retrieved successfully!',
          data: response
        })
      } else {
        res.status(200).send({
          success: 0,
          message: 'No printers found for the Venue!',
          data: {}
        })
      }
    })
      .catch(function (err) {
        console.log(err)
      })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'Error in retrieving printers.'
    })
  }
}

exports.deletePrinter = async (req, res) => {
  var id = req.body.id
  try {
    if (id) {
      printer
        .destroy(/*{ isDeleted: 'Yes', updatedAt: new Date() },*/ { where: { id: id } })
      printerSubIds.destroy({ where: { printerConnectID: id } })
        .then(async function () {
          res.json({
            success: 1,
            message: 'Printer Deleted Successfully.'
          })
        }).error(function (err) {
          res.status(200).json({
            success: 0,
            message: err.message,
          })
        })
    } else {
      res.status(200).json({
        success: 0,
        message: "No Printer Found."
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error in deleting record!'
    })
  }
}

// Automated Function to charge venue's for Docket Subscription.......
const docketSubscription = async () => {

  await bar.findAll({
    where: {
      [Op.and]: [{ stripeID: { [Op.ne]: null } },
      { stripeID: { [Op.ne]: "" } },
      { accountVerified: 'Approved' },
      { status: 'Active' },
      { isDeleted: 'No' },
      { docketSubscribed: '1' }]
    },
    attributes: ['stripeID', 'id', 'restaurantName'],
    raw: true,
  }).then(async results => {
    for (const result of results) {
      try {
        await stripe.charges.create({
          amount: 995,
          currency: "aud",
          source: result.stripeID,
          description: "Charge for Docket Subscription -- " + result.restaurantName,
        }, (err, charge) => {
          console.log(err, charge)
          if (charge) {
            docketSubscribe.create({
              barID: result.id,
              createdAt: new Date(),
              subscriptionTime: new Date()
            })
          }
        })
      } catch (e) {
        console.log(e)
      }
    }
  })
}
// Cron Job for charging every week to venue for docket......
// new CronJob('00 00 * * 4', docketSubscription, null, true, 'Australia/Perth');

// 1. & 2. New Taxes Page under Settings.... Starts
// Add New Tax...
exports.addTax = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID

  try {
    const requestOperatingTax = req.body.operating_bar_tax;
    // if (requestOperatingTax.length !== 7) {
    //   return res.status(200)
    //     .send({
    //       success: 0,
    //       message: "You'll need to fill all the week day's data to proceed.",
    //       data: {}
    //     })
    // }
    // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
    // var productIds = JSON.parse(req.body.productID)
    // var arrTaxProductIds = []
    // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.
    tax.findOne({
      where: { barID: barID, name: req.body.name, status: 'Active' },
      attributes: [
        'id',
        'name',
        'percentage',
        'createdAt',
        'updatedAt'
      ]
      // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
      // include: [{
      //   model: taxProductIds,
      //   where: { productID: { [Op.in]: productIds } },
      // }],
      // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.
    }).then(async result => {
      if (!result) {
        tax.create({
          barID: barID,
          name: req.body.name,
          percentage: req.body.percentage,
          status: req.body.status,
          createdAt: new Date(),
        })
          .then(async response => {
            // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
            // var ids = []
            // for (const ele of productIds) {
            //   ids = {
            //     taxID: response.dataValues.id,
            //     productID: ele
            //   }
            //   arrTaxProductIds.push(ids)
            // }
            // taxProductIds.bulkCreate(arrTaxProductIds)
            // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.
            if (requestOperatingTax.length == 7) {
              requestOperatingTax.map(opHours => {
                operatingBarTax.create({
                  weekDay: opHours.weekDay,
                  isClosed: opHours.isClosed,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  taxID: response.dataValues.id,
                })
              });
            }

            res.status(200).send({
              success: 1,
              data: response,
              message: 'Surcharge added successfully.'
            })
          })
          .catch(function (err) {
            console.log(err)
          })
      } else {
        res.status(200).send({
          success: 0,
          message: 'Surcharge with same name is already in use.'
        })
      }
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error!'
    })
  }
}

// Edit exisiting Tax...
exports.editTax = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID
  var taxId = req.body.id

  try {
    if (taxId) {
      // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
      // var productIds = JSON.parse(req.body.productID)
      // var arrTaxProductIds = []
      // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.
      const requestOperatingTax = req.body.operating_bar_tax;

      tax.findOne({
        where: { barID: barID, id: taxId },
        attributes: [
          'id',
          'name',
          'percentage',
        ],
      }).then(async result => {
        if (result) {
          tax.findAll({
            where: { barID: barID, id: { [Op.not]: taxId }, name: req.body.name },
          }).then(async taxRecord => {
            if (taxRecord.length == 0) {
              result.update(
                {
                  barID: barID,
                  name: req.body.name,
                  percentage: req.body.percentage,
                  status: req.body.status,
                  updatedAt: new Date()
                },
                {
                  where: { id: taxId },
                  returning: true,
                }
              )
                .then(async response => {
                  // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
                  // taxProductIds.destroy({ where: { taxID: req.body.id } })
                  // var ids = []
                  // for (const ele of productIds) {
                  //   ids = {
                  //     taxID: req.body.id,
                  //     productID: ele
                  //   }
                  //   arrTaxProductIds.push(ids)
                  // }
                  // taxProductIds.bulkCreate(arrTaxProductIds)
                  // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts ends.
                  if (requestOperatingTax.length == 7) {
                    await operatingBarTax
                      .destroy({
                        where: {
                          taxID: req.body.id,
                        }
                      });
                    requestOperatingTax.map(opHours => {
                      operatingBarTax.create({
                        isClosed: opHours.isClosed,
                        weekDay: opHours.weekDay,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        taxID: req.body.id,
                      })
                    });
                  }

                  res.status(200).send({
                    success: 1,
                    data: response,
                    message: 'Surcharge updated successfully'
                  })
                })
                .catch(function (err) {
                  console.log(err)
                })
            } else {
              res.status(200).send({
                success: 0,
                message: 'Surcharge with same name is already in use.'
              })
            }
          })
        } else {
          res.status(200).send({
            success: 0,
            message: 'Error in retrieving surcharge.'
          })
        }
      })
    } else {
      res.status(200).json({
        success: 0,
        message: "No surcharge found."
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error!'
    })
  }
}

// List the different Taxes...
exports.listTax = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const barID = sessionData.barID;

  try {
    tax.findAll({
      where: { barID: barID },
      include: [
        {
          model: operatingBarTax,
          required: false,
        }
      ],
      // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly starts.
      // include: [{
      //   model: taxProductIds,
      //   include: [{
      //     model: productModel,
      //     attributes: ['id', 'name'],
      //   }]
      // }],
      // -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly ends.
      order: ['name'],
    }).then(async result => {
      if (result != 0) {
        res.status(200).send({
          success: 1,
          message: 'All surcharges retrieved successfully.',
          data: result
        })
      } else {
        res.status(200).send({
          success: 0,
          message: 'No surcharges found for the Venue.',
          data: {}
        })
      }
    })
      .catch(function (err) {
        console.log(err)
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error in retrieving surcharges.'
    })
  }
}

// Delete a Tax from Venue...
exports.deleteTax = async (req, res) => {
  var taxId = req.body.id
  try {
    if (taxId) {
      tax
        .destroy({ where: { id: taxId } })
        // taxProductIds.destroy({ where: { taxID: taxId } }) -> If Client wants to change the ability to have tax on a specific product or subcategory then change it accordingly. 
        .then(async function () {
          res.json({
            success: 1,
            message: 'Surcharge deleted successfully.'
          })
        }).error(function (err) {
          res.status(200).json({
            success: 0,
            message: err.message,
          })
        })
    } else {
      res.status(200).json({
        success: 0,
        message: "No Surcharge Found."
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'Error in deleting surcharge!'
    })
  }
}
// 1. & 2. New Taxes Page under Settings.... Ends

// Api for adding wait time for item's Active & Inactive Hours....
// exports.addWaitTimeForExistingActiveHours = async (req, res, next) => {
//   try {
//     var subCategoryData;
//     await itemActiveHours.findAll()
//       .then(async result => {
//         if(result){
//           result.forEach(async element => {
//             // Check if time eixsits or not
//             var subCategoryWaitTimeExists = await subCategoryWaitTime.count({where:{itemActiveHoursID: element.dataValues.id}})
//             if(subCategoryWaitTimeExists <= 0){
//               var acHours = moment(`${element.dataValues.activeHours}`, 'HH:mm:ss');
//               var inAcHours = moment(`${element.dataValues.inActiveHours}`, 'HH:mm:ss');
//               subCategoryData = commonFunction.timeChunkArray(acHours, inAcHours, '60')
//               let subCategoryWaitTimeArray = subCategoryData && subCategoryData.map((item) => ({
//                   ...item,
//                   barID: element.dataValues.barID,
//                   subCategoryID: element.dataValues.subCategoryID,
//                   itemActiveHoursID: element.dataValues.id,
//                   weekDay: element.dataValues.weekDay,
//                   waitTime: '00:10:00' //default 10 minutes 
//                 }));
//               if(subCategoryWaitTimeArray){
//                 await subCategoryWaitTime.bulkCreate(subCategoryWaitTimeArray)
//                   .catch(function (err) {
//                     console.log(err)
//                   });
//               }
//             }
//           })
//         }
//       })
//       return res.status(200).send({
//         success: 1,
//         message: 'Wait Time added successfully'
//       })
//   } catch (error) {
//     console.log(error)
//     res.status(200).send({
//       success: 0,
//       message: 'error!'
//     })
//   }
// }

exports.setServiceType = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID;
  try {
    bar
      .findAll({
        attributes: [
          'id',
          'email',
          'password',
          'status',
          'editorFlag',
          'businessRegisterId',
          'serviceType'
        ],
        include: [{
          model: productModel,
          required: false,
          where: {
            isDeleted: 'No',
            isUpdateByUser: 'No',
          },
          include: [{
            model: productVariantTypes,
            as: 'productVariantTypes',
            required: false,
            where: {
              isDeleted: 'No',
              isUpdateByUser: 'No',
            }
          }]
        }],
        where: {
          isDeleted: 'No'
        }
      })
      .then(async barData => {
        if (barData.length > 0) {
          barData.forEach(element => {
            if (element.products.length > 0) {
              for (var productDetail of element.products) {
                for (let productExtraDetail of productDetail.productVariantTypes) {
                  productExtraDetail.update({ serviceType: element.dataValues.serviceType });
                }
                productDetail.update({ serviceType: element.dataValues.serviceType });
              }
            }
          });
          res.status(200).send({
            success: 1,
            data: barData,
            message: 'success!'
          })
        } else {
          res.status(200).send({
            success: 0,
            message: message.bar.notFound
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.pauseOrder = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID;
  let pauseOrderStatus = req.body.pauseOrderStatus;
  try {
    bar
      .findOne({
        where: {
          id: barID,
          isDeleted: 'No'
        }
      })
      .then(async barData => {
        if (barData) {
          if (pauseOrderStatus == '1') {
            let pauseTime = req.body.pauseTime;
            barData.update({ pauseOrderStartTime: moment.utc(), pauseOrderLimit: pauseTime });
            res.status(200).send({
              success: 1,
              message: 'Pause order service started.'
            })
          } else {
            barData.update({ pauseOrderStartTime: null, pauseOrderLimit: null });
            res.status(200).send({
              success: 1,
              message: 'Pause order service ended.'
            })
          }
        } else {
          res.status(200).send({
            success: 0,
            message: message.bar.notFound
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.pauseOrderStatus = async (req, res) => {
  let barID = req.body.barID
  try {
    bar
      .findOne({
        where: {
          id: barID,
          isDeleted: 'No'
        }
      })
      .then(async barData => {
        if (barData) {
          let pauseStartTime = null;
          let pauseEndTime = null;
          let pauseOrderStatus = 0;
          if (barData.dataValues.pauseOrderStartTime) {
            let endTime = moment.utc(barData.dataValues.pauseOrderStartTime).add(barData.dataValues.pauseOrderLimit, 'minutes');
            if (endTime.isAfter(moment.utc())) {
              pauseStartTime = barData.dataValues.pauseOrderStartTime;
              pauseEndTime = endTime;
              pauseOrderStatus = 1;
            }
          }
          res.status(200).send({
            success: 1,
            data: {
              pauseOrderStatus, pauseStartTime, pauseEndTime
            },
            message: 'Pause order service data.'
          })
        } else {
          res.status(200).send({
            success: 0,
            message: message.bar.notFound
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}