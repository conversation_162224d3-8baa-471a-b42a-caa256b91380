const Sequelize = require('sequelize')
const bar = require('./bar')

const user_consent_venue = sequelize.define(
  'user_consent_venue',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    userID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "user",
        key: "id"
      }
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

user_consent_venue.belongsTo(bar, { foreignKey: 'barID' })
bar.hasMany(user_consent_venue, { foreignKey: 'barID' })
module.exports = user_consent_venue