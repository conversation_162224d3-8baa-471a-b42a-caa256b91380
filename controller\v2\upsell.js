var Sequelize = require('sequelize')
const Op = Sequelize.Op
const moment = require('moment')
var env = require('../../config/environment')
const subCategoryModel = require("../../models/subCategory")
const barCategorySequence = require("../../models/barCategorySequence")
const subCategoryUpsellModel = require("../../models/subCategoryUpsell")
const message = require('../../config/message')
const bar = require('../../models/bar')

exports.addVenueSubCategoryUpsell = async (req, res) => {
  try {
    var barID = res.locals.barID

    const {
      parent_sub_category_id: parentSubCategoryID,
      child_sub_category_id: childSubCategoryID
    } = req.body || {};

    if (!parentSubCategoryID || !childSubCategoryID) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryIdRequired
      })
    }

    if (parentSubCategoryID == childSubCategoryID) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryIdSame
      })
    }

    const [subCategoriesExist, isEntryExist] = await Promise.all([
      subCategoryModel.count({
        where: {
          id: [parentSubCategoryID, childSubCategoryID]
        }
      }),
      subCategoryUpsellModel.count({
        where: {
          barID,
          parentSubCategoryID
        }
      })
    ]);

    if (subCategoriesExist !== 2) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryNotFound
      })
    }

    if (isEntryExist) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryLinkExist
      })
    }

    const data = await subCategoryUpsellModel.create({
      barID,
      parentSubCategoryID,
      childSubCategoryID
    });

    return res.status(200).send({
      success: 1,
      data: data,
      message: message.product.subCategoryLinkAdded
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateVenueSubCategoryUpsell = async (req, res) => {
  try {
    var barID = res.locals.barID

    const {
      parent_sub_category_id: parentSubCategoryID,
      child_sub_category_id: childSubCategoryID
    } = req.body || {};

    if (!parentSubCategoryID || !childSubCategoryID) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryIdRequired
      })
    }

    if (parentSubCategoryID == childSubCategoryID) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryIdSame
      })
    }

    const [subCategoriesExist, isEntryExist] = await Promise.all([
      subCategoryModel.count({
        where: {
          id: [parentSubCategoryID, childSubCategoryID]
        }
      }),
      subCategoryUpsellModel.count({
        where: {
          barID,
          parentSubCategoryID
        }
      })
    ]);

    if (subCategoriesExist !== 2) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryNotFound
      })
    }

    if (!isEntryExist) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryLinkNotFound
      })
    }

    await subCategoryUpsellModel.update(
      {
        childSubCategoryID
      },
      { where: { barID, parentSubCategoryID } }
    );

    return res.status(200).send({
      success: 1,
      message: message.product.subCategoryLinkUpdated
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.deleteVenueSubCategoryUpsell = async (req, res) => {
  try {
    var barID = res.locals.barID

    const {
      parent_sub_category_id: parentSubCategoryID,
    } = req.body || {};

    if (!parentSubCategoryID) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryIdRequired
      })
    }

    const [subCategoriesExist, isEntryExist] = await Promise.all([
      subCategoryModel.count({
        where: {
          id: parentSubCategoryID
        }
      }),
      subCategoryUpsellModel.count({
        where: {
          barID,
          parentSubCategoryID
        }
      })
    ]);

    if (!subCategoriesExist) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryNotFound
      })
    }

    if (!isEntryExist) {
      return res.status(200).send({
        success: 0,
        message: message.product.subCategoryLinkNotFound
      })
    }

    await subCategoryUpsellModel.destroy({
      where: {
        barID,
        parentSubCategoryID
      }
    });

    return res.status(200).send({
      success: 1,
      message: message.product.subCategoryUnLinked
    })

  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getVenueSubCategoryUpsell = async (req, res) => {
  try {
    var barID = res.locals.barID

    const barData = await bar.findOne({
      attributes: ['id', 'restaurantName', 'posStatus'],
      where: {
        id: barID
      }
    });

    if (
      barData.dataValues.posStatus &&
      barData.dataValues.posStatus === '1'
    ) {
      var whereClause = {
        categoryID: '-1',
        isDeleted: 'No'
      };
    } else {
      var whereClause = {
        [Op.not]: [{ categoryID: '-1' }],
        isDeleted: 'No'
      };
    }

    let data = await subCategoryModel.findAll({
      attributes: ['id', 'categoryID', 'name'],
      include: [
        {
          model: barCategorySequence,
          as: 'bar_category_sequence',
          required: false,
          attributes: [
            [
              Sequelize.fn(
                'coalesce',
                Sequelize.col('subCategorySequence'),
                1000000000000
              ),
              'subCategorySequence'
            ]
          ],
          where: { barId: barID }
        },
        {
          model: subCategoryUpsellModel,
          as: 'childSubCategoryLink',
          required: false,
          attributes: ['childSubCategoryID'],
          include: {
            model: subCategoryModel,
            as: 'subCategory',
            attributes: ['name']
          },
          where: { barID }
        },
      ],
      order: [
        [Sequelize.literal('`bar_category_sequence.subCategorySequence`')],
        'id'
      ],
      where: {
        ...whereClause,
        [Op.and]: [
          Sequelize.literal(`(
						SELECT COUNT(product.id) 
						FROM product 
						WHERE product.subCategoryID = sub_category.id 
						  AND product.isDeleted = "No" 
						  AND product.status = "Active" 
						  AND product.barID = ${barID}
					  ) > 0`)
        ]
      }
    });

    if (data.length > 0) {
      return res.status(200).send({
        success: 1,
        data: data,
        message: message.product.listFetched
      })
    } else {
      return res.status(200).send({
        success: 0,
        data: {},
        message: message.product.somethingWentWrong
      })
    }

  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}



