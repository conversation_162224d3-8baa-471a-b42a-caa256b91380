var Sequelize = require('sequelize')
const foodOptions = require("./foodOptions")
const product = require("./product")

var productFoodOptions = sequelize.define(
  'product_food_options',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    foodOptionID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "food_options",
        key: "id"
      }
    },
    productID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "product",
        key: "id"
      }
    },
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

productFoodOptions.belongsTo(foodOptions, { foreignKey: 'foodOptionID', as: 'foodOptions' })

module.exports = productFoodOptions