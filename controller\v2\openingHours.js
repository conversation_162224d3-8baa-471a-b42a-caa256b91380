var Sequelize = require('sequelize')
const Op = Sequelize.Op
const moment = require('moment')
var env = require('../../config/environment')
const message = require('../../config/message')
const subCategory = require('../../models/subCategory')
const bar = require('../../models/bar')
const barCategorySequence = require('../../models/barCategorySequence')
const barOpeningHours = require('../../models/barOpeningHours')
const barOpeningHoursUTC = require('../../models/barOpeningHoursUTC')
const barSubCategoryOpeningHours = require('../../models/barSubCategoryOpeningHours')
const barSubCategoryOpeningHoursUTC = require('../../models/barSubCategoryOpeningHoursUTC')
const barSubCategoryWaitTime = require('../../models/barSubCategoryWaitTime')
const barSubCategoryWaitTimeUTC = require('../../models/barSubCategoryWaitTimeUTC')
const { convertOpeningHours, timeChunkArray } = require('../../common/commonFunction')

exports.getVenueOpeningHours = async (req, res) => {
  try {
    var barID = res.locals.barID;

    const barDetails = await bar.findOne({
      where: { isDeleted: 'No', id: barID }
    });

    if (!barDetails) {
      return res.status(404).json({ message: 'Bar not found' });
    }

    let whereClause = [{ isDeleted: 'No' }];

    if (barDetails.posStatus === '1') {
      whereClause.push({ categoryID: '-1' });
    } else {
      whereClause.push({ categoryID: { [Op.ne]: '-1' } });
    }

    const subCategoryList = await subCategory.findAll({
      attributes: [
        ['id', 'subCategoryID'],
        'name',
        'id',
        'categoryID',
        [
          Sequelize.literal(
            `(SELECT COUNT(*) FROM product WHERE product.barID = ${barID} AND isDeleted = 'no' AND subCategoryID = sub_category.id LIMIT 1)`
          ),
          'productCount'
        ]
      ],
      where: whereClause,
      include: [
        {
          model: barCategorySequence,
          as: 'bar_category_sequence',
          required: false,
          attributes: [
            [
              Sequelize.fn(
                'coalesce',
                Sequelize.col('subCategorySequence'),
                1000000000000
              ),
              'subCategorySequence'
            ]
          ],
          where: { barId: barID }
        }
      ],
      order: [
        [Sequelize.literal('bar_category_sequence.subCategorySequence')],
        ['id']
      ]
    });

    const filteredSubCategoryIDs = subCategoryList
      .filter((category) => category.dataValues.productCount > 0)
      .map((category) => category.dataValues.subCategoryID);

    const subCategoryNames = subCategoryList.reduce((acc, category) => {
      acc[category.dataValues.subCategoryID] = category.dataValues.name;
      return acc;
    }, {});

    const barFetchResponse = await barOpeningHours.findAll({
      attributes: [
        'weekDay',
        [
          Sequelize.fn('date_format', Sequelize.col('openingHours'), '%H:%i'),
          'openingHours'
        ],
        [
          Sequelize.fn('date_format', Sequelize.col('closingHours'), '%H:%i'),
          'closingHours'
        ],
        [Sequelize.literal(`IF(isClosed=1, '1', '0')`), 'isClosed']
      ],
      where: { barID },
      order: [['weekDay'], ['openingHours']]
    });

    const subCategoryFetchResponse =
      await barSubCategoryOpeningHours.findAll({
        attributes: [
          'weekDay',
          'subCategoryID',
          [
            Sequelize.fn(
              'date_format',
              Sequelize.col('openingHours'),
              '%H:%i'
            ),
            'openingHours'
          ],
          [
            Sequelize.fn(
              'date_format',
              Sequelize.col('closingHours'),
              '%H:%i'
            ),
            'closingHours'
          ],
          [Sequelize.literal(`IF(isClosed=1, '1', '0')`), 'isClosed']
        ],
        where: {
          barID,
          subCategoryID: { [Op.in]: filteredSubCategoryIDs }
        },
        order: [['weekDay'], ['openingHours']]
      });

    if (!barFetchResponse.length && !subCategoryFetchResponse.length) {
      return res.status(200).send({
        success: 1,
        data: {
          matchCategoryOpeningHours: barDetails.matchCategoryOpeningHours,
          venueOpeningHours: [],
          subCategoryOpeningHours: []
        },
        message: message.openingHours.listFetched
      });
    }

    const groupedData = new Map();

    for (const {
      weekDay,
      openingHours,
      closingHours,
      isClosed
    } of barFetchResponse) {
      if (!groupedData.has(weekDay)) {
        groupedData.set(weekDay, { weekDay, isClosed, timeSlots: [] });
      }
      groupedData.get(weekDay).timeSlots.push({ openingHours, closingHours });
    }

    const subCategoryGroupedData = new Map();

    for (const {
      weekDay,
      subCategoryID,
      openingHours,
      closingHours,
      isClosed
    } of subCategoryFetchResponse) {
      if (!subCategoryGroupedData.has(subCategoryID)) {
        subCategoryGroupedData.set(subCategoryID, {
          subCategoryID,
          subCategoryName: subCategoryNames[subCategoryID],
          weekDays: new Map()
        });
      }

      const weekDayMap = subCategoryGroupedData.get(subCategoryID).weekDays;

      if (!weekDayMap.has(weekDay)) {
        weekDayMap.set(weekDay, { weekDay, isClosed, timeSlots: [] });
      }

      weekDayMap.get(weekDay).timeSlots.push({ openingHours, closingHours });
    }

    const formattedSubCategoryGroupedData = Array.from(
      subCategoryGroupedData.values()
    ).map((subCategory) => ({
      subCategoryID: subCategory.subCategoryID,
      subCategoryName: subCategory.subCategoryName,
      weekDays: Array.from(subCategory.weekDays.values())
    }));

    formattedSubCategoryGroupedData.sort(
      (a, b) =>
        filteredSubCategoryIDs.indexOf(a.subCategoryID) -
        filteredSubCategoryIDs.indexOf(b.subCategoryID)
    );

    return res.status(200).send({
      success: 1,
      data: {
				matchCategoryOpeningHours: barDetails.matchCategoryOpeningHours,
				venueOpeningHours: Array.from(groupedData.values()),
				subCategoryOpeningHours: formattedSubCategoryGroupedData
			},
      message: message.openingHours.listFetched
    })

  } catch (error) {
    console.error(error);
    return res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateVenueOpeningHours = async (req, res) => {
  try {
    const barID = res.locals.barID;
    const {
      matchCategoryOpeningHours,
      venueOpeningHours: updatedOperatingHours
    } = req.body;
    const barDetails = await bar.findByPk(barID);
    const timezone = barDetails.timezone;
    const whereClause =
      barDetails.posStatus === '1'
        ? { categoryID: '-1', isDeleted: 'No' }
        : { categoryID: { [Op.not]: '-1' }, isDeleted: 'No' };
    const subCategories = await subCategory.findAll({
      where: whereClause
    });
    const subCategoryIDs = subCategories.map((sc) => sc.id);
    for (const day of updatedOperatingHours) {
      const { weekDay: weekDayStr, isClosed, timeSlots } = day;
      const localWeekDay = parseInt(weekDayStr, 10);
      const existingRecords = await barOpeningHours.findAll({
        where: { barID, weekDay: localWeekDay }
      });
      const existingData = existingRecords.map((r) => ({
        openingHours: moment(r.openingHours, 'HH:mm:ss').format('HH:mm'),
        closingHours: moment(r.closingHours, 'HH:mm:ss').format('HH:mm'),
        isClosed: r.isClosed ? '1' : '0'
      }));
      const newData = timeSlots.map((s) => ({
        openingHours: s.openingHours,
        closingHours: s.closingHours,
        isClosed
      }));

      const arraysEqual = (arr1, arr2) =>
        arr1.length === arr2.length &&
        arr1.every((item1) =>
          arr2.some(
            (item2) =>
              item1.openingHours === item2.openingHours &&
              item1.closingHours === item2.closingHours &&
              item1.isClosed === item2.isClosed
          )
        );

      if (arraysEqual(existingData, newData)) continue;

      const existingIds = existingRecords.map((r) => r.id);
      if (existingIds.length) {
        if (
          barDetails.matchCategoryOpeningHours == 'Yes' &&
          matchCategoryOpeningHours == 'Yes'
        ) {
          await Promise.all([
            barOpeningHoursUTC.destroy({
              where: { barOpeningHoursID: existingIds }
            }),
            barOpeningHours.destroy({ where: { id: existingIds } })
          ]);

          const existingSubCategoryHours =
            await barSubCategoryOpeningHours.findAll({
              where: {
                barID,
                weekDay: localWeekDay,
                subCategoryID: subCategoryIDs
              }
            });
          const existingSubCategoryHourIDs = existingSubCategoryHours.map(
            (h) => h.id
          );
          await Promise.all([
            barSubCategoryWaitTime.destroy({
              where: {
                barSubCategoryOpeningHoursID: existingSubCategoryHourIDs
              }
            }),
            barSubCategoryWaitTimeUTC.destroy({
              where: {
                barSubCategoryOpeningHoursID: existingSubCategoryHourIDs
              }
            }),
            barSubCategoryOpeningHours.destroy({
              where: { id: existingSubCategoryHourIDs }
            }),
            barSubCategoryOpeningHoursUTC.destroy({
              where: {
                barSubCategoryOpeningHoursID: existingSubCategoryHourIDs
              }
            })
          ]);
        } else {
          await Promise.all([
            barOpeningHoursUTC.destroy({
              where: { barOpeningHoursID: existingIds }
            }),
            barOpeningHours.destroy({ where: { id: existingIds } })
          ]);
        }
      }

      const newBarHours = await barOpeningHours.bulkCreate(
        timeSlots.map((s) => ({
          openingHours: s.openingHours,
          closingHours: s.closingHours,
          weekDay: localWeekDay,
          isClosed: isClosed === '1',
          barID
        })),
        { returning: true }
      );

      const utcEntries = newBarHours.flatMap((r) =>
        convertOpeningHours(r, timezone, localWeekDay).map((e) => ({
          ...e,
          barID,
          isClosed,
          barOpeningHoursID: r.id
        }))
      );
      if (utcEntries.length)
        await barOpeningHoursUTC.bulkCreate(utcEntries);

      if (
        barDetails.matchCategoryOpeningHours == 'Yes' &&
        matchCategoryOpeningHours === 'Yes'
      ) {
        const subCategoryEntries = newBarHours.flatMap((newBarHour) =>
          subCategories.map((sc) => ({
            openingHours: newBarHour.openingHours,
            closingHours: newBarHour.closingHours,
            weekDay: newBarHour.weekDay,
            subCategoryID: sc.id,
            isClosed: newBarHour.isClosed,
            barID
          }))
        );

        if (subCategoryEntries.length) {
          const newSubHours =
            await barSubCategoryOpeningHours.bulkCreate(
              subCategoryEntries,
              { returning: true }
            );

          const utcSubEntries = newSubHours.flatMap((r) =>
            convertOpeningHours(r, timezone, r.weekDay).map((e) => ({
              ...e,
              barID,
              isClosed: r.isClosed,
              subCategoryID: r.subCategoryID,
              barSubCategoryOpeningHoursID: r.id
            }))
          );
          if (utcSubEntries.length)
            await barSubCategoryOpeningHoursUTC.bulkCreate(
              utcSubEntries
            );

          const timeChunkEntries = newSubHours.flatMap((record) =>
            timeChunkArray(
              record.openingHours,
              record.closingHours,
              '60'
            ).map(({ startTime, endTime }) => ({
              barID,
              subCategoryID: record.subCategoryID,
              barSubCategoryOpeningHoursID: record.id,
              weekDay: record.weekDay,
              waitTime: '00:10:00',
              startTime,
              endTime
            }))
          );
          if (timeChunkEntries.length){
            const waitTimeEntries = await barSubCategoryWaitTime.bulkCreate(timeChunkEntries, { returning: true });
            const utcWaitTimeEntries = waitTimeEntries.flatMap(
              ({
                id,
                startTime,
                endTime,
                waitTime,
                weekDay,
                subCategoryID,
                barSubCategoryOpeningHoursID
              }) =>
                convertOpeningHours({openingHours: startTime, closingHours: endTime}, timezone, weekDay).map((data) => ({
                  ...data,
                  barSubCategoryWaitTimeID: id,
                  startTime: data.openingHours,
                  endTime: data.closingHours,
                  waitTime,
                  subCategoryID,
                  barSubCategoryOpeningHoursID,
                  barID,
                }))
            );
            if (utcWaitTimeEntries.length)
              await barSubCategoryWaitTimeUTC.bulkCreate(utcWaitTimeEntries);
          }
        }
      }
    }

    if (
      matchCategoryOpeningHours === 'Yes' &&
      barDetails.matchCategoryOpeningHours !== 'Yes'
    ) {
      await Promise.all([
        barSubCategoryOpeningHours.destroy({
          where: { barID, subCategoryID: subCategoryIDs }
        }),
        barSubCategoryOpeningHoursUTC.destroy({
          where: { barID, subCategoryID: subCategoryIDs }
        }),
        barSubCategoryWaitTime.destroy({
          where: { barID, subCategoryID: subCategoryIDs }
        }),
        barSubCategoryWaitTimeUTC.destroy({
          where: { barID, subCategoryID: subCategoryIDs }
        })
      ]);

      const subCategoryEntries = updatedOperatingHours.flatMap((day) => {
        const localWeekDay = parseInt(day.weekDay, 10);
        return subCategories.flatMap((sc) =>
          day.timeSlots.map((s) => ({
            openingHours: s.openingHours,
            closingHours: s.closingHours,
            weekDay: localWeekDay,
            subCategoryID: sc.id,
            isClosed: day.isClosed === '1',
            barID
          }))
        );
      });

      if (subCategoryEntries.length) {
        const newSubHours = await barSubCategoryOpeningHours.bulkCreate(
          subCategoryEntries,
          { returning: true }
        );

        const utcSubEntries = newSubHours.flatMap((r) =>
          convertOpeningHours(r, timezone, r.weekDay).map((e) => ({
            ...e,
            barID,
            isClosed: r.isClosed,
            subCategoryID: r.subCategoryID,
            barSubCategoryOpeningHoursID: r.id
          }))
        );
        if (utcSubEntries.length)
          await barSubCategoryOpeningHoursUTC.bulkCreate(utcSubEntries);

        const timeChunkEntries = newSubHours.flatMap((record) =>
          timeChunkArray(record.openingHours, record.closingHours, '60').map(
            ({ startTime, endTime }) => ({
              barID,
              subCategoryID: record.subCategoryID,
              barSubCategoryOpeningHoursID: record.id,
              weekDay: record.weekDay,
              waitTime: '00:10:00',
              startTime,
              endTime
            })
          )
        );
        if (timeChunkEntries.length) {
          const waitTimeEntries =
            await barSubCategoryWaitTime.bulkCreate(timeChunkEntries, { returning: true });
          const utcWaitTimeEntries = waitTimeEntries.flatMap(
            ({
              id,
              startTime,
              endTime,
              waitTime,
              weekDay,
              subCategoryID,
              barSubCategoryOpeningHoursID
            }) =>
              convertOpeningHours(
                { openingHours: startTime, closingHours: endTime },
                timezone,
                weekDay
              ).map((data) => ({
                ...data,
                barSubCategoryWaitTimeID: id,
                startTime: data.openingHours,
                endTime: data.closingHours,
                waitTime,
                subCategoryID,
                barSubCategoryOpeningHoursID,
                barID
              }))
          );
          if (utcWaitTimeEntries.length)
            await barSubCategoryWaitTimeUTC.bulkCreate(
              utcWaitTimeEntries
            );
        }
      }
    }

    await bar.update(
      { matchCategoryOpeningHours },
      { where: { id: barID } }
    );

    return res.status(200).send({
      success: 1,
      data: {},
      message: message.openingHours.updated
    });
  } catch (error) {
    console.error(error);
    return res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
};

exports.updateVenueCategoryOpeningHours = async (req, res) => {
  try {
    const barID = res.locals.barID;
    const {
      subCategoryID,
      subCategoryOpeningHours
    } = req.body;

    const barDetails = await bar.findByPk(barID);
    const timezone = barDetails.timezone;

    const venueHours = await barOpeningHours.findAll({
      where: { barID },
      attributes: ['weekDay', 'openingHours', 'closingHours'],
      raw: true
    });

    const venueHoursMap = venueHours.reduce((acc, { weekDay, openingHours, closingHours }) => {
      if (!acc[weekDay]) acc[weekDay] = [];
      acc[weekDay].push({
        start: moment(openingHours, 'HH:mm:ss'),
        end: moment(closingHours, 'HH:mm:ss')
      });
      return acc;
    }, {});

    for (const { weekDay, timeSlots } of subCategoryOpeningHours) {
      
      const venueSlots = venueHoursMap[weekDay];

      const hasInvalidSlot = timeSlots.some(slot => {
        const subCatStart = moment(slot.openingHours, 'HH:mm');
        const subCatEnd = moment(slot.closingHours, 'HH:mm');
        
        return !venueSlots.some(venueSlot => 
          subCatStart.isSameOrAfter(venueSlot.start) && 
          subCatEnd.isSameOrBefore(venueSlot.end)
        );
      });

      if (hasInvalidSlot) {
        return res.status(400).json({
          success: 0,
          message: `The selected time is out of Venue's operating Hours. Please try again.`
        });
      }
    }

    for (const day of subCategoryOpeningHours) {
      const { weekDay: weekDayStr, isClosed, timeSlots } = day;
      const localWeekDay = parseInt(weekDayStr, 10);
      const existingRecords = await barSubCategoryOpeningHours.findAll({
        where: { barID, subCategoryID, weekDay: localWeekDay }
      });
      const existingData = existingRecords.map((record) => ({
        openingHours: moment(record.openingHours, 'HH:mm:ss').format('HH:mm'),
        closingHours: moment(record.closingHours, 'HH:mm:ss').format('HH:mm'),
        subCategoryID,
        isClosed: record.isClosed ? '1' : '0'
      }));
      const newData = timeSlots.map((timeSlot) => ({
        ...timeSlot,
        subCategoryID,
        isClosed
      }));

      const arraysEqual = (arr1, arr2) =>
        arr1.length === arr2.length &&
        arr1.every((item1) =>
          arr2.some(
            (item2) =>
              item1.openingHours === item2.openingHours &&
              item1.closingHours === item2.closingHours &&
              item1.subCategoryID === item2.subCategoryID &&
              item1.isClosed === item2.isClosed
          )
        );

      if (arraysEqual(existingData, newData)) continue;

      const existingIds = existingRecords.map((record) => record.id);
      if (existingIds.length) {
        await Promise.all([
          barSubCategoryOpeningHoursUTC.destroy({
            where: { barSubCategoryOpeningHoursID: existingIds }
          }),
          barSubCategoryOpeningHours.destroy({
            where: { id: existingIds }
          }),
          barSubCategoryWaitTime.destroy({
            where: { barSubCategoryOpeningHoursID: existingIds }
          }),
          barSubCategoryWaitTimeUTC.destroy({
            where: { barSubCategoryOpeningHoursID: existingIds }
          })
        ]);
      }

      const newCategoryHours =
        await barSubCategoryOpeningHours.bulkCreate(
          timeSlots.map(({ openingHours, closingHours }) => ({
            openingHours,
            closingHours,
            subCategoryID,
            weekDay: localWeekDay,
            isClosed: isClosed === '1',
            barID
          })),
          { returning: true }
        );

      const utcEntries = newCategoryHours.flatMap((record) =>
        convertOpeningHours(record, timezone, localWeekDay).map((data) => ({
          ...data,
          barID,
          isClosed,
          subCategoryID,
          barSubCategoryOpeningHoursID: record.id
        }))
      );
      if (utcEntries.length)
        await barSubCategoryOpeningHoursUTC.bulkCreate(utcEntries);

      const timeChunkEntries = newCategoryHours.flatMap((record) => {
        return timeChunkArray(
          record.openingHours,
          record.closingHours,
          '60'
        ).map(({ startTime, endTime }) => ({
          barID,
          subCategoryID,
          barSubCategoryOpeningHoursID: record.id,
          weekDay: record.weekDay,
          waitTime: '00:10:00',
          startTime,
          endTime
        }));
      });

      if (timeChunkEntries.length){
        const waitTimeEntries = await barSubCategoryWaitTime.bulkCreate(timeChunkEntries, { returning: true });
        const utcWaitTimeEntries = waitTimeEntries.flatMap(
          ({
            id,
            startTime,
            endTime,
            waitTime,
            weekDay,
            barSubCategoryOpeningHoursID
          }) =>
            convertOpeningHours({openingHours: startTime, closingHours: endTime}, timezone, weekDay).map((data) => ({
              ...data,
              barSubCategoryWaitTimeID: id,
              startTime: data.openingHours,
              endTime: data.closingHours,
              waitTime,
              subCategoryID,
              barSubCategoryOpeningHoursID,
              barID,
            }))
        );
        if (utcWaitTimeEntries.length)
          await barSubCategoryWaitTimeUTC.bulkCreate(utcWaitTimeEntries);
      }
    }
    return res.status(200).send({
      success: 1,
      data: {},
      message: message.subCategpryOpeningHours.updated
    })
  } catch (error) {
    console.error(error);
    return res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
};