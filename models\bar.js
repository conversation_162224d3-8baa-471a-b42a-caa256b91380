var Sequelize = require('sequelize')

var env = require('../config/environment')
const { s3GetFile } = require('../middleware/awsS3Operations')

var bar = sequelize.define(
  'bar',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    restaurantName: Sequelize.TEXT,
    managerName: Sequelize.TEXT,
    email: Sequelize.TEXT,
    countryCode: Sequelize.TEXT,
    mobile: Sequelize.TEXT,
    password: Sequelize.TEXT,
    address: Sequelize.TEXT,
    latitude: Sequelize.TEXT,
    longitude: Sequelize.TEXT,
    passcode: Sequelize.TEXT,
    passcodeStatus: Sequelize.ENUM('Active', 'Inactive'),
    stripeID: Sequelize.TEXT,
    oneTimePassword: Sequelize.TEXT,
    resetPasswordCode: Sequelize.TEXT,
    avatar: {
      type: Sequelize.TEXT,
      get() {
        if (this.getDataValue('avatar') != '') {
          // const contentValue = s3GetFile(
          //   env.awsBarFolder +
          //   this.getDataValue('avatar')
          // );
          // return process.binding('util').getPromiseDetails(contentValue)[1];
          // console.log(this.getDataValue('avatar'));
          const newURL = env.awsPublicServerURL + env.awsBarFolder + this.getDataValue('avatar')
          // console.log(newURL);
          return newURL;
        } else {
          return '';
        }
      }
    },
    mobileVerified: Sequelize.ENUM('Yes', 'No'),
    badge: Sequelize.INTEGER,
    notification: Sequelize.ENUM('Yes', 'No'),
    accountVerified: Sequelize.ENUM('New', 'Approved', 'Rejected'),
    posStatus: Sequelize.TEXT,
    posFee: Sequelize.TEXT,
    venueId: Sequelize.TEXT,
    isVenueServeAlcohol: Sequelize.ENUM('Yes', 'No'),
    liquorLicenseNumber: Sequelize.TEXT,
    status: Sequelize.ENUM('Active', 'Inactive'),
    waitTimeDrink: Sequelize.INTEGER,
    waitTimeFood: Sequelize.INTEGER,
    serviceType: Sequelize.ENUM('PICKUP', 'TABLE', 'BOTH'),
    waitTimeServiceType: Sequelize.ENUM('PICKUP', 'TABLE', 'BOTH'),
    attachedPosConfig: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "barPOSMigration",
        key: "id"
      }
    },
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No'),
    docketSubscribed: Sequelize.ENUM('0', '1'),
    docketStatus: Sequelize.ENUM('0', '1'),
    docketCommission: Sequelize.DOUBLE,
    businessRegisterId: Sequelize.INTEGER,
    readPopup: Sequelize.ENUM('Yes', 'No'),
    pauseOrderStartTime: Sequelize.DATE,
    pauseOrderLimit: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    matchCategoryOpeningHours: {
      type: Sequelize.ENUM('Yes', 'No'),
      defaultValue: 'Yes'
    },
    timezone: {
      type: Sequelize.STRING,
      defaultValue: 'Australia/Perth'
    }
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

module.exports = bar