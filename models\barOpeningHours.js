const Sequelize = require('sequelize')
const bar = require('./bar')

const barOpeningHours = sequelize.define(
	'bar_opening_hours',
	{
		id: {
			type: Sequelize.BIGINT,
			autoIncrement: true,
			primaryKey: true
		},
		barID: {
			type: Sequelize.INTEGER,
			allowNull: false,
			references: {
				model: "bar",
				key: "id"
			}
		},
		weekDay: Sequelize.SMALLINT,
		openingHours: Sequelize.TIME,
		closingHours: Sequelize.TIME,
		isClosed: Sequelize.BOOLEAN,
		timeZone: { type: Sequelize.STRING, defaultValue: 'Australia/Perth' },
		createdAt: Sequelize.DATE,
		updatedAt: Sequelize.DATE,
	},
	{
		freezeTableName: true,
		timestamps: false
	}
)

barOpeningHours.belongsTo(bar, { foreignKey: 'barID' })
bar.hasMany(barOpeningHours, { foreignKey: 'barID' })

module.exports = barOpeningHours