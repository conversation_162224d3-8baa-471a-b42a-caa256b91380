exports.user = {
  sessionExpired: 'Session Expired, Please re-login to renew your session!',
  notExits: 'User not exists!',
  emailExists: 'This email is linked to an existing account',
  userNameExists: 'Username exists!',
  otpVerified: 'OTP verified successfully!',
  mobileExists: 'This mobile number is linked to an existing account',
  mobileVerified: 'Mobile verified successfully!',
  otpVerificationFailed: 'Please enter the correct verification code',
  passwordUpdated: 'Your password is changed. Please login again',
  mobileUpdated: 'Your mobile has been changed!',
  xapiNotMatched: 'xapiKey not matched!',
  enterValidCredential: 'Please enter valid login credentials',
  userInactive: 'You have been suspended please contact admin for further information.',
  userDeleted: 'This email is already registered with app, Try again with a different email address',
  userNotFound: 'userNotFound!',
  userProfileUpdated: 'Your profile has been updated!',
  userDeviceTokenUpdated: 'Your device token has been updated!',
  oldPasswordWrong: 'Your old Password is wrong. please try again!',
  logOut: 'You have successfully logged out!',
  emailDoesNotExists: '<PERSON><PERSON> does not exists!',
  alreadyViewPopup: 'This user has already view the popup message.',
  messageRead: 'Popup message ready successfully.',
}

exports.bar = {
  sessionExpired: 'Session Expired, Please re-login to renew your session!',
  emailExists: 'This email is linked to an existing account',
  emailNotExits: 'Email not exists!',
  enterValidCredential: 'Please enter valid login credentials',
  barInactive: 'Your account is inactive. Please contact to administrator!',
  mobileVerified: 'Mobile verified successfully!',
  notFound: 'Bar not found!',
  productTaxNotFound: "Product tax not found.",
  alreadyViewPopup: 'This venue has already view the popup message.',
  messageRead: 'Popup message ready successfully.',
  productTaxFetched: "Product tax fetched successfully.",
  accountDeleteFormFetched: "Account delete form fetched successfully."
}

exports.landing = {
  xapiNotMatched: 'xapiKey not matched!',
  memberNumberExists: 'Member Number exists!',
  emailNotRegistered: 'There is no account registered to this email',
  logOut: 'You have successfully logged out!',
  activeAccout: 'Please contact admin to activate your account!'
}

exports.openingHours = {
  notFound: 'No data found',
  listFetched:"Opening hours fetch successfully.",
  updated:"Opening hours updated successfully.",
}
exports.subCategpryOpeningHours = {
  notFound: 'No data found',
  listFetched:"Menu sub-category hours fetch successfully.",
  updated:"Menu sub-category hours updated successfully.",
}

exports.product = {
  notFound: 'Product not found!',
  listFetched:"List fetch successfully.",
  somethingWentWrong:"We're sorry, something went wrong. Please refresh your screen or contact us for support.",
  subCategoryIdRequired:"Sub category id required.",
  subCategoryIdSame: 'Parent sub category ID and Child sub category ID can not be same.',
  subCategoryNotFound: 'Sub category not found.',
  subCategoryLinkExist: 'This sub category link already exists.',
  subCategoryLinkNotFound: 'This sub category link does not exists.',
  subCategoryLinkAdded: 'Sub category link added successfully.',
  subCategoryLinkUpdated: 'Sub category link updated successfully.',
  subCategoryUnLinked: 'Sub category un-linked successfully.'
}

exports.feed = {
  notFound: 'Feed not found!'
}

exports.category={
  listFetched: "List fetched successfully.",
}