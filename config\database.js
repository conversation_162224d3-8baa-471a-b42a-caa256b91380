// 'use strict'

// var Sequelize = require('sequelize')
// const env = require('./environment')

// global.sequelize = new Sequelize(
//   env.databaseName,
//   env.databaseUserName,
//   env.databasePassword,
//   {
//     host: env.databaseHost,
//     port: env.databasePort,
//     dialect: env.databaseDialect,
//     define: {
//       underscored: true
//     },
//     pool: {
//       max: 10,
//       min: 0,
//       acquire: 300000,
//       idle: 10000
//     },
//     logging: false
//   }
// )

// sequelize
//   .authenticate()
//   .then(function () {
//     console.log('CONNECTION SUCCESSful! ')
//   })
//   .catch(function (err) {
//     console.log(err)
//     console.log(
//       env.databaseHost,
//     )
//     console.log('CONNECTION ERROR--')
//   })
//   .done()

// module.exports = sequelize;
