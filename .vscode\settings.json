{"files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/dist": true, "**/ios/build": true, "**/.jest": true, "**/node_modules": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/dist": true, "**/ios/build": true, "**/.jest": true, "**/coverage": true}, "editor.formatOnSave": true, "prettier.useTabs": false, "prettier.tabWidth": 2, "prettier.jsonEnable": ["json"], "prettier.singleQuote": true, "prettier.semi": false, "prettier.spaceBeforeFunctionParen": true, "prettier.bracketSpacing": false, "prettier.printWidth": 80, "prettier.arrowParens": false, "editor.tabSize": 2, "workbench.colorCustomizations": {"titleBar.activeBackground": "#03c5f5", "titleBar.inactiveBackground": "#03c5f5", "statusBar.background": "#03c5f5", "statusBar.foreground": "#000000"}}