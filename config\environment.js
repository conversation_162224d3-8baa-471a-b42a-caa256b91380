'use strict'

const environment = {
  port: process.env.port,
  databaseName: process.env.databaseName,
  databaseHost: process.env.databaseHost,
  databaseUserName: process.env.databaseUserName,
  databasePassword: process.env.databasePassword,
  databaseDialect: process.env.databaseDialect,
  databasePort: process.env.databasePort,

  ACCESS_TOKEN_SECRET: '58ce6ed8d74bc9d53b9793bb773e452bafea889e9b982e6faac5d32bcc8304d1db2fe913ae9d8d0ad59e652137371c0b892caf6b55dc2abade1a9e4507bbf9c7',
  REFRESH_TOKEN_SECRET: '14cbd7d31ec35aa66b759a000db86905d17d1f16b6c3bff33c8e9ecb2eb1bbf80834ed9946bbcc1fc738a6f19eea470e5f55f1855d17720304bf4c5d136a0feb',
  AWS_SECRET_ID: 'stage/rds/mytab_staging',
  sesEmailHost: 'email-smtp.ap-southeast-2.amazonaws.com',
  sesUser: 'AKIA2DESQUT3BRHD5SP3',
  sesPassword: 'BCe3v5oKpT27eY096385W4VCrH6IoBYlBBdSwqMI8S3y',
  fromEmailVenueAdmin: '<EMAIL>',
  fromEmailAdmin: '<EMAIL>',
  replyEmail: '<EMAIL>',
  customerEmailTo: '<EMAIL>',
  barEmailTo: '<EMAIL>',

  awsS3Arn: process.env.awsS3Arn,
  awsMainBucket: process.env.awsMainBucket,
  awsBucket: process.env.awsBucket,
  awsPublicBucket: process.env.awsPublicBucket,
  awsRegion: process.env.awsRegion,
  awsPrivateBucketCloudFrontURL: process.env.awsPrivateBucketCloudFrontURL,
  awsServerURL: process.env.awsServerURL,
  awsPublicServerURL: process.env.awsPublicServerURL,
  awsAccessKey: process.env.awsAccessKey,
  awsSecretAccessKey: process.env.awsSecretAccessKey,
  awsBarFolder: process.env.awsBarFolder,
  awsUserFolder: process.env.awsUserFolder,
  awsAdsFolder: process.env.awsAdsFolder,
  awsProductFolder: process.env.awsProductFolder,
  awsSignedUrlExpireTime: process.env.awsSignedUrlExpireTime,
  logo: process.env.logo,
  customer_logo: process.env.customer_logo,

  fcm_file: process.env.FCM_FILE,
  firebase_project_id: process.env.firebase_project_id,

  stripe_secret_key: process.env.stripe_secret_key,
  stripe_publishable_key: process.env.stripe_publishable_key,

  API_URL: process.env.API_URL,

  push_ios_keyid: '778HQ6BCC2',
  push_ios_teamid: '9YB3SM7F58',
  push_ios_BundleIDs: 'com.mytab.customer',
  push_ios_venue: 'com.myTab.bar',
  push_production: process.env.push_production,

  DISVAL: '6371', //for km use 6371
  DISTEXT: 'km',
  latitude: '23.034863',
  longitude: '72.585222',

  DOSHI_APP_CLIENT_ID: process.env.DOSHI_APP_CLIENT_ID,
  DOSHI_APP_CLIENT_SECRET: process.env.DOSHI_APP_CLIENT_SECRET,
  DOSHI_APP_BASE_URL: process.env.DOSHI_APP_BASE_URL,

  KOUNTA_API_BASE_URL: 'https://api.kounta.com/v1',
  KOUNTA_AUTHORIZATION_BASE_URL: 'https://my.kounta.com',

  stripeStaticCharge: process.env.stripeStaticCharge,
  stripeDomesticCardCharge: process.env.stripeDomesticCardCharge,
  stripeInternationalCardCharge: process.env.stripeInternationalCardCharge,

}

module.exports = environment
