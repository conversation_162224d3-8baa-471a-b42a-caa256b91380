const express = require('express')
const router = express()
const multer = require('multer')

const checkUserAuth = require('../middleware/checkAuth')
const cart = require('../controller/cart')
const bar = require("../controller/bar");

var upload = multer({})

/*User API*/
// router.post('/add', upload.array(), checkUserAuth, bar.isVenueClosedController, cart.cartItemsActiveOrInactive, cart.add)
router.post('/add', upload.array(), checkUserAuth, bar.isVenueClosedControllerV2, cart.cartItemsActiveOrInactiveV2, cart.add)
router.post('/updateItem', upload.array(), checkUserAuth, cart.updateItem)
router.post('/deleteItem', upload.array(), checkUserAuth, cart.deleteItem)
router.post('/clearCart', upload.array(), checkUserAuth, cart.clearCart)
router.post('/getCartItems', upload.array(), checkUserAuth, cart.getCartItems)

module.exports = router